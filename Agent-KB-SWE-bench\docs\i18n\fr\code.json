{"footer.title": {"message": "OpenHands"}, "footer.docs": {"message": "Documents"}, "footer.community": {"message": "Communauté"}, "footer.copyright": {"message": "© {year} OpenHands"}, "faq.title": {"message": "Questions Fréquemment Po<PERSON>ées", "description": "FAQ Title"}, "faq.description": {"message": "Questions Fréquemment Po<PERSON>ées"}, "faq.section.title.1": {"message": "Qu'est-ce qu'OpenHands ?", "description": "First Section Title"}, "faq.section.highlight": {"message": "OpenHands", "description": "Highlight Text"}, "faq.section.description.1": {"message": "est un ingénieur logiciel autonome qui peut résoudre des tâches d'ingénierie logicielle et de navigation web à tout moment. Il peut exécuter des requêtes en sciences des données, telles que \"Trouver le nombre de demandes de pull à l'repository OpenHands dans les derniers mois\", et des tâches d'ingénierie logicielle, comme \"Veuillez ajouter des tests à ce fichier et vérifier si tous les tests passent. Si ce n'est pas le cas, réparez le fichier.\"", "description": "Description for OpenHands"}, "faq.section.description.2": {"message": "De plus, OpenHands est une plateforme et communauté pour les développeurs d'agents qui souhaitent tester et évaluer de nouveaux agents.", "description": "Further Description for OpenHands"}, "faq.section.title.2": {"message": "Support", "description": "Support Section Title"}, "faq.section.support.answer": {"message": "Si vous rencontrez un problème que d'autres utilisateurs peuvent également avoir, merci de le signaler sur {githubLink}. Si vous avez des difficultés à l'installation ou des questions générales, rejoignez-vous sur {discordLink} ou {slackLink}.", "description": "Support Answer"}, "faq.section.title.3": {"message": "Comment résoudre un problème sur GitHub avec OpenHands ?", "description": "GitHub Issue Section Title"}, "faq.section.github.steps.intro": {"message": "Pour résoudre un problème sur GitHub en utilisant OpenHands, envoyez une commande à OpenHands demandant qu'il suit des étapes comme les suivantes :", "description": "GitHub Steps Introduction"}, "faq.section.github.step1": {"message": "Lisez l'issue https://github.com/All-Hands-AI/OpenHands/issues/1611", "description": "GitHub Step 1"}, "faq.section.github.step2": {"message": "<PERSON><PERSON><PERSON> le dépôt et vérifier une nouvelle branche", "description": "GitHub Step 2"}, "faq.section.github.step3": {"message": "Sur la base des instructions dans la description de l'issue, modifiez les fichiers pour résoudre le problème", "description": "GitHub Step 3"}, "faq.section.github.step4": {"message": "<PERSON><PERSON><PERSON> le résultat à GitHub en utilisant la variable d'environnement GITHUB_TOKEN", "description": "GitHub Step 4"}, "faq.section.github.step5": {"message": "Dites-moi le lien que je dois utiliser pour envoyer une demande de pull", "description": "GitHub Step 5"}, "faq.section.github.steps.preRun": {"message": "Avant de lancer <PERSON><PERSON><PERSON><PERSON>, vous pouvez faire :", "description": "GitHub Steps Pre-Run"}, "faq.section.github.steps.tokenInfo": {"message": "où XXX est un jeton GitHub que vous avez créé et qui a les autorisations pour pousser dans le dépôt OpenHands. Si vous n'avez pas d'autorisations de modification du dépôt OpenHands, vous devrez peut-être changer cela en :", "description": "GitHub Steps Token Info"}, "faq.section.github.steps.usernameInfo": {"message": "où USERNAME est votre nom GitHub.", "description": "GitHub Steps Username Info"}, "faq.section.title.4": {"message": "Comment OpenHands est-il différent de Devin ?", "description": "Devin Section Title"}, "faq.section.openhands.linkText": {"message": "<PERSON>", "description": "<PERSON>"}, "faq.section.openhands.description": {"message": "est un produit commercial par Cognition Inc., qui a servi d'inspiration initiale pour OpenHands. Les deux visent à bien faire le travail d'ingénierie logicielle, mais vous pouvez télécharger, utiliser et modifier OpenHands, tandis que Devin peut être utilisé uniquement via le site de Cognition. De plus, OpenHands a évolué au-delà de l'inspiration initiale, et est maintenant un écosystème communautaire pour le développement d'agents en général, et nous serions ravis de vous voir rejoindre et", "description": "Devin <PERSON>"}, "faq.section.openhands.contribute": {"message": "contribuer", "description": "Contribute Link"}, "faq.section.title.5": {"message": "Comment OpenHands est-il différent de ChatGPT ?", "description": "ChatGPT Section Title"}, "faq.section.chatgpt.description": {"message": "ChatGPT vous pouvez accéder en ligne, il ne se connecte pas aux fichiers locaux et ses capacités d'exécution du code sont limitées. Alors qu'il peut écrire du code, mais c'est difficile à tester ou à exécuter.", "description": "ChatGPT Description"}, "homepage.description": {"message": "Génération d'code AI pour l'ingénierie logicielle.", "description": "The homepage description"}, "homepage.getStarted": {"message": "Commencer"}, "welcome.message": {"message": "Bienvenue à OpenHands, un système d'IA autonome ingénieur logiciel capable d'exécuter des tâches d'ingénierie complexes et de collaborer activement avec les utilisateurs sur les projets de développement logiciel."}, "theme.ErrorPageContent.title": {"message": "<PERSON><PERSON> page a planté.", "description": "The title of the fallback page when the page crashed"}, "theme.BackToTopButton.buttonAriaLabel": {"message": "Retour<PERSON> en haut de la page", "description": "The ARIA label for the back to top button"}, "theme.blog.archive.title": {"message": "Archives", "description": "The page & hero title of the blog archive page"}, "theme.blog.archive.description": {"message": "Archives", "description": "The page & hero description of the blog archive page"}, "theme.blog.paginator.navAriaLabel": {"message": "Pagination des listes d'articles du blog", "description": "The ARIA label for the blog pagination"}, "theme.blog.paginator.newerEntries": {"message": "Nouvelles entrées", "description": "The label used to navigate to the newer blog posts page (previous page)"}, "theme.blog.paginator.olderEntries": {"message": "Anciennes entrées", "description": "The label used to navigate to the older blog posts page (next page)"}, "theme.blog.post.paginator.navAriaLabel": {"message": "Pagination des articles du blog", "description": "The ARIA label for the blog posts pagination"}, "theme.blog.post.paginator.newerPost": {"message": "Article plus récent", "description": "The blog post button label to navigate to the newer/previous post"}, "theme.blog.post.paginator.olderPost": {"message": "Article plus ancien", "description": "The blog post button label to navigate to the older/next post"}, "theme.blog.post.plurals": {"message": "Un article|{count} articles", "description": "Pluralized label for \"{count} posts\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.blog.tagTitle": {"message": "{nPosts} tags avec « {tagName} »", "description": "The title of the page for a blog tag"}, "theme.tags.tagsPageLink": {"message": "Voir tous les tags", "description": "The label of the link targeting the tag list page"}, "theme.colorToggle.ariaLabel": {"message": "Basculer entre le mode sombre et clair (actuellement {mode})", "description": "The ARIA label for the navbar color mode toggle"}, "theme.colorToggle.ariaLabel.mode.dark": {"message": "mode sombre", "description": "The name for the dark color mode"}, "theme.colorToggle.ariaLabel.mode.light": {"message": "mode clair", "description": "The name for the light color mode"}, "theme.docs.breadcrumbs.navAriaLabel": {"message": "Bouton de navigation des liens de la page", "description": "The ARIA label for the breadcrumbs"}, "theme.docs.DocCard.categoryDescription.plurals": {"message": "1 élément|{count} éléments", "description": "The default description for a category card in the generated index about how many items this category includes"}, "theme.docs.paginator.navAriaLabel": {"message": "Pages de documentation", "description": "The ARIA label for the docs pagination"}, "theme.docs.paginator.previous": {"message": "Précédent", "description": "The label used to navigate to the previous doc"}, "theme.docs.paginator.next": {"message": "Suivant", "description": "The label used to navigate to the next doc"}, "theme.docs.tagDocListPageTitle.nDocsTagged": {"message": "Un document tagué|{count} documents tagués", "description": "Pluralized label for \"{count} docs tagged\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.docs.tagDocListPageTitle": {"message": "{nDocsTagged} avec \"{tagName}\"", "description": "The title of the page for a docs tag"}, "theme.docs.versionBadge.label": {"message": "Version: {versionLabel}"}, "theme.docs.versions.unreleasedVersionLabel": {"message": "Ceci est la documentation de la prochaine version {versionLabel} de {siteTitle}.", "description": "The label used to tell the user that he's browsing an unreleased doc version"}, "theme.docs.versions.unmaintainedVersionLabel": {"message": "Ceci est la documentation de {siteTitle} {versionLabel}, qui n'est plus activement maintenue.", "description": "The label used to tell the user that he's browsing an unmaintained doc version"}, "theme.docs.versions.latestVersionSuggestionLabel": {"message": "Pour une documentation à jour, consultez la {latestVersionLink} ({versionLabel}).", "description": "The label used to tell the user to check the latest version"}, "theme.docs.versions.latestVersionLinkLabel": {"message": "dernière version", "description": "The label used for the latest version suggestion link label"}, "theme.common.editThisPage": {"message": "Éditer cette page", "description": "The link label to edit the current page"}, "theme.common.headingLinkTitle": {"message": "Lien direct vers {heading}", "description": "Title for link to heading"}, "theme.lastUpdated.atDate": {"message": " le {date}", "description": "The words used to describe on which date a page has been last updated"}, "theme.lastUpdated.byUser": {"message": " par {user}", "description": "The words used to describe by who the page has been last updated"}, "theme.lastUpdated.lastUpdatedAtBy": {"message": "<PERSON><PERSON><PERSON> mise à jour{atDate}{byUser}", "description": "The sentence used to display when a page has been last updated, and by who"}, "theme.navbar.mobileVersionsDropdown.label": {"message": "Versions", "description": "The label for the navbar versions dropdown on mobile view"}, "theme.NotFound.title": {"message": "Page introuvable", "description": "The title of the 404 page"}, "theme.tags.tagsListLabel": {"message": "Tags :", "description": "The label alongside a tag list"}, "theme.admonition.caution": {"message": "prudence", "description": "The default label used for the Caution admonition (:::caution)"}, "theme.admonition.danger": {"message": "danger", "description": "The default label used for the Danger admonition (:::danger)"}, "theme.admonition.info": {"message": "information", "description": "The default label used for the Info admonition (:::info)"}, "theme.admonition.note": {"message": "remarque", "description": "The default label used for the Note admonition (:::note)"}, "theme.admonition.tip": {"message": "astuce", "description": "The default label used for the Tip admonition (:::tip)"}, "theme.admonition.warning": {"message": "prudence", "description": "The default label used for the Warning admonition (:::warning)"}, "theme.AnnouncementBar.closeButtonAriaLabel": {"message": "<PERSON><PERSON><PERSON>", "description": "The ARIA label for close button of announcement bar"}, "theme.blog.sidebar.navAriaLabel": {"message": "Navigation vers les articles récents du blog", "description": "The ARIA label for recent posts in the blog sidebar"}, "theme.CodeBlock.copied": {"message": "<PERSON><PERSON><PERSON>", "description": "The copied button label on code blocks"}, "theme.CodeBlock.copyButtonAriaLabel": {"message": "Copier le code", "description": "The ARIA label for copy code blocks button"}, "theme.CodeBlock.copy": {"message": "<PERSON><PERSON><PERSON>", "description": "The copy button label on code blocks"}, "theme.CodeBlock.wordWrapToggle": {"message": "Activer/dés<PERSON>r le retour à la ligne", "description": "The title attribute for toggle word wrapping button of code block lines"}, "theme.DocSidebarItem.expandCategoryAriaLabel": {"message": "Développer la catégorie '{label}' de la barre latérale", "description": "The ARIA label to expand the sidebar category"}, "theme.DocSidebarItem.collapseCategoryAriaLabel": {"message": "Réduire la catégorie '{label}' de la barre latérale", "description": "The ARIA label to collapse the sidebar category"}, "theme.NavBar.navAriaLabel": {"message": "Main", "description": "The ARIA label for the main navigation"}, "theme.navbar.mobileLanguageDropdown.label": {"message": "<PERSON><PERSON>", "description": "The label for the mobile language switcher dropdown"}, "theme.NotFound.p1": {"message": "Nous n'avons pas trouvé ce que vous recherchez.", "description": "The first paragraph of the 404 page"}, "theme.NotFound.p2": {"message": "Veuillez contacter le propriétaire du site qui vous a lié à l'URL d'origine et leur faire savoir que leur lien est cassé.", "description": "The 2nd paragraph of the 404 page"}, "theme.TOCCollapsible.toggleButtonLabel": {"message": "Sur cette page", "description": "The label used by the button on the collapsible TOC component"}, "theme.blog.post.readMore": {"message": "Lire plus", "description": "The label used in blog post item excerpts to link to full blog posts"}, "theme.blog.post.readMoreLabel": {"message": "En savoir plus sur {title}", "description": "The ARIA label for the link to full blog posts from excerpts"}, "theme.blog.post.readingTime.plurals": {"message": "Une minute de lecture|{readingTime} minutes de lecture", "description": "Pluralized label for \"{readingTime} min read\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.docs.breadcrumbs.home": {"message": "Page d'accueil", "description": "The ARIA label for the home page in the breadcrumbs"}, "theme.docs.sidebar.collapseButtonTitle": {"message": "Réduire le menu latéral", "description": "The title attribute for collapse button of doc sidebar"}, "theme.docs.sidebar.collapseButtonAriaLabel": {"message": "Réduire le menu latérale", "description": "The title attribute for collapse button of doc sidebar"}, "theme.docs.sidebar.navAriaLabel": {"message": "Barre de navigation latérale des docs", "description": "The ARIA label for the sidebar navigation"}, "theme.docs.sidebar.closeSidebarButtonAriaLabel": {"message": "Fermer la barre de navigation", "description": "The ARIA label for close button of mobile sidebar"}, "theme.navbar.mobileSidebarSecondaryMenu.backButtonLabel": {"message": "← Retour au menu principal", "description": "The label of the back button to return to main menu, inside the mobile navbar sidebar secondary menu (notably used to display the docs sidebar)"}, "theme.docs.sidebar.toggleSidebarButtonAriaLabel": {"message": "Ouvrir/fermer la barre de navigation", "description": "The ARIA label for hamburger menu button of mobile navigation"}, "theme.docs.sidebar.expandButtonTitle": {"message": "Déplier le menu latéral", "description": "The ARIA label and title attribute for expand button of doc sidebar"}, "theme.docs.sidebar.expandButtonAriaLabel": {"message": "Déployer le menu latérale", "description": "The ARIA label and title attribute for expand button of doc sidebar"}, "theme.ErrorPageContent.tryAgain": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "The label of the button to try again rendering when the React error boundary captures an error"}, "theme.common.skipToMainContent": {"message": "Aller directement au contenu principal", "description": "The skip to content label used for accessibility, allowing to rapidly navigate to main content with keyboard tab/enter navigation"}, "theme.tags.tagsPageTitle": {"message": "Tags", "description": "The title of the tag list page"}, "theme.unlistedContent.title": {"message": "Page non répertoriée", "description": "The unlisted content banner title"}, "theme.unlistedContent.message": {"message": "Cette page n'est pas répertoriée. Les moteurs de recherche ne l'indexeront pas, et seuls les utilisateurs ayant un lien direct peuvent y accéder.", "description": "The unlisted content banner message"}, "Use AI to tackle the toil in your backlog. Our agents have all the same tools as a human developer: they can modify code, run commands, browse the web, call APIs, and yes-even copy code snippets from StackOverflow.": {"message": "Utilisez l'IA pour gérer les tâches répétitives de votre backlog. Nos agents disposent des mêmes outils qu'un développeur humain : ils peuvent modifier du code, exécuter des commandes, naviguer sur le web, appeler des API et même copier des extraits de code depuis StackOverflow."}, "Get started with OpenHands.": {"message": "Commencer avec OpenHands"}, "Most Popular Links": {"message": "Liens Populaires"}, "Customizing OpenHands to a repository": {"message": "Personnaliser OpenHands pour un dépôt"}, "Integrating OpenHands with Github": {"message": "Intégrer OpenHands avec Github"}, "Recommended models to use": {"message": "Mod<PERSON><PERSON> recommandés"}, "Connecting OpenHands to your filesystem": {"message": "Connecter OpenHands à votre système de fichiers"}}