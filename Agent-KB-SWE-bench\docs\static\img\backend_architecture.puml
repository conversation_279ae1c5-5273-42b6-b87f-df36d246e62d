@startuml openhands
!pragma useIntermediatePackages false

class openhands.action.agent.AgentEchoAction {
  content: str
  runnable: bool
  action: str
}
class openhands.action.agent.AgentFinishAction {
  runnable: bool
  action: str
}
class openhands.observation.AgentMessageObservation {
  role: str
  observation: str
}
class openhands.action.agent.AgentSummarizeAction {
  summary: str
  action: str
}
class openhands.action.agent.AgentThinkAction {
  thought: str
  runnable: bool
  action: str
}
class openhands.action.base.ExecutableAction {
}
class openhands.action.base.NotExecutableAction {
}
class openhands.observation.Observation {
  content: str
}
class openhands.action.base.Action {
}
class openhands.action.base.NullAction {
  action: str
}
class openhands.action.bash.CmdRunAction {
  command: str
  action: str
}
class openhands.action.browse.BrowseURLAction {
  url: str
  action: str
}
class openhands.observation.BrowserOutputObservation {
  url: str
  status_code: int
  error: bool
  observation: str
}
class openhands.action.fileop.FileReadAction {
  path: str
  action: str
}
class openhands.observation.FileReadObservation {
  path: str
  observation: str
}
class openhands.action.fileop.FileWriteAction {
  path: str
  contents: str
  action: str
}
class openhands.observation.FileWriteObservation {
  path: str
  observation: str
}
class openhands.action.tasks.AddTaskAction {
  parent: str
  goal: str
  subtasks: list
  action: str
}
class openhands.action.tasks.ModifyTaskAction {
  id: str
  state: str
  action: str
}
abstract class openhands.agent.Agent {
  _registry: Dict[str, Type[Agent]] {static}
  llm: LLM
  _complete: None
}
class openhands.llm.llm.LLM {
  model: None
  api_key: None
  base_url: None
  _debug_dir: None
  _debug_idx: None
  _debug_id: None
  _completion: None
}
class openhands.controller.agent_controller.AgentController {
  agent: Agent
  max_iterations: int
  workdir: str
  command_manager: CommandManager
  state: State
  plan: Plan
  callbacks: List[Callable]
}
class openhands.observation.AgentErrorObservation {
  observation: str
}
class openhands.controller.command_manager.CommandManager {
  directory: None
  shell: None
}
class openhands.observation.NullObservation {
  observation: str
}
class openhands.plan.Plan {
  main_goal: str {static}
  task: Task {static}
  main_goal: str
  task: None
}
class openhands.state.State {
  plan: Plan
  iteration: int
  history: List[Tuple[Action, Observation]]
  updated_info: List[Tuple[Action, Observation]]
}
class openhands.observation.CmdOutputObservation {
  command: str
  exit_code: int
  observation: str
}
class openhands.sandbox.sandbox.DockerInteractive {
  instance_id: None
  instance_id: None
  workspace_dir: None
  workspace_dir: None
  workspace_dir: None
  timeout: int
  base_container_image: None
  container_name: None
}
class openhands.observation.UserMessageObservation {
  role: str
  observation: str
}
class openhands.plan.Task {
  id: str {static}
  goal: str {static}
  parent: Task | None {static}
  subtasks: List[Task] {static}
  id: None
  id: None
  parent: None
  goal: str
  subtasks: None
}

class openhands.server.session.Session {
  websocket: None
  controller: Optional[AgentController]
  agent: Optional[Agent]
  agent_task: None
}

openhands.action.base.ExecutableAction <|-- openhands.action.agent.AgentEchoAction
openhands.action.base.NotExecutableAction <|-- openhands.action.agent.AgentFinishAction
openhands.observation.Observation <|-- openhands.observation.AgentMessageObservation
openhands.action.base.NotExecutableAction <|-- openhands.action.agent.AgentSummarizeAction
openhands.action.base.NotExecutableAction <|-- openhands.action.agent.AgentThinkAction
openhands.action.base.Action <|-- openhands.action.base.ExecutableAction
openhands.action.base.Action <|-- openhands.action.base.NotExecutableAction
openhands.action.base.NotExecutableAction <|-- openhands.action.base.NullAction
openhands.action.base.ExecutableAction <|-- openhands.action.bash.CmdRunAction
openhands.action.base.ExecutableAction <|-- openhands.action.browse.BrowseURLAction
openhands.observation.Observation <|-- openhands.observation.BrowserOutputObservation
openhands.action.base.ExecutableAction <|-- openhands.action.fileop.FileReadAction
openhands.observation.Observation <|-- openhands.observation.FileReadObservation
openhands.action.base.ExecutableAction <|-- openhands.action.fileop.FileWriteAction
openhands.observation.Observation <|-- openhands.observation.FileWriteObservation
openhands.action.base.NotExecutableAction <|-- openhands.action.tasks.AddTaskAction
openhands.action.base.NotExecutableAction <|-- openhands.action.tasks.ModifyTaskAction
openhands.agent.Agent *-- openhands.agent.Agent
openhands.agent.Agent *-- openhands.llm.llm.LLM
openhands.controller.agent_controller.AgentController *-- openhands.agent.Agent
openhands.observation.Observation <|-- openhands.observation.AgentErrorObservation
openhands.observation.Observation <|-- openhands.observation.NullObservation
openhands.plan.Plan *-- openhands.plan.Task
openhands.state.State *-- openhands.plan.Plan
openhands.state.State *-- openhands.observation.CmdOutputObservation
openhands.state.State *-- openhands.action.base.Action
openhands.state.State *-- openhands.observation.Observation
openhands.observation.Observation <|-- openhands.observation.CmdOutputObservation
openhands.observation.Observation <|-- openhands.observation.UserMessageObservation
openhands.plan.Task *-- openhands.plan.Task
openhands.server.session.Session *-- openhands.controller.agent_controller.AgentController
openhands.server.session.Session *-- openhands.agent.Agent
openhands.controller.agent_controller.AgentController -> openhands.state.State
openhands.controller.agent_controller.AgentController -> openhands.plan.Plan
openhands.controller.agent_controller.AgentController -> openhands.controller.command_manager.CommandManager
openhands.controller.command_manager.CommandManager -> openhands.sandbox.sandbox.DockerInteractive

footer Based on f3fda42; Generated by //py2puml//
@enduml
