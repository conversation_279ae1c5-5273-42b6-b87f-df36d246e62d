{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["import json\n", "from collections import defaultdict\n", "\n", "\n", "def read_json_lines(file_path, id_key):\n", "    \"\"\"读取每行是一个JSON对象的文件\"\"\"\n", "    data = {}\n", "    level_dict = defaultdict(list)\n", "    try:\n", "        print(f'Opening file: {file_path}')\n", "        with open(file_path, 'r', encoding='utf-8') as file:\n", "            for line_number, line in enumerate(file, 1):\n", "                try:\n", "                    json_obj = json.loads(line.strip())\n", "                    if id_key not in json_obj:\n", "                        print(f'Warning: {id_key} not found in line {line_number}')\n", "                        continue\n", "                    if json_obj[id_key] in data:\n", "                        print(\n", "                            f'Warning: Duplicate {id_key} found in line {line_number}'\n", "                        )\n", "                        continue\n", "                    data[json_obj[id_key]] = json_obj\n", "                    level_dict[json_obj['Level']].append(json_obj[id_key])\n", "\n", "                except json.JSONDecodeError as e:\n", "                    print(f'第 {line_number} 行JSON解析错误: {e}')\n", "                    continue\n", "        print(f'Successfully read {len(data)} records from {file_path}')\n", "        return data, level_dict\n", "    except FileNotFoundError:\n", "        print(f'错误：文件 {file_path} 未找到')\n", "        return None\n", "    except Exception as e:\n", "        print(f'读取文件时发生错误: {e}')\n", "        return None"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Opening file: data/test_metadata.jsonl\n", "Successfully read 301 records from data/test_metadata.jsonl\n", "Opening file: data/validation_metadata.jsonl\n", "Successfully read 165 records from data/validation_metadata.jsonl\n"]}], "source": ["test_path = 'data/test_metadata.jsonl'\n", "test_metadata, test_level_dict = read_json_lines(test_path, 'task_id')\n", "\n", "valid_path = 'data/validation_metadata.jsonl'\n", "valid_metadata, valid_level_dict = read_json_lines(valid_path, 'task_id')"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["6 93 301\n", "10 159 301\n", "3 49 301\n"]}], "source": ["import random\n", "\n", "sampled_id = defaultdict(list)\n", "for level, l in test_level_dict.items():\n", "    sampled_num = int(len(l) / len(test_metadata) * 20)\n", "    print(sampled_num, len(l), len(test_metadata))\n", "    sampled_id[f'Level {level}'] = random.sample(l, sampled_num)"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"data": {"text/plain": ["defaultdict(list,\n", "            {'Level 1': ['d89733a3-7d86-4ed8-b5a3-bf4831b06e3c',\n", "              '3c5f0280-b1a3-43cf-817e-c3fa0016b1e2',\n", "              '60fbc5a3-2805-4ad4-8eef-b58843b5053b',\n", "              '220a2b08-ffdc-4665-af4e-025670f5408b',\n", "              '6af95c8f-8cbf-4c12-b02c-f9a23cc1ecb9',\n", "              '70e0a9c6-24bf-48ed-afa1-f0d0eaaa0209'],\n", "             'Level 2': ['4cf4a5c1-7c9c-4cce-94cb-57b8be196244',\n", "              '82b89810-1217-4ad8-aa9f-26e7c74ba6e5',\n", "              '900bb2d0-c2ae-43a6-b25b-62f96c3770e3',\n", "              '04893fc3-34fc-4117-8457-a717ad01a6a9',\n", "              'f5d0b1c6-5e15-4c55-b60c-9fc855dda5cf',\n", "              '021a5339-744f-42b7-bd9b-9368b3efda7a',\n", "              'f2fa52f6-fc8a-498c-98d3-17f66c848d1b',\n", "              '4810c253-7b06-447d-8bf6-64558ac5f00f',\n", "              '3cc53dbf-1ab9-4d21-a56a-fc0151c10f89',\n", "              '9b98305b-af16-489e-adbc-41b5c5a0ec2d'],\n", "             'Level 3': ['460ef201-c5f4-41f4-9acd-e4215384e678',\n", "              'c68c0db6-1929-4194-8602-56dce5ddbd29',\n", "              '967ad395-7b16-43a2-83e7-41df7cd6401a']})"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["sampled_id"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["'e1fc63a2-da7a-432f-be78-7c4a95598703' in valid_metadata.keys()"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"data": {"text/plain": ["(165, 301)"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["len(valid_metadata), len(test_metadata)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "AKB20250404_2", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 2}