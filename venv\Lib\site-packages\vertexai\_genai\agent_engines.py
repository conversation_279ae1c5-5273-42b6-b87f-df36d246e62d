# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# Code generated by the Google Gen AI SDK generator DO NOT EDIT.

import json
import logging
import time
from typing import Any, Callable, Iterator, Optional, Sequence, Union
from urllib.parse import urlencode

from google.genai import _api_module
from google.genai import _common
from google.genai import types as genai_types
from google.genai._common import get_value_by_path as getv
from google.genai._common import set_value_by_path as setv
from google.genai.pagers import Pager

from . import _agent_engines_utils
from . import types


logger = logging.getLogger("vertexai_genai.agentengines")


def _ReasoningEngineSpec_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["agent_framework"]) is not None:
        setv(
            to_object,
            ["agentFramework"],
            getv(from_object, ["agent_framework"]),
        )

    if getv(from_object, ["class_methods"]) is not None:
        setv(to_object, ["classMethods"], getv(from_object, ["class_methods"]))

    if getv(from_object, ["deployment_spec"]) is not None:
        setv(
            to_object,
            ["deploymentSpec"],
            getv(from_object, ["deployment_spec"]),
        )

    if getv(from_object, ["package_spec"]) is not None:
        setv(to_object, ["packageSpec"], getv(from_object, ["package_spec"]))

    return to_object


def _CreateAgentEngineConfig_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}

    if getv(from_object, ["display_name"]) is not None:
        setv(parent_object, ["displayName"], getv(from_object, ["display_name"]))

    if getv(from_object, ["description"]) is not None:
        setv(parent_object, ["description"], getv(from_object, ["description"]))

    if getv(from_object, ["spec"]) is not None:
        setv(
            parent_object,
            ["spec"],
            _ReasoningEngineSpec_to_vertex(getv(from_object, ["spec"]), to_object),
        )

    return to_object


def _CreateAgentEngineRequestParameters_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["config"]) is not None:
        setv(
            to_object,
            ["config"],
            _CreateAgentEngineConfig_to_vertex(
                getv(from_object, ["config"]), to_object
            ),
        )

    return to_object


def _AgentEngineMemoryConfig_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}

    if getv(from_object, ["display_name"]) is not None:
        setv(parent_object, ["displayName"], getv(from_object, ["display_name"]))

    if getv(from_object, ["description"]) is not None:
        setv(parent_object, ["description"], getv(from_object, ["description"]))

    return to_object


def _CreateAgentEngineMemoryRequestParameters_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["name"]) is not None:
        setv(to_object, ["_url", "name"], getv(from_object, ["name"]))

    if getv(from_object, ["fact"]) is not None:
        setv(to_object, ["fact"], getv(from_object, ["fact"]))

    if getv(from_object, ["scope"]) is not None:
        setv(to_object, ["scope"], getv(from_object, ["scope"]))

    if getv(from_object, ["config"]) is not None:
        setv(
            to_object,
            ["config"],
            _AgentEngineMemoryConfig_to_vertex(
                getv(from_object, ["config"]), to_object
            ),
        )

    return to_object


def _DeleteAgentEngineRequestParameters_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["name"]) is not None:
        setv(to_object, ["_url", "name"], getv(from_object, ["name"]))

    if getv(from_object, ["force"]) is not None:
        setv(to_object, ["force"], getv(from_object, ["force"]))

    if getv(from_object, ["config"]) is not None:
        setv(to_object, ["config"], getv(from_object, ["config"]))

    return to_object


def _DeleteAgentEngineMemoryRequestParameters_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["name"]) is not None:
        setv(to_object, ["_url", "name"], getv(from_object, ["name"]))

    if getv(from_object, ["config"]) is not None:
        setv(to_object, ["config"], getv(from_object, ["config"]))

    return to_object


def _GenerateMemoriesRequestVertexSessionSource_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["end_time"]) is not None:
        setv(to_object, ["endTime"], getv(from_object, ["end_time"]))

    if getv(from_object, ["session"]) is not None:
        setv(to_object, ["session"], getv(from_object, ["session"]))

    if getv(from_object, ["start_time"]) is not None:
        setv(to_object, ["startTime"], getv(from_object, ["start_time"]))

    return to_object


def _GenerateMemoriesRequestDirectContentsSource_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["events"]) is not None:
        setv(to_object, ["events"], getv(from_object, ["events"]))

    return to_object


def _GenerateAgentEngineMemoriesConfig_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}

    if getv(from_object, ["disable_consolidation"]) is not None:
        setv(
            to_object,
            ["disableConsolidation"],
            getv(from_object, ["disable_consolidation"]),
        )

    return to_object


def _GenerateAgentEngineMemoriesRequestParameters_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["name"]) is not None:
        setv(to_object, ["_url", "name"], getv(from_object, ["name"]))

    if getv(from_object, ["vertex_session_source"]) is not None:
        setv(
            to_object,
            ["vertexSessionSource"],
            _GenerateMemoriesRequestVertexSessionSource_to_vertex(
                getv(from_object, ["vertex_session_source"]), to_object
            ),
        )

    if getv(from_object, ["direct_contents_source"]) is not None:
        setv(
            to_object,
            ["directContentsSource"],
            _GenerateMemoriesRequestDirectContentsSource_to_vertex(
                getv(from_object, ["direct_contents_source"]), to_object
            ),
        )

    if getv(from_object, ["scope"]) is not None:
        setv(to_object, ["scope"], getv(from_object, ["scope"]))

    if getv(from_object, ["config"]) is not None:
        setv(
            to_object,
            ["config"],
            _GenerateAgentEngineMemoriesConfig_to_vertex(
                getv(from_object, ["config"]), to_object
            ),
        )

    return to_object


def _GetAgentEngineRequestParameters_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["name"]) is not None:
        setv(to_object, ["_url", "name"], getv(from_object, ["name"]))

    if getv(from_object, ["config"]) is not None:
        setv(to_object, ["config"], getv(from_object, ["config"]))

    return to_object


def _GetAgentEngineMemoryRequestParameters_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["name"]) is not None:
        setv(to_object, ["_url", "name"], getv(from_object, ["name"]))

    if getv(from_object, ["config"]) is not None:
        setv(to_object, ["config"], getv(from_object, ["config"]))

    return to_object


def _ListAgentEngineConfig_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}

    if getv(from_object, ["page_size"]) is not None:
        setv(
            parent_object,
            ["_query", "pageSize"],
            getv(from_object, ["page_size"]),
        )

    if getv(from_object, ["page_token"]) is not None:
        setv(
            parent_object,
            ["_query", "pageToken"],
            getv(from_object, ["page_token"]),
        )

    if getv(from_object, ["filter"]) is not None:
        setv(parent_object, ["_query", "filter"], getv(from_object, ["filter"]))

    return to_object


def _ListAgentEngineRequestParameters_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["config"]) is not None:
        setv(
            to_object,
            ["config"],
            _ListAgentEngineConfig_to_vertex(getv(from_object, ["config"]), to_object),
        )

    return to_object


def _ListAgentEngineMemoryConfig_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}

    if getv(from_object, ["page_size"]) is not None:
        setv(
            parent_object,
            ["_query", "pageSize"],
            getv(from_object, ["page_size"]),
        )

    if getv(from_object, ["page_token"]) is not None:
        setv(
            parent_object,
            ["_query", "pageToken"],
            getv(from_object, ["page_token"]),
        )

    if getv(from_object, ["filter"]) is not None:
        setv(parent_object, ["_query", "filter"], getv(from_object, ["filter"]))

    return to_object


def _ListAgentEngineMemoryRequestParameters_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["name"]) is not None:
        setv(to_object, ["_url", "name"], getv(from_object, ["name"]))

    if getv(from_object, ["config"]) is not None:
        setv(
            to_object,
            ["config"],
            _ListAgentEngineMemoryConfig_to_vertex(
                getv(from_object, ["config"]), to_object
            ),
        )

    return to_object


def _GetAgentEngineOperationParameters_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["operation_name"]) is not None:
        setv(
            to_object,
            ["_url", "operationName"],
            getv(from_object, ["operation_name"]),
        )

    if getv(from_object, ["config"]) is not None:
        setv(to_object, ["config"], getv(from_object, ["config"]))

    return to_object


def _GetAgentEngineMemoryOperationParameters_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["operation_name"]) is not None:
        setv(
            to_object,
            ["_url", "operationName"],
            getv(from_object, ["operation_name"]),
        )

    if getv(from_object, ["config"]) is not None:
        setv(to_object, ["config"], getv(from_object, ["config"]))

    return to_object


def _GetAgentEngineGenerateMemoriesOperationParameters_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["operation_name"]) is not None:
        setv(
            to_object,
            ["_url", "operationName"],
            getv(from_object, ["operation_name"]),
        )

    if getv(from_object, ["config"]) is not None:
        setv(to_object, ["config"], getv(from_object, ["config"]))

    return to_object


def _QueryAgentEngineConfig_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}

    if getv(from_object, ["class_method"]) is not None:
        setv(parent_object, ["classMethod"], getv(from_object, ["class_method"]))

    if getv(from_object, ["input"]) is not None:
        setv(parent_object, ["input"], getv(from_object, ["input"]))

    if getv(from_object, ["include_all_fields"]) is not None:
        setv(
            to_object,
            ["includeAllFields"],
            getv(from_object, ["include_all_fields"]),
        )

    return to_object


def _QueryAgentEngineRequestParameters_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["name"]) is not None:
        setv(to_object, ["_url", "name"], getv(from_object, ["name"]))

    if getv(from_object, ["config"]) is not None:
        setv(
            to_object,
            ["config"],
            _QueryAgentEngineConfig_to_vertex(getv(from_object, ["config"]), to_object),
        )

    return to_object


def _RetrieveMemoriesRequestSimilaritySearchParams_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["search_query"]) is not None:
        setv(to_object, ["searchQuery"], getv(from_object, ["search_query"]))

    if getv(from_object, ["top_k"]) is not None:
        setv(to_object, ["topK"], getv(from_object, ["top_k"]))

    return to_object


def _RetrieveMemoriesRequestSimpleRetrievalParams_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["page_size"]) is not None:
        setv(to_object, ["pageSize"], getv(from_object, ["page_size"]))

    if getv(from_object, ["page_token"]) is not None:
        setv(to_object, ["pageToken"], getv(from_object, ["page_token"]))

    return to_object


def _RetrieveAgentEngineMemoriesRequestParameters_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["name"]) is not None:
        setv(to_object, ["_url", "name"], getv(from_object, ["name"]))

    if getv(from_object, ["scope"]) is not None:
        setv(to_object, ["scope"], getv(from_object, ["scope"]))

    if getv(from_object, ["similarity_search_params"]) is not None:
        setv(
            to_object,
            ["similaritySearchParams"],
            _RetrieveMemoriesRequestSimilaritySearchParams_to_vertex(
                getv(from_object, ["similarity_search_params"]), to_object
            ),
        )

    if getv(from_object, ["simple_retrieval_params"]) is not None:
        setv(
            to_object,
            ["simpleRetrievalParams"],
            _RetrieveMemoriesRequestSimpleRetrievalParams_to_vertex(
                getv(from_object, ["simple_retrieval_params"]), to_object
            ),
        )

    if getv(from_object, ["config"]) is not None:
        setv(to_object, ["config"], getv(from_object, ["config"]))

    return to_object


def _UpdateAgentEngineConfig_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}

    if getv(from_object, ["display_name"]) is not None:
        setv(parent_object, ["displayName"], getv(from_object, ["display_name"]))

    if getv(from_object, ["description"]) is not None:
        setv(parent_object, ["description"], getv(from_object, ["description"]))

    if getv(from_object, ["spec"]) is not None:
        setv(
            parent_object,
            ["spec"],
            _ReasoningEngineSpec_to_vertex(getv(from_object, ["spec"]), to_object),
        )

    if getv(from_object, ["update_mask"]) is not None:
        setv(
            parent_object,
            ["_query", "updateMask"],
            getv(from_object, ["update_mask"]),
        )

    return to_object


def _UpdateAgentEngineRequestParameters_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["name"]) is not None:
        setv(to_object, ["_url", "name"], getv(from_object, ["name"]))

    if getv(from_object, ["config"]) is not None:
        setv(
            to_object,
            ["config"],
            _UpdateAgentEngineConfig_to_vertex(
                getv(from_object, ["config"]), to_object
            ),
        )

    return to_object


def _UpdateAgentEngineMemoryConfig_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}

    if getv(from_object, ["display_name"]) is not None:
        setv(parent_object, ["displayName"], getv(from_object, ["display_name"]))

    if getv(from_object, ["description"]) is not None:
        setv(parent_object, ["description"], getv(from_object, ["description"]))

    if getv(from_object, ["update_mask"]) is not None:
        setv(
            parent_object,
            ["_query", "updateMask"],
            getv(from_object, ["update_mask"]),
        )

    return to_object


def _UpdateAgentEngineMemoryRequestParameters_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["name"]) is not None:
        setv(to_object, ["_url", "name"], getv(from_object, ["name"]))

    if getv(from_object, ["fact"]) is not None:
        setv(to_object, ["fact"], getv(from_object, ["fact"]))

    if getv(from_object, ["scope"]) is not None:
        setv(to_object, ["scope"], getv(from_object, ["scope"]))

    if getv(from_object, ["config"]) is not None:
        setv(
            to_object,
            ["config"],
            _UpdateAgentEngineMemoryConfig_to_vertex(
                getv(from_object, ["config"]), to_object
            ),
        )

    return to_object


def _ReasoningEngine_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["contextSpec"]) is not None:
        setv(to_object, ["context_spec"], getv(from_object, ["contextSpec"]))

    if getv(from_object, ["createTime"]) is not None:
        setv(to_object, ["create_time"], getv(from_object, ["createTime"]))

    if getv(from_object, ["description"]) is not None:
        setv(to_object, ["description"], getv(from_object, ["description"]))

    if getv(from_object, ["displayName"]) is not None:
        setv(to_object, ["display_name"], getv(from_object, ["displayName"]))

    if getv(from_object, ["etag"]) is not None:
        setv(to_object, ["etag"], getv(from_object, ["etag"]))

    if getv(from_object, ["name"]) is not None:
        setv(to_object, ["name"], getv(from_object, ["name"]))

    if getv(from_object, ["spec"]) is not None:
        setv(to_object, ["spec"], getv(from_object, ["spec"]))

    if getv(from_object, ["updateTime"]) is not None:
        setv(to_object, ["update_time"], getv(from_object, ["updateTime"]))

    return to_object


def _AgentEngineOperation_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["name"]) is not None:
        setv(to_object, ["name"], getv(from_object, ["name"]))

    if getv(from_object, ["metadata"]) is not None:
        setv(to_object, ["metadata"], getv(from_object, ["metadata"]))

    if getv(from_object, ["done"]) is not None:
        setv(to_object, ["done"], getv(from_object, ["done"]))

    if getv(from_object, ["error"]) is not None:
        setv(to_object, ["error"], getv(from_object, ["error"]))

    if getv(from_object, ["response"]) is not None:
        setv(
            to_object,
            ["response"],
            _ReasoningEngine_from_vertex(getv(from_object, ["response"]), to_object),
        )

    return to_object


def _Memory_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["createTime"]) is not None:
        setv(to_object, ["create_time"], getv(from_object, ["createTime"]))

    if getv(from_object, ["description"]) is not None:
        setv(to_object, ["description"], getv(from_object, ["description"]))

    if getv(from_object, ["displayName"]) is not None:
        setv(to_object, ["display_name"], getv(from_object, ["displayName"]))

    if getv(from_object, ["fact"]) is not None:
        setv(to_object, ["fact"], getv(from_object, ["fact"]))

    if getv(from_object, ["name"]) is not None:
        setv(to_object, ["name"], getv(from_object, ["name"]))

    if getv(from_object, ["scope"]) is not None:
        setv(to_object, ["scope"], getv(from_object, ["scope"]))

    if getv(from_object, ["updateTime"]) is not None:
        setv(to_object, ["update_time"], getv(from_object, ["updateTime"]))

    return to_object


def _AgentEngineMemoryOperation_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["name"]) is not None:
        setv(to_object, ["name"], getv(from_object, ["name"]))

    if getv(from_object, ["metadata"]) is not None:
        setv(to_object, ["metadata"], getv(from_object, ["metadata"]))

    if getv(from_object, ["done"]) is not None:
        setv(to_object, ["done"], getv(from_object, ["done"]))

    if getv(from_object, ["error"]) is not None:
        setv(to_object, ["error"], getv(from_object, ["error"]))

    if getv(from_object, ["response"]) is not None:
        setv(
            to_object,
            ["response"],
            _Memory_from_vertex(getv(from_object, ["response"]), to_object),
        )

    return to_object


def _DeleteAgentEngineOperation_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["name"]) is not None:
        setv(to_object, ["name"], getv(from_object, ["name"]))

    if getv(from_object, ["metadata"]) is not None:
        setv(to_object, ["metadata"], getv(from_object, ["metadata"]))

    if getv(from_object, ["done"]) is not None:
        setv(to_object, ["done"], getv(from_object, ["done"]))

    if getv(from_object, ["error"]) is not None:
        setv(to_object, ["error"], getv(from_object, ["error"]))

    return to_object


def _DeleteAgentEngineMemoryOperation_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["name"]) is not None:
        setv(to_object, ["name"], getv(from_object, ["name"]))

    if getv(from_object, ["metadata"]) is not None:
        setv(to_object, ["metadata"], getv(from_object, ["metadata"]))

    if getv(from_object, ["done"]) is not None:
        setv(to_object, ["done"], getv(from_object, ["done"]))

    if getv(from_object, ["error"]) is not None:
        setv(to_object, ["error"], getv(from_object, ["error"]))

    return to_object


def _GenerateMemoriesResponseGeneratedMemory_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["memory"]) is not None:
        setv(
            to_object,
            ["memory"],
            _Memory_from_vertex(getv(from_object, ["memory"]), to_object),
        )

    if getv(from_object, ["action"]) is not None:
        setv(to_object, ["action"], getv(from_object, ["action"]))

    return to_object


def _GenerateMemoriesResponse_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["generatedMemories"]) is not None:
        setv(
            to_object,
            ["generated_memories"],
            [
                _GenerateMemoriesResponseGeneratedMemory_from_vertex(item, to_object)
                for item in getv(from_object, ["generatedMemories"])
            ],
        )

    return to_object


def _AgentEngineGenerateMemoriesOperation_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["name"]) is not None:
        setv(to_object, ["name"], getv(from_object, ["name"]))

    if getv(from_object, ["metadata"]) is not None:
        setv(to_object, ["metadata"], getv(from_object, ["metadata"]))

    if getv(from_object, ["done"]) is not None:
        setv(to_object, ["done"], getv(from_object, ["done"]))

    if getv(from_object, ["error"]) is not None:
        setv(to_object, ["error"], getv(from_object, ["error"]))

    if getv(from_object, ["response"]) is not None:
        setv(
            to_object,
            ["response"],
            _GenerateMemoriesResponse_from_vertex(
                getv(from_object, ["response"]), to_object
            ),
        )

    return to_object


def _ListReasoningEnginesResponse_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["nextPageToken"]) is not None:
        setv(to_object, ["next_page_token"], getv(from_object, ["nextPageToken"]))

    if getv(from_object, ["reasoningEngines"]) is not None:
        setv(
            to_object,
            ["reasoning_engines"],
            [
                _ReasoningEngine_from_vertex(item, to_object)
                for item in getv(from_object, ["reasoningEngines"])
            ],
        )

    return to_object


def _ListReasoningEnginesMemoriesResponse_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["nextPageToken"]) is not None:
        setv(to_object, ["next_page_token"], getv(from_object, ["nextPageToken"]))

    if getv(from_object, ["memories"]) is not None:
        setv(
            to_object,
            ["memories"],
            [
                _Memory_from_vertex(item, to_object)
                for item in getv(from_object, ["memories"])
            ],
        )

    return to_object


def _QueryReasoningEngineResponse_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["output"]) is not None:
        setv(to_object, ["output"], getv(from_object, ["output"]))

    return to_object


def _RetrieveMemoriesResponse_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["nextPageToken"]) is not None:
        setv(to_object, ["next_page_token"], getv(from_object, ["nextPageToken"]))

    if getv(from_object, ["retrievedMemories"]) is not None:
        setv(
            to_object,
            ["retrieved_memories"],
            getv(from_object, ["retrievedMemories"]),
        )

    return to_object


class AgentEngines(_api_module.BaseModule):
    def _create(
        self, *, config: Optional[types.CreateAgentEngineConfigOrDict] = None
    ) -> types.AgentEngineOperation:
        """Creates a new Agent Engine."""

        parameter_model = types._CreateAgentEngineRequestParameters(
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _CreateAgentEngineRequestParameters_to_vertex(
                parameter_model
            )
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "reasoningEngines".format_map(request_url_dict)
            else:
                path = "reasoningEngines"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = self._api_client.request("post", path, request_dict, http_options)

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _AgentEngineOperation_from_vertex(response_dict)

        return_value = types.AgentEngineOperation._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    def _create_memory(
        self,
        *,
        name: str,
        fact: str,
        scope: dict[str, str],
        config: Optional[types.AgentEngineMemoryConfigOrDict] = None,
    ) -> types.AgentEngineMemoryOperation:
        """Creates a new memory in the Agent Engine."""

        parameter_model = types._CreateAgentEngineMemoryRequestParameters(
            name=name,
            fact=fact,
            scope=scope,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _CreateAgentEngineMemoryRequestParameters_to_vertex(
                parameter_model
            )
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "{name}/memories".format_map(request_url_dict)
            else:
                path = "{name}/memories"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = self._api_client.request("post", path, request_dict, http_options)

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _AgentEngineMemoryOperation_from_vertex(response_dict)

        return_value = types.AgentEngineMemoryOperation._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    def delete(
        self,
        *,
        name: str,
        force: Optional[bool] = None,
        config: Optional[types.DeleteAgentEngineConfigOrDict] = None,
    ) -> types.DeleteAgentEngineOperation:
        """Delete an Agent Engine resource.

        Args:
            name (str): Required. The name of the Agent Engine to be deleted.
              Format:
                `projects/{project}/locations/{location}/reasoningEngines/{resource_id}`
                or `reasoningEngines/{resource_id}`.
            force (bool): Optional. If set to True, child resources will also be
              deleted. Otherwise, the request will fail with FAILED_PRECONDITION
              error when the Agent Engine has undeleted child resources.
              Defaults to False.
            config (DeleteAgentEngineConfig): Optional. Additional
              configurations for deleting the Agent Engine.
        """

        parameter_model = types._DeleteAgentEngineRequestParameters(
            name=name,
            force=force,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _DeleteAgentEngineRequestParameters_to_vertex(
                parameter_model
            )
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "{name}".format_map(request_url_dict)
            else:
                path = "{name}"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = self._api_client.request("delete", path, request_dict, http_options)

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _DeleteAgentEngineOperation_from_vertex(response_dict)

        return_value = types.DeleteAgentEngineOperation._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    def delete_memory(
        self,
        *,
        name: str,
        config: Optional[types.DeleteAgentEngineMemoryConfigOrDict] = None,
    ) -> types.DeleteAgentEngineMemoryOperation:
        """Delete an Agent Engine memory.

        Args:
            name (str): Required. The name of the Agent Engine memory to be
              deleted. Format:
              `projects/{project}/locations/{location}/reasoningEngines/{resource_id}/memories/{memory}`.
            config (DeleteAgentEngineMemoryConfig): Optional. Additional
              configurations for deleting the Agent Engine.
        """

        parameter_model = types._DeleteAgentEngineMemoryRequestParameters(
            name=name,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _DeleteAgentEngineMemoryRequestParameters_to_vertex(
                parameter_model
            )
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "{name}".format_map(request_url_dict)
            else:
                path = "{name}"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = self._api_client.request("delete", path, request_dict, http_options)

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _DeleteAgentEngineMemoryOperation_from_vertex(response_dict)

        return_value = types.DeleteAgentEngineMemoryOperation._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    def _generate_memories(
        self,
        *,
        name: str,
        vertex_session_source: Optional[
            types.GenerateMemoriesRequestVertexSessionSourceOrDict
        ] = None,
        direct_contents_source: Optional[
            types.GenerateMemoriesRequestDirectContentsSourceOrDict
        ] = None,
        scope: Optional[dict[str, str]] = None,
        config: Optional[types.GenerateAgentEngineMemoriesConfigOrDict] = None,
    ) -> types.AgentEngineGenerateMemoriesOperation:
        """Generates memories for an Agent Engine."""

        parameter_model = types._GenerateAgentEngineMemoriesRequestParameters(
            name=name,
            vertex_session_source=vertex_session_source,
            direct_contents_source=direct_contents_source,
            scope=scope,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _GenerateAgentEngineMemoriesRequestParameters_to_vertex(
                parameter_model
            )
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "{name}/memories:generate".format_map(request_url_dict)
            else:
                path = "{name}/memories:generate"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = self._api_client.request("post", path, request_dict, http_options)

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _AgentEngineGenerateMemoriesOperation_from_vertex(
                response_dict
            )

        return_value = types.AgentEngineGenerateMemoriesOperation._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    def _get(
        self,
        *,
        name: str,
        config: Optional[types.GetAgentEngineConfigOrDict] = None,
    ) -> types.ReasoningEngine:
        """Get an Agent Engine instance."""

        parameter_model = types._GetAgentEngineRequestParameters(
            name=name,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _GetAgentEngineRequestParameters_to_vertex(parameter_model)
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "{name}".format_map(request_url_dict)
            else:
                path = "{name}"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = self._api_client.request("get", path, request_dict, http_options)

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _ReasoningEngine_from_vertex(response_dict)

        return_value = types.ReasoningEngine._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    def get_memory(
        self,
        *,
        name: str,
        config: Optional[types.GetAgentEngineMemoryConfigOrDict] = None,
    ) -> types.Memory:
        """Gets an agent engine memory.

        Args:
            name (str): Required. A fully-qualified resource name or ID such as
              "projects/123/locations/us-central1/reasoningEngines/456/memories/789"
              or a shortened name such as "reasoningEngines/456/memories/789".
        """

        parameter_model = types._GetAgentEngineMemoryRequestParameters(
            name=name,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _GetAgentEngineMemoryRequestParameters_to_vertex(
                parameter_model
            )
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "{name}".format_map(request_url_dict)
            else:
                path = "{name}"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = self._api_client.request("get", path, request_dict, http_options)

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _Memory_from_vertex(response_dict)

        return_value = types.Memory._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    def _list(
        self, *, config: Optional[types.ListAgentEngineConfigOrDict] = None
    ) -> types.ListReasoningEnginesResponse:
        """Lists Agent Engines."""

        parameter_model = types._ListAgentEngineRequestParameters(
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _ListAgentEngineRequestParameters_to_vertex(parameter_model)
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "reasoningEngines".format_map(request_url_dict)
            else:
                path = "reasoningEngines"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = self._api_client.request("get", path, request_dict, http_options)

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _ListReasoningEnginesResponse_from_vertex(response_dict)

        return_value = types.ListReasoningEnginesResponse._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    def _list_memories(
        self,
        *,
        name: str,
        config: Optional[types.ListAgentEngineMemoryConfigOrDict] = None,
    ) -> types.ListReasoningEnginesMemoriesResponse:
        """Lists Agent Engine memories."""

        parameter_model = types._ListAgentEngineMemoryRequestParameters(
            name=name,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _ListAgentEngineMemoryRequestParameters_to_vertex(
                parameter_model
            )
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "{name}/memories".format_map(request_url_dict)
            else:
                path = "{name}/memories"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = self._api_client.request("get", path, request_dict, http_options)

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _ListReasoningEnginesMemoriesResponse_from_vertex(
                response_dict
            )

        return_value = types.ListReasoningEnginesMemoriesResponse._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    def _get_agent_operation(
        self,
        *,
        operation_name: str,
        config: Optional[types.GetAgentEngineOperationConfigOrDict] = None,
    ) -> types.AgentEngineOperation:
        parameter_model = types._GetAgentEngineOperationParameters(
            operation_name=operation_name,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _GetAgentEngineOperationParameters_to_vertex(parameter_model)
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "{operationName}".format_map(request_url_dict)
            else:
                path = "{operationName}"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = self._api_client.request("get", path, request_dict, http_options)

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _AgentEngineOperation_from_vertex(response_dict)

        return_value = types.AgentEngineOperation._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    def _get_memory_operation(
        self,
        *,
        operation_name: str,
        config: Optional[types.GetAgentEngineOperationConfigOrDict] = None,
    ) -> types.AgentEngineMemoryOperation:
        parameter_model = types._GetAgentEngineMemoryOperationParameters(
            operation_name=operation_name,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _GetAgentEngineMemoryOperationParameters_to_vertex(
                parameter_model
            )
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "{operationName}".format_map(request_url_dict)
            else:
                path = "{operationName}"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = self._api_client.request("get", path, request_dict, http_options)

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _AgentEngineMemoryOperation_from_vertex(response_dict)

        return_value = types.AgentEngineMemoryOperation._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    def _get_generate_memories_operation(
        self,
        *,
        operation_name: str,
        config: Optional[types.GetAgentEngineOperationConfigOrDict] = None,
    ) -> types.AgentEngineGenerateMemoriesOperation:
        parameter_model = types._GetAgentEngineGenerateMemoriesOperationParameters(
            operation_name=operation_name,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _GetAgentEngineGenerateMemoriesOperationParameters_to_vertex(
                parameter_model
            )
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "{operationName}".format_map(request_url_dict)
            else:
                path = "{operationName}"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = self._api_client.request("get", path, request_dict, http_options)

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _AgentEngineGenerateMemoriesOperation_from_vertex(
                response_dict
            )

        return_value = types.AgentEngineGenerateMemoriesOperation._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    def _query(
        self,
        *,
        name: str,
        config: Optional[types.QueryAgentEngineConfigOrDict] = None,
    ) -> types.QueryReasoningEngineResponse:
        """Query an Agent Engine."""

        parameter_model = types._QueryAgentEngineRequestParameters(
            name=name,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _QueryAgentEngineRequestParameters_to_vertex(parameter_model)
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "{name}:query".format_map(request_url_dict)
            else:
                path = "{name}:query"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = self._api_client.request("post", path, request_dict, http_options)

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _QueryReasoningEngineResponse_from_vertex(response_dict)

        return_value = types.QueryReasoningEngineResponse._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    def _retrieve_memories(
        self,
        *,
        name: str,
        scope: dict[str, str],
        similarity_search_params: Optional[
            types.RetrieveMemoriesRequestSimilaritySearchParamsOrDict
        ] = None,
        simple_retrieval_params: Optional[
            types.RetrieveMemoriesRequestSimpleRetrievalParamsOrDict
        ] = None,
        config: Optional[types.RetrieveAgentEngineMemoriesConfigOrDict] = None,
    ) -> types.RetrieveMemoriesResponse:
        """Retrieves memories for an Agent Engine."""

        parameter_model = types._RetrieveAgentEngineMemoriesRequestParameters(
            name=name,
            scope=scope,
            similarity_search_params=similarity_search_params,
            simple_retrieval_params=simple_retrieval_params,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _RetrieveAgentEngineMemoriesRequestParameters_to_vertex(
                parameter_model
            )
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "{name}/memories:retrieve".format_map(request_url_dict)
            else:
                path = "{name}/memories:retrieve"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = self._api_client.request("post", path, request_dict, http_options)

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _RetrieveMemoriesResponse_from_vertex(response_dict)

        return_value = types.RetrieveMemoriesResponse._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    def _update(
        self,
        *,
        name: str,
        config: Optional[types.UpdateAgentEngineConfigOrDict] = None,
    ) -> types.AgentEngineOperation:
        """Updates an Agent Engine."""

        parameter_model = types._UpdateAgentEngineRequestParameters(
            name=name,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _UpdateAgentEngineRequestParameters_to_vertex(
                parameter_model
            )
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "{name}".format_map(request_url_dict)
            else:
                path = "{name}"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = self._api_client.request("patch", path, request_dict, http_options)

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _AgentEngineOperation_from_vertex(response_dict)

        return_value = types.AgentEngineOperation._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    def _update_memory(
        self,
        *,
        name: str,
        fact: Optional[str] = None,
        scope: Optional[dict[str, str]] = None,
        config: Optional[types.UpdateAgentEngineMemoryConfigOrDict] = None,
    ) -> types.AgentEngineMemoryOperation:
        """Updates an Agent Engine memory."""

        parameter_model = types._UpdateAgentEngineMemoryRequestParameters(
            name=name,
            fact=fact,
            scope=scope,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _UpdateAgentEngineMemoryRequestParameters_to_vertex(
                parameter_model
            )
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "{name}".format_map(request_url_dict)
            else:
                path = "{name}"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = self._api_client.request("patch", path, request_dict, http_options)

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _AgentEngineMemoryOperation_from_vertex(response_dict)

        return_value = types.AgentEngineMemoryOperation._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    def _list_pager(
        self, *, config: Optional[types.ListAgentEngineConfigOrDict] = None
    ) -> Pager[types.ReasoningEngine]:
        return Pager(
            "reasoning_engines",
            self._list,
            self._list(config=config),
            config,
        )

    def get(
        self,
        *,
        name: str,
        config: Optional[types.GetAgentEngineConfigOrDict] = None,
    ) -> types.AgentEngine:
        """Gets an agent engine.

        Args:
            name (str): Required. A fully-qualified resource name or ID such as
              "projects/123/locations/us-central1/reasoningEngines/456" or a
              shortened name such as "reasoningEngines/456".
        """
        agent = types.AgentEngine(
            api_client=self,
            api_async_client=AsyncAgentEngines(api_client_=self._api_client),
            api_resource=self._get(name=name, config=config),
        )
        return self._register_api_methods(agent=agent)

    def create(
        self,
        *,
        agent_engine: Any = None,
        config: Optional[types.AgentEngineConfigOrDict] = None,
    ) -> types.AgentEngine:
        """Creates an agent engine.

        The Agent Engine will be an instance of the `agent_engine` that
        was passed in, running remotely on Vertex AI.

        Sample ``src_dir`` contents (e.g. ``./user_src_dir``):

        .. code-block:: python

            user_src_dir/
            |-- main.py
            |-- requirements.txt
            |-- user_code/
            |   |-- utils.py
            |   |-- ...
            |-- ...

        To build an Agent Engine with the above files, run:

        .. code-block:: python

            client = vertexai.Client(
                project="your-project",
                location="us-central1",
            )
            remote_agent = client.agent_engines.create(
                agent_engine=local_agent,
                config=dict(
                    requirements=[
                        # I.e. the PyPI dependencies listed in requirements.txt
                        "google-cloud-aiplatform[agent_engines,adk]",
                        ...
                    ],
                    extra_packages=[
                        "./user_src_dir/main.py", # a single file
                        "./user_src_dir/user_code", # a directory
                        ...
                    ],
                ),
            )

        Args:
            agent_engine (Any): Optional. The Agent Engine to be created. If not
              specified, this will correspond to a lightweight instance that
              cannot be queried (but can be updated to future instances that can
              be queried).
            config (AgentEngineConfig): Optional. The configurations to use for
              creating the Agent Engine.

        Returns:
            AgentEngine: The created Agent Engine instance.

        Raises:
            ValueError: If the `project` was not set using `client.Client`.
            ValueError: If the `location` was not set using `client.Client`.
            ValueError: If `config.staging_bucket` was not set when
            `agent_engine`
            is specified.
            ValueError: If `config.staging_bucket` does not start with "gs://".
            ValueError: If `config.extra_packages` is specified but
            `agent_engine`
            is None.
            ValueError: If `config.requirements` is specified but `agent_engine`
            is
            None.
            ValueError: If `config.env_vars` has a dictionary entry that does
            not
            correspond to an environment variable value or a SecretRef.
            TypeError: If `config.env_vars` is not a dictionary.
            FileNotFoundError: If `config.extra_packages` includes a file or
            directory that does not exist.
            IOError: If ``config.requirements` is a string that corresponds to a
            nonexistent file.
        """
        if config is None:
            config = {}
        if isinstance(config, dict):
            config = types.AgentEngineConfig.model_validate(config)
        elif not isinstance(config, types.AgentEngineConfig):
            raise TypeError(
                "config must be a dict or AgentEngineConfig, but got"
                f" {type(config)}."
            )
        api_config = self._create_config(
            mode="create",
            agent_engine=agent_engine,
            staging_bucket=config.staging_bucket,
            requirements=config.requirements,
            display_name=config.display_name,
            description=config.description,
            gcs_dir_name=config.gcs_dir_name,
            extra_packages=config.extra_packages,
            env_vars=config.env_vars,
        )
        operation = self._create(config=api_config)
        # TODO: Use a more specific link.
        logger.info(
            "View progress and logs at"
            f" https://console.cloud.google.com/logs/query?project={self._api_client.project}."
        )
        if agent_engine is None:
            poll_interval_seconds = 1  # Lightweight agent engine resource creation.
        else:
            poll_interval_seconds = 10
        operation = self._await_operation(
            operation_name=operation.name,
            poll_interval_seconds=poll_interval_seconds,
        )

        agent = types.AgentEngine(
            api_client=self,
            api_async_client=AsyncAgentEngines(api_client_=self._api_client),
            api_resource=operation.response,
        )
        logger.info("Agent Engine created. To use it in another session:")
        logger.info(
            f"agent_engine=client.agent_engines.get('{agent.api_resource.name}')"
        )
        if agent_engine is not None:
            # If the user did not provide an agent_engine (e.g. lightweight
            # provisioning), it will not have any API methods registered.
            agent = self._register_api_methods(agent=agent)
        return agent

    def _create_config(
        self,
        *,
        mode: str,
        agent_engine: Any = None,
        staging_bucket: Optional[str] = None,
        requirements: Optional[Union[str, Sequence[str]]] = None,
        display_name: Optional[str] = None,
        description: Optional[str] = None,
        gcs_dir_name: Optional[str] = None,
        extra_packages: Optional[Sequence[str]] = None,
        env_vars: Optional[dict[str, Union[str, Any]]] = None,
    ):
        import sys
        from vertexai.agent_engines import _agent_engines
        from vertexai.agent_engines import _utils

        config = {}
        update_masks = []
        if mode not in ["create", "update"]:
            raise ValueError(f"Unsupported mode: {mode}")
        if agent_engine is None:
            if requirements is not None:
                raise ValueError("requirements must be None if agent_engine is None.")
            if extra_packages is not None:
                raise ValueError("extra_packages must be None if agent_engine is None.")
        if display_name is not None:
            update_masks.append("display_name")
            config["display_name"] = display_name
        if description is not None:
            update_masks.append("description")
            config["description"] = description
        if agent_engine is not None:
            sys_version = f"{sys.version_info.major}.{sys.version_info.minor}"
            gcs_dir_name = gcs_dir_name or _agent_engines._DEFAULT_GCS_DIR_NAME
            agent_engine = _agent_engines._validate_agent_engine_or_raise(
                agent_engine=agent_engine, logger=logger
            )
            _agent_engines._validate_staging_bucket_or_raise(staging_bucket)
            requirements = _agent_engines._validate_requirements_or_raise(
                agent_engine=agent_engine,
                requirements=requirements,
                logger=logger,
            )
            extra_packages = _agent_engines._validate_extra_packages_or_raise(
                extra_packages
            )
            # Prepares the Agent Engine for creation/update in Vertex AI. This
            # involves packaging and uploading the artifacts for agent_engine,
            # requirements and extra_packages to `staging_bucket/gcs_dir_name`.
            _agent_engines._prepare(
                agent_engine=agent_engine,
                requirements=requirements,
                project=self._api_client.project,
                location=self._api_client.location,
                staging_bucket=staging_bucket,
                gcs_dir_name=gcs_dir_name,
                extra_packages=extra_packages,
                logger=logger,
            )
            # Update the package spec.
            update_masks.append("spec.package_spec.pickle_object_gcs_uri")
            package_spec = {
                "python_version": sys_version,
                "pickle_object_gcs_uri": "{}/{}/{}".format(
                    staging_bucket,
                    gcs_dir_name,
                    _agent_engines._BLOB_FILENAME,
                ),
            }
            if extra_packages:
                update_masks.append("spec.package_spec.dependency_files_gcs_uri")
                package_spec["dependency_files_gcs_uri"] = "{}/{}/{}".format(
                    staging_bucket,
                    gcs_dir_name,
                    _agent_engines._EXTRA_PACKAGES_FILE,
                )
            if requirements:
                update_masks.append("spec.package_spec.requirements_gcs_uri")
                package_spec["requirements_gcs_uri"] = "{}/{}/{}".format(
                    staging_bucket,
                    gcs_dir_name,
                    _agent_engines._REQUIREMENTS_FILE,
                )
            agent_engine_spec = {"package_spec": package_spec}
            if env_vars is not None:
                (
                    deployment_spec,
                    deployment_update_masks,
                ) = self._generate_deployment_spec_or_raise(env_vars=env_vars)
                update_masks.extend(deployment_update_masks)
                agent_engine_spec["deployment_spec"] = deployment_spec
            class_methods = _agent_engines._generate_class_methods_spec_or_raise(
                agent_engine=agent_engine,
                operations=_agent_engines._get_registered_operations(agent_engine),
                logger=logger,
            )
            agent_engine_spec["class_methods"] = [
                _utils.to_dict(class_method) for class_method in class_methods
            ]
            update_masks.append("spec.class_methods")
            agent_engine_spec["agent_framework"] = _agent_engines._get_agent_framework(
                agent_engine
            )
            update_masks.append("spec.agent_framework")
            config["spec"] = agent_engine_spec
        if update_masks and mode == "update":
            config["update_mask"] = ",".join(update_masks)
        return config

    def _generate_deployment_spec_or_raise(
        self,
        *,
        env_vars: Optional[dict[str, Union[str, Any]]] = None,
    ):
        deployment_spec: dict[str, Any] = {}
        update_masks = []
        if env_vars:
            deployment_spec["env"] = []
            deployment_spec["secret_env"] = []
            if isinstance(env_vars, dict):
                self._update_deployment_spec_with_env_vars_dict_or_raise(
                    deployment_spec=deployment_spec,
                    env_vars=env_vars,
                )
            else:
                raise TypeError(f"env_vars must be a dict, but got {type(env_vars)}.")
            if deployment_spec.get("env"):
                update_masks.append("spec.deployment_spec.env")
            if deployment_spec.get("secret_env"):
                update_masks.append("spec.deployment_spec.secret_env")
        return deployment_spec, update_masks

    def _update_deployment_spec_with_env_vars_dict_or_raise(
        self,
        *,
        deployment_spec: dict[str, Any],
        env_vars: dict[str, Any],
    ) -> None:
        for key, value in env_vars.items():
            if isinstance(value, dict):
                if "secret_env" not in deployment_spec:
                    deployment_spec["secret_env"] = []
                deployment_spec["secret_env"].append({"name": key, "secret_ref": value})
            elif isinstance(value, str):
                if "env" not in deployment_spec:
                    deployment_spec["env"] = []
                deployment_spec["env"].append({"name": key, "value": value})
            else:
                raise TypeError(
                    f"Unknown value type in env_vars for {key}. "
                    f"Must be a str or SecretRef: {value}"
                )

    def _await_operation(
        self,
        *,
        operation_name: str,
        poll_interval_seconds: int = 10,
        get_operation_fn: Optional[Callable[[str], Any]] = None,
    ) -> Any:
        """Waits for the operation for creating an agent engine to complete.

        Args:
            operation_name (str): Required. The name of the operation for
              creating the Agent Engine.
            poll_interval_seconds (int): The number of seconds to wait between
              each poll.
            get_operation_fn (Callable[[str], Any]): Optional. The function to
              use for getting the operation. If not provided,
              `self._get_agent_operation` will be used.

        Returns:
            The operation that has completed (i.e. `operation.done==True`).
        """
        if get_operation_fn is None:
            get_operation_fn = self._get_agent_operation
        operation = get_operation_fn(operation_name=operation_name)
        while not operation.done:
            time.sleep(poll_interval_seconds)
            operation = get_operation_fn(operation_name=operation.name)

        return operation

    def _register_api_methods(self, *, agent: types.AgentEngine) -> types.AgentEngine:
        """Registers the API methods for the agent engine."""
        from vertexai.agent_engines import _agent_engines

        try:
            _agent_engines._register_api_methods_or_raise(
                agent,
                wrap_operation_fn={
                    "": _agent_engines_utils._wrap_query_operation,
                    "async": _agent_engines_utils._wrap_async_query_operation,
                    "stream": _agent_engines_utils._wrap_stream_query_operation,
                    "async_stream": _agent_engines_utils._wrap_async_stream_query_operation,
                },
            )
        except Exception as e:
            logger.warning(
                _agent_engines._FAILED_TO_REGISTER_API_METHODS_WARNING_TEMPLATE,
                e,
            )
        return agent

    def list(
        self, *, config: Optional[types.ListAgentEngineConfigOrDict] = None
    ) -> Iterator[types.AgentEngine]:
        """List all instances of Agent Engine matching the filter.

        Example Usage:

        .. code-block:: python
            import vertexai

            client = vertexai.Client(project="my_project",
            location="us-central1")
            for agent in client.agent_engines.list(
                config={"filter": "'display_name="My Custom Agent"'},
            ):
                print(agent.api_resource.name)

        Args:
            config (ListAgentEngineConfig): Optional. The config (e.g. filter)
              for the agents to be listed.

        Returns:
            Iterable[AgentEngine]: An iterable of Agent Engines matching the
            filter.
        """

        for reasoning_engine in self._list_pager(config=config):
            yield types.AgentEngine(
                api_client=self,
                api_async_client=AsyncAgentEngines(api_client_=self._api_client),
                api_resource=reasoning_engine,
            )

    def update(
        self,
        *,
        name: str,
        agent_engine: Any = None,
        config: types.AgentEngineConfigOrDict,
    ) -> types.AgentEngine:
        """Updates an existing Agent Engine.

        This method updates the configuration of an existing Agent Engine
        running
        remotely, which is identified by its name.

        Args:
            name (str): Required. A fully-qualified resource name or ID such as
              "projects/123/locations/us-central1/reasoningEngines/456" or a
              shortened name such as "reasoningEngines/456".
            agent_engine (Any): Optional. The instance to be used as the updated
              Agent Engine. If it is not specified, the existing instance will
              be used.
            config (AgentEngineConfig): Optional. The configurations to use for
              updating the Agent Engine.

        Returns:
            AgentEngine: The updated Agent Engine.

        Raises:
          ValueError: If the `project` was not set using `client.Client`.
          ValueError: If the `location` was not set using `client.Client`.
          ValueError: If `config.staging_bucket` was not set when `agent_engine`
          is specified.
          ValueError: If `config.staging_bucket` does not start with "gs://".
          ValueError: If `config.extra_packages` is specified but `agent_engine`
          is None.
          ValueError: If `config.requirements` is specified but `agent_engine`
          is
          None.
          ValueError: If `config.env_vars` has a dictionary entry that does not
          correspond to an environment variable value or a SecretRef.
          TypeError: If `config.env_vars` is not a dictionary.
          FileNotFoundError: If `config.extra_packages` includes a file or
          directory that does not exist.
          IOError: If `config.requirements` is a string that corresponds to a
          nonexistent file.
        """
        if isinstance(config, dict):
            config = types.AgentEngineConfig.model_validate(config)
        elif not isinstance(config, types.AgentEngineConfig):
            raise TypeError(
                "config must be a dict or AgentEngineConfig, but got"
                f" {type(config)}."
            )
        api_config = self._create_config(
            mode="update",
            agent_engine=agent_engine,
            staging_bucket=config.staging_bucket,
            requirements=config.requirements,
            display_name=config.display_name,
            description=config.description,
            gcs_dir_name=config.gcs_dir_name,
            extra_packages=config.extra_packages,
            env_vars=config.env_vars,
        )
        operation = self._update(name=name, config=api_config)
        logger.info(
            "View progress and logs at"
            f" https://console.cloud.google.com/logs/query?project={self._api_client.project}."
        )
        operation = self._await_operation(operation_name=operation.name)
        agent = types.AgentEngine(
            api_client=self,
            api_async_client=AsyncAgentEngines(api_client_=self._api_client),
            api_resource=operation.response,
        )
        logger.info("Agent Engine updated. To use it in another session:")
        logger.info(
            f"agent_engine=client.agent_engines.get('{agent.api_resource.name}')"
        )
        return self._register_api_methods(agent=agent)

    def _stream_query(
        self,
        *,
        name: str,
        config: Optional[types.QueryAgentEngineConfigOrDict] = None,
    ) -> Iterator[Any]:
        """Streams the response of the agent engine."""
        parameter_model = types._QueryAgentEngineRequestParameters(
            name=name,
            config=config,
        )
        request_dict = _QueryAgentEngineRequestParameters_to_vertex(parameter_model)
        request_url_dict = request_dict.get("_url")
        if request_url_dict:
            path = "{name}:streamQuery?alt=sse".format_map(request_url_dict)
        else:
            path = "{name}:streamQuery?alt=sse"
        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)
        http_options: Optional[genai_types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)
        for response in self._api_client.request_streamed(
            "post", path, request_dict, http_options
        ):
            yield response

    def create_memory(
        self,
        *,
        name: str,
        fact: str,
        scope: dict[str, str],
        config: Optional[types.AgentEngineMemoryConfigOrDict] = None,
    ) -> types.AgentEngineMemoryOperation:
        """Creates a new memory in the Agent Engine.

        Args:
            name (str): Required. The name of the memory to create.
            fact (str): Required. The fact to be stored in the memory.
            scope (dict[str, str]): Required. The scope of the memory. For
              example, {"user_id": "123"}.
            config (AgentEngineMemoryConfigOrDict): Optional. The configuration
              for the memory.

        Returns:
            AgentEngineMemoryOperation: The operation for creating the memory.
        """
        operation = self._create_memory(
            name=name,
            fact=fact,
            scope=scope,
            config=config,
        )
        if config is None:
            config = types.AgentEngineMemoryConfig()
        elif isinstance(config, dict):
            config = types.AgentEngineMemoryConfig.model_validate(config)
        if config.wait_for_completion:
            if not operation.done:
                operation = self._await_operation(
                    operation_name=operation.name,
                    get_operation_fn=self._get_memory_operation,
                )
            # We need to make a call to get the memory because the operation
            # response might not contain the relevant fields.
            if not operation.response:
                raise ValueError("Error retrieving memory.")
            operation.response = self.get_memory(name=operation.response.name)
        return operation

    def generate_memories(
        self,
        *,
        name: str,
        vertex_session_source: Optional[
            types.GenerateMemoriesRequestVertexSessionSourceOrDict
        ] = None,
        direct_contents_source: Optional[
            types.GenerateMemoriesRequestDirectContentsSourceOrDict
        ] = None,
        scope: Optional[dict[str, str]] = None,
        config: Optional[types.GenerateAgentEngineMemoriesConfigOrDict] = None,
    ) -> types.AgentEngineGenerateMemoriesOperation:
        """Generates memories for the agent engine.

        Args:
            name (str): Required. The name of the agent engine to generate
              memories for.
            vertex_session_source (GenerateMemoriesRequestVertexSessionSource):
              Optional. The vertex session source to use for generating
              memories. Either vertex_session_source or direct_contents_source
              must be specified, but not both. direct_contents_source
              (GenerateMemoriesRequestDirectContentsSource): Optional. The
              direct contents source to use for generating memories. Either
              vertex_session_source or direct_contents_source must be specified,
              but not both.
            scope (dict[str, str]): Optional. The scope of the memories to
              generate. This is optional if vertex_session_source is used,
              otherwise it must be specified.
            config (GenerateMemoriesConfig): Optional. The configuration for the
              memories to generate.

        Returns:
            AgentEngineGenerateMemoriesOperation:
                The operation for generating the memories.
        """
        operation = self._generate_memories(
            name=name,
            vertex_session_source=vertex_session_source,
            direct_contents_source=direct_contents_source,
            scope=scope,
            config=config,
        )
        if config is None:
            config = types.GenerateAgentEngineMemoriesConfig()
        elif isinstance(config, dict):
            config = types.GenerateAgentEngineMemoriesConfig.model_validate(config)
        if config.wait_for_completion and not operation.done:
            return self._await_operation(
                operation_name=operation.name,
                get_operation_fn=self._get_generate_memories_operation,
            )
        return operation

    def list_memories(
        self,
        *,
        name: str,
        config: Optional[types.ListAgentEngineMemoryConfigOrDict] = None,
    ) -> Iterator[types.Memory]:
        """Lists Agent Engine memories.

        Args:
            name (str): Required. The name of the agent engine to list memories
              for.
            config (ListAgentEngineMemoryConfig): Optional. The configuration
              for the memories to list.

        Returns:
            Iterable[Memory]: An iterable of memories.
        """
        return Pager(
            "memories",
            self._list_memories,
            self._list_memories(name=name, config=config),
            config,
        )

    def retrieve_memories(
        self,
        *,
        name: str,
        scope: dict[str, str],
        similarity_search_params: Optional[
            types.RetrieveMemoriesRequestSimilaritySearchParamsOrDict
        ] = None,
        simple_retrieval_params: Optional[
            types.RetrieveMemoriesRequestSimpleRetrievalParamsOrDict
        ] = None,
        config: Optional[types.RetrieveAgentEngineMemoriesConfigOrDict] = None,
    ) -> Iterator[types.RetrieveMemoriesResponseRetrievedMemory]:
        """Retrieves memories for the agent.

        Args:
            name (str): Required. The name of the agent engine to retrieve
              memories for.
            scope (dict[str, str]): Required. The scope of the memories to
              retrieve. For example, {"user_id": "123"}.
              similarity_search_params
              (RetrieveMemoriesRequestSimilaritySearchParams): Optional. The
              similarity search parameters to use for retrieving memories.
              simple_retrieval_params
              (RetrieveMemoriesRequestSimpleRetrievalParams): Optional. The
              simple retrieval parameters to use for retrieving memories.
            config (RetrieveAgentEngineMemoriesConfig): Optional. The
              configuration for the memories to retrieve.

        Returns:
            Iterator[RetrieveMemoriesResponseRetrievedMemory]: An iterable of
                retrieved memories.
        """
        return Pager(
            "retrieved_memories",
            lambda config: self._retrieve_memories(
                name=name,
                similarity_search_params=similarity_search_params,
                simple_retrieval_params=simple_retrieval_params,
                scope=scope,
                config=config,
            ),
            self._retrieve_memories(
                name=name,
                similarity_search_params=similarity_search_params,
                simple_retrieval_params=simple_retrieval_params,
                scope=scope,
                config=config,
            ),
            config,
        )


class AsyncAgentEngines(_api_module.BaseModule):
    async def _create(
        self, *, config: Optional[types.CreateAgentEngineConfigOrDict] = None
    ) -> types.AgentEngineOperation:
        """Creates a new Agent Engine."""

        parameter_model = types._CreateAgentEngineRequestParameters(
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _CreateAgentEngineRequestParameters_to_vertex(
                parameter_model
            )
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "reasoningEngines".format_map(request_url_dict)
            else:
                path = "reasoningEngines"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = await self._api_client.async_request(
            "post", path, request_dict, http_options
        )

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _AgentEngineOperation_from_vertex(response_dict)

        return_value = types.AgentEngineOperation._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    async def _create_memory(
        self,
        *,
        name: str,
        fact: str,
        scope: dict[str, str],
        config: Optional[types.AgentEngineMemoryConfigOrDict] = None,
    ) -> types.AgentEngineMemoryOperation:
        """Creates a new memory in the Agent Engine."""

        parameter_model = types._CreateAgentEngineMemoryRequestParameters(
            name=name,
            fact=fact,
            scope=scope,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _CreateAgentEngineMemoryRequestParameters_to_vertex(
                parameter_model
            )
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "{name}/memories".format_map(request_url_dict)
            else:
                path = "{name}/memories"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = await self._api_client.async_request(
            "post", path, request_dict, http_options
        )

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _AgentEngineMemoryOperation_from_vertex(response_dict)

        return_value = types.AgentEngineMemoryOperation._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    async def delete(
        self,
        *,
        name: str,
        force: Optional[bool] = None,
        config: Optional[types.DeleteAgentEngineConfigOrDict] = None,
    ) -> types.DeleteAgentEngineOperation:
        """Delete an Agent Engine resource.

        Args:
            name (str): Required. The name of the Agent Engine to be deleted.
              Format:
                `projects/{project}/locations/{location}/reasoningEngines/{resource_id}`
                or `reasoningEngines/{resource_id}`.
            force (bool): Optional. If set to True, child resources will also be
              deleted. Otherwise, the request will fail with FAILED_PRECONDITION
              error when the Agent Engine has undeleted child resources.
              Defaults to False.
            config (DeleteAgentEngineConfig): Optional. Additional
              configurations for deleting the Agent Engine.
        """

        parameter_model = types._DeleteAgentEngineRequestParameters(
            name=name,
            force=force,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _DeleteAgentEngineRequestParameters_to_vertex(
                parameter_model
            )
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "{name}".format_map(request_url_dict)
            else:
                path = "{name}"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = await self._api_client.async_request(
            "delete", path, request_dict, http_options
        )

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _DeleteAgentEngineOperation_from_vertex(response_dict)

        return_value = types.DeleteAgentEngineOperation._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    async def delete_memory(
        self,
        *,
        name: str,
        config: Optional[types.DeleteAgentEngineMemoryConfigOrDict] = None,
    ) -> types.DeleteAgentEngineMemoryOperation:
        """Delete an Agent Engine memory.

        Args:
            name (str): Required. The name of the Agent Engine memory to be
              deleted. Format:
              `projects/{project}/locations/{location}/reasoningEngines/{resource_id}/memories/{memory}`.
            config (DeleteAgentEngineMemoryConfig): Optional. Additional
              configurations for deleting the Agent Engine.
        """

        parameter_model = types._DeleteAgentEngineMemoryRequestParameters(
            name=name,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _DeleteAgentEngineMemoryRequestParameters_to_vertex(
                parameter_model
            )
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "{name}".format_map(request_url_dict)
            else:
                path = "{name}"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = await self._api_client.async_request(
            "delete", path, request_dict, http_options
        )

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _DeleteAgentEngineMemoryOperation_from_vertex(response_dict)

        return_value = types.DeleteAgentEngineMemoryOperation._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    async def _generate_memories(
        self,
        *,
        name: str,
        vertex_session_source: Optional[
            types.GenerateMemoriesRequestVertexSessionSourceOrDict
        ] = None,
        direct_contents_source: Optional[
            types.GenerateMemoriesRequestDirectContentsSourceOrDict
        ] = None,
        scope: Optional[dict[str, str]] = None,
        config: Optional[types.GenerateAgentEngineMemoriesConfigOrDict] = None,
    ) -> types.AgentEngineGenerateMemoriesOperation:
        """Generates memories for an Agent Engine."""

        parameter_model = types._GenerateAgentEngineMemoriesRequestParameters(
            name=name,
            vertex_session_source=vertex_session_source,
            direct_contents_source=direct_contents_source,
            scope=scope,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _GenerateAgentEngineMemoriesRequestParameters_to_vertex(
                parameter_model
            )
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "{name}/memories:generate".format_map(request_url_dict)
            else:
                path = "{name}/memories:generate"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = await self._api_client.async_request(
            "post", path, request_dict, http_options
        )

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _AgentEngineGenerateMemoriesOperation_from_vertex(
                response_dict
            )

        return_value = types.AgentEngineGenerateMemoriesOperation._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    async def _get(
        self,
        *,
        name: str,
        config: Optional[types.GetAgentEngineConfigOrDict] = None,
    ) -> types.ReasoningEngine:
        """Get an Agent Engine instance."""

        parameter_model = types._GetAgentEngineRequestParameters(
            name=name,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _GetAgentEngineRequestParameters_to_vertex(parameter_model)
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "{name}".format_map(request_url_dict)
            else:
                path = "{name}"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = await self._api_client.async_request(
            "get", path, request_dict, http_options
        )

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _ReasoningEngine_from_vertex(response_dict)

        return_value = types.ReasoningEngine._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    async def get_memory(
        self,
        *,
        name: str,
        config: Optional[types.GetAgentEngineMemoryConfigOrDict] = None,
    ) -> types.Memory:
        """Gets an agent engine memory.

        Args:
            name (str): Required. A fully-qualified resource name or ID such as
              "projects/123/locations/us-central1/reasoningEngines/456/memories/789"
              or a shortened name such as "reasoningEngines/456/memories/789".
        """

        parameter_model = types._GetAgentEngineMemoryRequestParameters(
            name=name,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _GetAgentEngineMemoryRequestParameters_to_vertex(
                parameter_model
            )
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "{name}".format_map(request_url_dict)
            else:
                path = "{name}"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = await self._api_client.async_request(
            "get", path, request_dict, http_options
        )

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _Memory_from_vertex(response_dict)

        return_value = types.Memory._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    async def _list(
        self, *, config: Optional[types.ListAgentEngineConfigOrDict] = None
    ) -> types.ListReasoningEnginesResponse:
        """Lists Agent Engines."""

        parameter_model = types._ListAgentEngineRequestParameters(
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _ListAgentEngineRequestParameters_to_vertex(parameter_model)
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "reasoningEngines".format_map(request_url_dict)
            else:
                path = "reasoningEngines"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = await self._api_client.async_request(
            "get", path, request_dict, http_options
        )

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _ListReasoningEnginesResponse_from_vertex(response_dict)

        return_value = types.ListReasoningEnginesResponse._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    async def _list_memories(
        self,
        *,
        name: str,
        config: Optional[types.ListAgentEngineMemoryConfigOrDict] = None,
    ) -> types.ListReasoningEnginesMemoriesResponse:
        """Lists Agent Engine memories."""

        parameter_model = types._ListAgentEngineMemoryRequestParameters(
            name=name,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _ListAgentEngineMemoryRequestParameters_to_vertex(
                parameter_model
            )
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "{name}/memories".format_map(request_url_dict)
            else:
                path = "{name}/memories"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = await self._api_client.async_request(
            "get", path, request_dict, http_options
        )

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _ListReasoningEnginesMemoriesResponse_from_vertex(
                response_dict
            )

        return_value = types.ListReasoningEnginesMemoriesResponse._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    async def _get_agent_operation(
        self,
        *,
        operation_name: str,
        config: Optional[types.GetAgentEngineOperationConfigOrDict] = None,
    ) -> types.AgentEngineOperation:
        parameter_model = types._GetAgentEngineOperationParameters(
            operation_name=operation_name,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _GetAgentEngineOperationParameters_to_vertex(parameter_model)
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "{operationName}".format_map(request_url_dict)
            else:
                path = "{operationName}"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = await self._api_client.async_request(
            "get", path, request_dict, http_options
        )

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _AgentEngineOperation_from_vertex(response_dict)

        return_value = types.AgentEngineOperation._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    async def _get_memory_operation(
        self,
        *,
        operation_name: str,
        config: Optional[types.GetAgentEngineOperationConfigOrDict] = None,
    ) -> types.AgentEngineMemoryOperation:
        parameter_model = types._GetAgentEngineMemoryOperationParameters(
            operation_name=operation_name,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _GetAgentEngineMemoryOperationParameters_to_vertex(
                parameter_model
            )
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "{operationName}".format_map(request_url_dict)
            else:
                path = "{operationName}"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = await self._api_client.async_request(
            "get", path, request_dict, http_options
        )

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _AgentEngineMemoryOperation_from_vertex(response_dict)

        return_value = types.AgentEngineMemoryOperation._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    async def _get_generate_memories_operation(
        self,
        *,
        operation_name: str,
        config: Optional[types.GetAgentEngineOperationConfigOrDict] = None,
    ) -> types.AgentEngineGenerateMemoriesOperation:
        parameter_model = types._GetAgentEngineGenerateMemoriesOperationParameters(
            operation_name=operation_name,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _GetAgentEngineGenerateMemoriesOperationParameters_to_vertex(
                parameter_model
            )
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "{operationName}".format_map(request_url_dict)
            else:
                path = "{operationName}"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = await self._api_client.async_request(
            "get", path, request_dict, http_options
        )

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _AgentEngineGenerateMemoriesOperation_from_vertex(
                response_dict
            )

        return_value = types.AgentEngineGenerateMemoriesOperation._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    async def _query(
        self,
        *,
        name: str,
        config: Optional[types.QueryAgentEngineConfigOrDict] = None,
    ) -> types.QueryReasoningEngineResponse:
        """Query an Agent Engine."""

        parameter_model = types._QueryAgentEngineRequestParameters(
            name=name,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _QueryAgentEngineRequestParameters_to_vertex(parameter_model)
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "{name}:query".format_map(request_url_dict)
            else:
                path = "{name}:query"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = await self._api_client.async_request(
            "post", path, request_dict, http_options
        )

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _QueryReasoningEngineResponse_from_vertex(response_dict)

        return_value = types.QueryReasoningEngineResponse._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    async def _retrieve_memories(
        self,
        *,
        name: str,
        scope: dict[str, str],
        similarity_search_params: Optional[
            types.RetrieveMemoriesRequestSimilaritySearchParamsOrDict
        ] = None,
        simple_retrieval_params: Optional[
            types.RetrieveMemoriesRequestSimpleRetrievalParamsOrDict
        ] = None,
        config: Optional[types.RetrieveAgentEngineMemoriesConfigOrDict] = None,
    ) -> types.RetrieveMemoriesResponse:
        """Retrieves memories for an Agent Engine."""

        parameter_model = types._RetrieveAgentEngineMemoriesRequestParameters(
            name=name,
            scope=scope,
            similarity_search_params=similarity_search_params,
            simple_retrieval_params=simple_retrieval_params,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _RetrieveAgentEngineMemoriesRequestParameters_to_vertex(
                parameter_model
            )
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "{name}/memories:retrieve".format_map(request_url_dict)
            else:
                path = "{name}/memories:retrieve"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = await self._api_client.async_request(
            "post", path, request_dict, http_options
        )

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _RetrieveMemoriesResponse_from_vertex(response_dict)

        return_value = types.RetrieveMemoriesResponse._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    async def _update(
        self,
        *,
        name: str,
        config: Optional[types.UpdateAgentEngineConfigOrDict] = None,
    ) -> types.AgentEngineOperation:
        """Updates an Agent Engine."""

        parameter_model = types._UpdateAgentEngineRequestParameters(
            name=name,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _UpdateAgentEngineRequestParameters_to_vertex(
                parameter_model
            )
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "{name}".format_map(request_url_dict)
            else:
                path = "{name}"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = await self._api_client.async_request(
            "patch", path, request_dict, http_options
        )

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _AgentEngineOperation_from_vertex(response_dict)

        return_value = types.AgentEngineOperation._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    async def _update_memory(
        self,
        *,
        name: str,
        fact: Optional[str] = None,
        scope: Optional[dict[str, str]] = None,
        config: Optional[types.UpdateAgentEngineMemoryConfigOrDict] = None,
    ) -> types.AgentEngineMemoryOperation:
        """Updates an Agent Engine memory."""

        parameter_model = types._UpdateAgentEngineMemoryRequestParameters(
            name=name,
            fact=fact,
            scope=scope,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _UpdateAgentEngineMemoryRequestParameters_to_vertex(
                parameter_model
            )
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "{name}".format_map(request_url_dict)
            else:
                path = "{name}"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = await self._api_client.async_request(
            "patch", path, request_dict, http_options
        )

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _AgentEngineMemoryOperation_from_vertex(response_dict)

        return_value = types.AgentEngineMemoryOperation._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value
