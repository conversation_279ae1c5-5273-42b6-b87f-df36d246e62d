#
services:
  dev:
    privileged: true
    build:
      context: ${OPENHANDS_WORKSPACE:-../../}
      dockerfile: ./containers/dev/Dockerfile
    image: openhands:dev
    container_name: openhands-dev
    environment:
      - BACKEND_HOST=${BACKEND_HOST:-"0.0.0.0"}
      - SANDBOX_API_HOSTNAME=host.docker.internal
      #
      - SANDBOX_RUNTIME_CONTAINER_IMAGE=${SANDBOX_RUNTIME_CONTAINER_IMAGE:-ghcr.io/all-hands-ai/runtime:0.31-nikolaik}
      - SANDBOX_USER_ID=${SANDBOX_USER_ID:-1234}
      - WORKSPACE_MOUNT_PATH=${WORKSPACE_BASE:-$PWD/workspace}
    ports:
      - "3000:3000"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ${WORKSPACE_BASE:-$PWD/workspace}:/opt/workspace_base
      # source code
      - ${OPENHANDS_WORKSPACE:-../../}:/app
      # host credentials
      - $HOME/.git-credentials:/root/.git-credentials:ro
      - $HOME/.gitconfig:/root/.gitconfig:ro
      - $HOME/.npmrc:/root/.npmrc:ro
      # cache
      - cache-data:/root/.cache
    pull_policy: never
    stdin_open: true
    tty: true

##
volumes:
  cache-data:
