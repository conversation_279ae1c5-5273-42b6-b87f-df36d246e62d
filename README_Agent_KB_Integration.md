# Agent-KB统计服务器集成

基于Agent-KB架构对stats_mcp_server_official.py进行深度改造，实现智能化的统计查询服务。

## 🚀 主要改进

### 1. 配置管理系统
- **灵活配置**: 支持环境变量、TOML文件、函数参数多种配置方式
- **配置验证**: 自动验证配置参数的有效性
- **类型转换**: 智能处理不同类型的配置值

```python
# 环境变量配置
export STATS_API_URL="https://your-server.com/"
export STATS_API_USERNAME="your_username"
export STATS_API_PASSWORD="your_password"

# 或使用TOML配置文件
[stats_server]
api_url = "https://your-server.com/"
api_username = "your_username"
api_password = "your_password"
```

### 2. 微代理(MicroAgent)系统
- **智能触发**: 基于关键词自动触发相关知识代理
- **知识分类**: 统计知识、API文档、错误处理、查询优化四大类代理
- **上下文感知**: 根据查询内容提供精准的帮助信息

```python
# 自动触发相关代理
agents = microagent_manager.find_relevant_agents("查询TCP流量统计")
# 返回: [{"name": "stats_knowledge", "trigger": "统计", "knowledge": "..."}]
```

### 3. 知识库管理
- **结构化知识**: API文档、错误模式、最佳实践的结构化存储
- **智能检索**: 根据表名、错误码快速获取相关知识
- **历史记录**: 自动记录查询历史，支持相似查询分析

```python
# 获取表知识
table_info = knowledge_base.get_table_knowledge("tcp_flow")
# 获取错误解决方案
solution = knowledge_base.get_error_solution("5")
# 获取最佳实践
practices = knowledge_base.get_best_practices("query_optimization")
```

### 4. 内存和状态管理
- **工作内存**: 存储当前会话的临时信息
- **情景记忆**: 记录重要的操作事件和结果
- **会话上下文**: 维护用户会话状态
- **相似查询**: 基于历史查询提供优化建议

```python
# 工作内存操作
memory_manager.store_working_memory("last_query", query_info)
value = memory_manager.get_working_memory("last_query")

# 情景记忆
memory_manager.add_episodic_memory({"type": "query_success", "table": "tcp_flow"})
```

### 5. 事件驱动架构
- **异步处理**: 基于事件流的异步操作处理
- **事件类型**: API连接、查询开始/成功/错误、知识召回等事件
- **自动响应**: 事件触发自动执行相关的知识检索和状态更新

```python
# 事件发布
event_stream.emit(Event(EventType.QUERY_START, query_data))

# 事件订阅
event_stream.subscribe(EventType.QUERY_SUCCESS, handler.handle_query_success)
```

## 🛠️ 新增工具函数

### 配置管理
- `get_current_config()`: 获取当前配置信息
- `setup_api_connection()`: 智能API连接设置

### 知识辅助
- `get_knowledge_assistance(query)`: 获取智能知识辅助
- `list_available_knowledge()`: 列出所有可用知识代理
- `get_agent_knowledge(agent_name)`: 获取指定代理的详细知识

### 知识库查询
- `get_table_knowledge(table_name)`: 获取表知识信息
- `get_error_solution(error_code)`: 获取错误解决方案
- `get_best_practices(category)`: 获取最佳实践建议

### 内存管理
- `get_session_status()`: 获取会话状态信息
- `get_query_history(limit)`: 获取查询历史
- `get_similar_queries(table, fields)`: 获取相似查询
- `clear_session_memory()`: 清除会话内存
- `set_session_context(key, value)`: 设置会话上下文

### 事件系统
- `get_event_history(limit, event_type)`: 获取事件历史
- `get_event_statistics()`: 获取事件统计信息

## 📁 文件结构

```
├── stats_mcp_server_official.py      # 主服务器文件(已改造)
├── stats_server_config.toml          # 配置文件示例
├── test_agent_kb_integration.py      # 集成测试
├── example_usage.py                  # 使用示例
├── microagents/                      # 微代理目录
│   └── knowledge/
│       └── stats_api.md             # 统计API知识
└── README_Agent_KB_Integration.md    # 本文档
```

## 🚦 快速开始

### 1. 配置环境
```bash
# 设置环境变量
export STATS_API_URL="https://your-stats-server.com/"
export STATS_API_USERNAME="your_username"
export STATS_API_PASSWORD="your_password"

# 或编辑配置文件
cp stats_server_config.toml.example stats_server_config.toml
# 编辑配置文件...
```

### 2. 运行测试
```bash
python test_agent_kb_integration.py
```

### 3. 查看示例
```bash
python example_usage.py
```

### 4. 启动服务器
```bash
python stats_mcp_server_official.py
```

## 🔧 使用示例

### 智能API连接
```python
# 自动使用配置文件或环境变量
setup_api_connection()

# 或手动指定参数
setup_api_connection(
    url="https://your-server.com/",
    username="admin",
    password="password"
)
```

### 智能查询辅助
```python
# 获取查询帮助
get_knowledge_assistance("我想查询TCP流量统计")
# 返回相关的知识代理和建议

# 获取表知识
get_table_knowledge("tcp_flow")
# 返回表的详细信息和使用建议
```

### 查询优化建议
```python
# 获取相似的历史查询
get_similar_queries("tcp_flow", "server_ip_addr,total_byte")
# 返回相似查询和优化建议

# 获取最佳实践
get_best_practices("query_optimization")
# 返回查询优化的最佳实践建议
```

## 🎯 核心优势

1. **零硬编码**: 所有配置都可通过环境变量或配置文件管理
2. **智能化**: 微代理系统提供上下文相关的智能建议
3. **可扩展**: 基于Agent-KB架构，易于扩展新功能
4. **高可用**: 事件驱动架构支持异步处理和错误恢复
5. **用户友好**: 丰富的帮助信息和最佳实践指导

## 🔍 技术特点

- **配置管理**: 基于Agent-KB的配置管理模式
- **微代理**: 知识驱动的智能助手系统
- **知识库**: 结构化的API文档和最佳实践
- **内存管理**: 工作内存和情景记忆的双重管理
- **事件驱动**: 异步事件处理和状态管理
- **向后兼容**: 保持原有API接口的完全兼容

这个改造将原本简单的MCP服务器转变为一个智能化的、可扩展的、用户友好的统计查询平台！
