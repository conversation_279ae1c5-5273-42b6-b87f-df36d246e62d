# OpenHandsを始める

[OpenHandsをインストール](./installation)し、[LLMを設定](./installation#setup)しました。次は何をしましょうか？

OpenHandsは、さまざまなエンジニアリングタスクを支援できます。しかし、この技術はまだ新しく、
エージェントが支援なしで広範で複雑なエンジニアリングタスクを処理できるようになるまでには時間がかかります。
そのため、エージェントが得意とすることと、支援が必要な部分を理解することが重要です。

## Hello World

最初に試してみたいのは、シンプルな「hello world」の例かもしれません。
これは見た目以上に複雑になる可能性があります！

エージェントに以下のように依頼してみてください：
> "hello world!"と表示するbashスクリプトhello.shを作成してください

エージェントがスクリプトを作成するだけでなく、適切な権限を設定し、
スクリプトを実行して出力を確認することに気付くでしょう。

エージェントにコードの改良を続けて依頼することもできます。これはエージェントと
作業する優れた方法です。シンプルに始めて、反復的に改良していきます。

> hello.shを修正して、最初の引数として名前を受け取るようにしてください。デフォルトは"world"にしてください

また、エージェントが環境のセットアップに時間を要する可能性はありますが、
必要な任意の言語で作業することができます！

> hello.shをRubyスクリプトに変換して実行してください

## ゼロからの構築

エージェントは、「グリーンフィールド」タスク（既存のコードベースに関する文脈を必要としないタスク）で
特に優れた性能を発揮し、ゼロから始めることができます。

シンプルなタスクから始めて、反復的に改良していくのがベストです。また、
要望する内容、使用する技術スタックなどについて、できるだけ具体的に指定することをお勧めします。

例えば、TODOアプリケーションを構築できます：

> フロントエンドのみのReactベースの基本的なTODOリストアプリケーションを作成してください。
> すべての状態はlocalStorageに保存してください。

基本的な構造ができたら、アプリケーションの改良を続けることができます：

> 各タスクにオプションの期限を追加できるようにしてください

通常の開発と同様に、頻繁にコミットとプッシュを行うことをお勧めします。
これにより、エージェントが予期せぬ方向に進んだ場合でも、以前の状態に戻ることができます。
エージェントにコミットとプッシュを依頼することもできます：

> 変更をコミットして、"feature/due-dates"という新しいブランチにプッシュしてください

## 新しいコードの追加

OpenHandsは、既存のコードベースに新しいコードを追加する作業も優れています。

例えば、OpenHandsにコードを分析するGitHubアクションをプロジェクトに追加するよう依頼できます。
OpenHandsはコードベースを確認して使用すべき言語を判断し、新しいファイルを
`./github/workflows/lint.yml`に作成できます。

> このリポジトリのコードを分析するGitHubアクションを追加してください

一部のタスクではより多くの文脈が必要かもしれません。OpenHandsは`ls`や`grep`を使用して
コードベースを検索できますが、事前に文脈を提供することで、より速く、より正確に作業を
進めることができます。また、トークンの消費も少なくなります！

> ./backend/api/routes.jsを修正して、すべてのタスクのリストを返す新しいルートを追加してください

> ./frontend/componentsディレクトリにWidgetのリストを表示する新しいReactコンポーネントを追加してください。
> 既存のWidgetコンポーネントを使用する必要があります。

## リファクタリング

OpenHandsは、特に小規模な既存コードのリファクタリングに優れています。
コードベース全体のアーキテクチャを変更しようとするのは避けた方がよいですが、
長いファイルや関数の分割、変数名の変更などは非常にうまく機能する傾向があります。

> ./app.goの1文字の変数名をすべて変更してください

> widget.phpの`build_and_deploy_widgets`関数を`build_widgets`と`deploy_widgets`の2つの関数に分割してください

> ./api/routes.jsを各ルートごとの別々のファイルに分割してください

## バグ修正

OpenHandsは、コードのバグの追跡と修正も支援できます。しかし、すべての
開発者が知っているように、バグ修正は非常に繊細な作業になる可能性があり、多くの場合OpenHandsはより多くの文脈を
必要とします。バグを診断済みで、OpenHandsにロジックを理解してもらいたい場合に特に役立ちます。

> 現在、`/subscribe`エンドポイントのemailフィールドが.ioドメインを拒否しています。これを修正してください。

> ./app.pyの`search_widgets`関数が大文字小文字を区別して検索を行っています。大文字小文字を区別しないように修正してください。

エージェントとバグを修正する際は、テスト駆動開発を行うと便利なことが多いです。
エージェントに新しいテストを書かせ、バグが修正されるまで反復することができます：

> `hello`関数が空文字列でクラッシュします。このバグを再現するテストを書いて、コードを修正してテストが通るようにしてください。

## その他

OpenHandsは、ほぼすべてのコーディングタスクを支援できます。しかし、最大限に
活用するには練習が必要です。以下の点を忘れないでください：
* タスクを小さく保つ
* できるだけ具体的に指定する
* できるだけ多くの文脈を提供する
* 頻繁にコミットとプッシュを行う

OpenHandsを最大限に活用する方法についての詳細なアドバイスは、[プロンプトのベストプラクティス](./prompting/prompting-best-practices)をご覧ください。
