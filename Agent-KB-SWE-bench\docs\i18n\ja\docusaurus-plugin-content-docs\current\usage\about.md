# OpenHands について

## 研究戦略

LLM を使用して本番レベルのアプリケーションを完全に複製することは複雑な endeavor です。私たちの戦略は以下の通りです:

- **コア技術研究:** コード生成と処理の技術的側面を理解し改善するための基礎研究に注力します。
- **タスク計画:** バグ検出、コードベース管理、最適化の機能を開発します。
- **評価:** 私たちのエージェントをより良く理解し改善するための包括的な評価指標を確立します。

## デフォルトエージェント

現在のデフォルトエージェントは、コードの生成とファイル処理が可能な [CodeActAgent](agents) です。

## 構築技術

OpenHands は、強力なフレームワークとライブラリを組み合わせて構築されており、開発のための堅牢な基盤を提供しています。
プロジェクトで使用されている主要な技術は以下の通りです:

![FastAPI](https://img.shields.io/badge/FastAPI-black?style=for-the-badge) ![uvicorn](https://img.shields.io/badge/uvicorn-black?style=for-the-badge) ![LiteLLM](https://img.shields.io/badge/LiteLLM-black?style=for-the-badge) ![Docker](https://img.shields.io/badge/Docker-black?style=for-the-badge) ![Ruff](https://img.shields.io/badge/Ruff-black?style=for-the-badge) ![MyPy](https://img.shields.io/badge/MyPy-black?style=for-the-badge) ![LlamaIndex](https://img.shields.io/badge/LlamaIndex-black?style=for-the-badge) ![React](https://img.shields.io/badge/React-black?style=for-the-badge)

これらの技術の選択は進行中であり、プロジェクトの進化に伴って新しい技術が追加されたり、既存の技術が削除されたりする可能性があることに注意してください。
私たちは OpenHands の機能を強化するために、最も適切で効率的なツールを採用するよう努めています。

## ライセンス

MIT [ライセンス](https://github.com/All-Hands-AI/OpenHands/blob/main/LICENSE) の下で配布されています。
