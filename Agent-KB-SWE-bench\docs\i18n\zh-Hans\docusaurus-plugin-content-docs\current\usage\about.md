# 关于 OpenHands

## 研究策略

使用 LLM 实现生产级应用的完全复制是一项复杂的工作。我们的策略包括：

1. **核心技术研究：** 专注于基础研究，以理解和改进代码生成和处理的技术方面
2. **专业能力：** 通过数据整理、训练方法等提高核心组件的效率
3. **任务规划：** 开发 bug 检测、代码库管理和优化的能力
4. **评估：** 建立全面的评估指标，以更好地理解和改进我们的模型

## 默认 Agent

我们当前的默认 Agent 是 [CodeActAgent](agents)，它能够生成代码并处理文件。

## 构建技术

OpenHands 使用强大的框架和库组合构建，为其开发提供了坚实的基础。以下是项目中使用的关键技术：

![FastAPI](https://img.shields.io/badge/FastAPI-black?style=for-the-badge) ![uvicorn](https://img.shields.io/badge/uvicorn-black?style=for-the-badge) ![LiteLLM](https://img.shields.io/badge/LiteLLM-black?style=for-the-badge) ![Docker](https://img.shields.io/badge/Docker-black?style=for-the-badge) ![Ruff](https://img.shields.io/badge/Ruff-black?style=for-the-badge) ![MyPy](https://img.shields.io/badge/MyPy-black?style=for-the-badge) ![LlamaIndex](https://img.shields.io/badge/LlamaIndex-black?style=for-the-badge) ![React](https://img.shields.io/badge/React-black?style=for-the-badge)

请注意，这些技术的选择正在进行中，随着项目的发展，可能会添加其他技术或删除现有技术。我们努力采用最合适和最有效的工具来增强 OpenHands 的能力。

## 许可证

根据 MIT [许可证](https://github.com/All-Hands-AI/OpenHands/blob/main/LICENSE) 分发。
