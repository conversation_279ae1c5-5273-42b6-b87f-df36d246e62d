# 主な機能

![overview](https://www.all-hands.dev/assets/product/product-slide-1.webp)

## 1. ワークスペース

ワークスペース機能は、以下の主要な機能を備えた包括的な開発環境を提供します：

* ファイルエクスプローラー：プロジェクトのファイルとディレクトリの閲覧、表示、管理
* プロジェクト管理：異なるプロジェクトのインポート、作成、ナビゲーション
* 統合開発ツール：様々な開発ワークフローとのシームレスな統合
* ファイル操作：
  * ファイル内容の表示
  * 新規ファイルとフォルダの作成
  * ファイルのアップロードとダウンロード
  * 基本的なファイル操作

## 2. Jupyterノートブック

Jupyterノートブック機能は、インタラクティブなコーディングとデータ分析環境を提供します：

* インタラクティブなコードセル：セルベースのインターフェースでPythonコードを実行
* 入出力の追跡：コード入力とその出力の履歴を保持
* 永続的なセッション：セル間でコード実行コンテキストを保持
* 様々なPython操作とデータ分析タスクをサポート
* リアルタイムのコード実行と結果の可視化

## 3. ブラウザ（ベータ）

ブラウザ機能は、Web操作機能を提供します：

* Webページナビゲーション：アプリケーション内でウェブサイトを開いて閲覧
* スクリーンショット取得：Webページのスクリーンショットを自動生成
* インタラクション機能：
  * 要素のクリック
  * フォームの入力
  * ページのスクロール
  * Web内容のナビゲーション
* 15種類のブラウザ操作機能をサポート

## 4. ターミナル

ターミナル機能は、アプリケーション内でコマンドラインインターフェースを提供します：

* シェルコマンドの実行：bashとシステムコマンドを実行
* コマンド履歴：過去のコマンドを追跡して呼び出し
* 環境との対話：システムのコマンドラインと直接対話
* 様々なプログラミングとシステム管理タスクをサポート

## 5. チャット / AI会話

チャットインターフェースは、AIを活用した会話体験を提供します：

* インタラクティブなAIアシスタント：自然言語での会話に参加
* コンテキストを理解した応答：開発関連の質問を理解して応答
* アクション提案：タスクに対する実行可能な推奨事項を提供
* 会話管理：異なる会話スレッドの作成、削除、管理

## 6. アプリケーション（ベータ）

メインアプリケーションインターフェースは、これらすべての機能を統合します：

* 統合ワークスペース：ワークスペース、ブラウザ、ターミナル、AIチャットのシームレスな統合
* カスタマイズ可能なレイアウト：異なる機能パネルの配置をカスタマイズ
* 状態管理：異なる機能間でコンテキストと状態を維持
* セキュリティとプライバシー制御：アプリケーションの設定と権限を管理

### 追加情報

* アプリケーションは現在ベータ版で、継続的な改善と機能追加が行われています
* 様々な開発ワークフローとAIアシストコーディングをサポート
* 統合ツールとAIアシスタンスを通じて開発者の生産性を向上するように設計
