# OpenHands 入门指南

您已经[安装了 OpenHands](./installation)并且
[设置了您的 LLM](./installation#setup)。接下来呢？

OpenHands 可以帮助您处理各种工程任务。但这项技术
仍然很新,我们还有很长的路要走,才能拥有无需任何指导就能承担大型、复杂
工程任务的智能体。因此,了解智能体擅长什么,以及
它可能需要什么帮助非常重要。

## Hello World

您可能想尝试的第一件事是一个简单的 "hello world" 示例。
这可能比听起来更复杂!

尝试提示智能体:
> 请编写一个 bash 脚本 hello.sh,打印 "hello world!"

您应该看到,智能体不仅编写了脚本,还设置了正确的
权限并运行脚本以检查输出。

您可以继续提示智能体优化您的代码。这是一个很好的
与智能体合作的方式。从简单开始,然后迭代。

> 请修改 hello.sh,使其接受一个名称作为第一个参数,但默认为 "world"

您还可以使用任何需要的语言,尽管智能体可能需要花一些
时间来设置它的环境!

> 请将 hello.sh 转换为 Ruby 脚本,并运行它

## 从头开始构建

智能体在 "绿地" 任务(不需要
任何关于现有代码库的上下文的任务)方面表现得非常出色,它们可以直接从头开始。

最好从一个简单的任务开始,然后迭代它。最好也要
尽可能具体地说明您想要什么,技术栈应该是什么,等等。

例如,我们可以构建一个 TODO 应用:

> 请在 React 中构建一个基本的 TODO 列表应用。它应该只有前端,所有状态
> 应该保存在 localStorage 中。

一旦骨架搭建好,我们就可以继续迭代应用:

> 请允许为每个任务添加一个可选的截止日期

就像正常开发一样,经常提交和推送代码是很好的做法。
这样,如果智能体偏离轨道,您总是可以恢复到旧的状态。
您可以要求智能体为您提交和推送:

> 请提交更改并将其推送到名为 "feature/due-dates" 的新分支


## 添加新代码

OpenHands 还可以很好地将新代码添加到现有代码库中。

例如,您可以要求 OpenHands 向您的项目添加一个新的 GitHub action
来检查您的代码。OpenHands 可能会查看您的代码库以确定应该使用什么语言,
但随后它可以直接将一个新文件放入 `./github/workflows/lint.yml`

> 请添加一个 GitHub action 来检查此仓库中的代码

某些任务可能需要更多上下文。虽然 OpenHands 可以使用 `ls` 和 `grep`
在您的代码库中搜索,但提前提供上下文可以让它移动得更快、
更准确。而且这会让您花费更少的 token!

> 请修改 ./backend/api/routes.js 以添加一个新路由,返回所有任务的列表

> 请在 ./frontend/components 目录中添加一个新的 React 组件,用于显示 Widget 列表。
> 它应该使用现有的 Widget 组件。

## 重构

OpenHands 在重构现有代码方面表现出色,尤其是在小块中。
您可能不想尝试重新构建整个代码库,但拆分
长文件和函数、重命名变量等往往效果很好。

> 请重命名 ./app.go 中的所有单字母变量

> 请将 widget.php 中的函数 `build_and_deploy_widgets` 拆分为两个函数:`build_widgets` 和 `deploy_widgets`

> 请将 ./api/routes.js 拆分为每个路由的单独文件

## Bug 修复

OpenHands 还可以帮助您跟踪和修复代码中的错误。但是,任何
开发人员都知道,修复错误可能非常棘手,通常 OpenHands 需要更多上下文。
如果您已经诊断出错误,但希望 OpenHands 找出逻辑,这会有所帮助。

> 目前 `/subscribe` 端点中的电子邮件字段拒绝 .io 域名。请修复这个问题。

> ./app.py 中的 `search_widgets` 函数正在执行区分大小写的搜索。请使其不区分大小写。

在使用智能体进行错误修复时,进行测试驱动开发通常很有帮助。
您可以要求智能体编写一个新测试,然后迭代直到修复错误:

> `hello` 函数在空字符串上崩溃。请编写一个测试来重现此错误,然后修复代码以通过测试。

## 更多

OpenHands 能够在几乎任何编码任务上提供帮助。但需要一些练习
才能充分利用它。请记住:
* 保持任务简单
* 尽可能具体
* 提供尽可能多的上下文
* 经常提交和推送

有关如何充分利用 OpenHands 的更多提示,请参阅[提示最佳实践](./prompting/prompting-best-practices)。
