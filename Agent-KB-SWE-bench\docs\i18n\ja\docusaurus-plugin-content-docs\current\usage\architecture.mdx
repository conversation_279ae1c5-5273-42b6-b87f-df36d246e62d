---
sidebar_position: 4
---

# 🏛️ システムアーキテクチャの概要

以下は、システムアーキテクチャの高レベルな概要です。システムは主に2つのコンポーネントに分かれています: フロントエンドとバックエンドです。フロントエンドはユーザーとのインタラクションの管理と結果の表示を担当します。バックエンドはビジネスロジックの管理とエージェントの実行を担当します。

![system_architecture.svg](/img/system_architecture.svg)

この概要は、主要なコンポーネントとそれらの相互作用を示すために簡略化されています。バックエンドアーキテクチャのより詳細なビューについては、[バックエンドアーキテクチャ](#backend-architecture-ja)のセクションを参照してください。

# バックエンドアーキテクチャ {#backend-architecture-ja}

_**注意**: バックエンドアーキテクチャは開発中であり、変更される可能性があります。以下の図は、図のフッターに示されているコミットに基づく現在のバックエンドアーキテクチャを示しています。_

![backend_architecture.svg](/img/backend_architecture.svg)

<details>
  <summary>この図の更新</summary>
  <div>
    バックエンドアーキテクチャ図の生成は部分的に自動化されています。
    図はpy2pumlツールを使用してコード内の型アノテーションから生成されます。
    その後、図は手動でレビューされ、調整され、PNGとSVGにエクスポートされます。

    ## 前提条件

    - openhandsが実行可能なPython環境
    (リポジトリのルートにあるREADME.mdファイルの指示に従って)
    - [py2puml](https://github.com/lucsorel/py2puml)がインストールされていること

## 手順

1.  リポジトリのルートから以下のコマンドを実行して、図を自動的に生成します:
    `py2puml openhands openhands > docs/architecture/backend_architecture.puml`

2.  生成されたファイルをPlantUMLエディタ(Visual Studio CodeのPlantUML拡張機能や[PlantText](https://www.planttext.com/)など)で開きます。

3.  生成されたPUMLを確認し、図に必要な変更を加えます(欠落している部分を追加し、エラーを修正し、レイアウトを改善します)。
    _py2pumlはコード内の型アノテーションから図を作成するため、型アノテーションが欠落していたり正しくない場合、図が不完全または不正確になる可能性があります。_

4.  新しい図と以前の図の違いを確認し、変更が正しいかどうかを手動で確認します。
    _過去に図に手動で追加され、現在も関連性のある部分を削除しないように注意してください。_

5.  図の生成に使用されたコミットのハッシュを図のフッターに追加します。

6.  図をPNGファイルとSVGファイルにエクスポートし、`docs/architecture`ディレクトリ内の既存の図を置き換えます。これは(例えば[PlantText](https://www.planttext.com/)を使用して)行うことができます。

  </div>
</details>
