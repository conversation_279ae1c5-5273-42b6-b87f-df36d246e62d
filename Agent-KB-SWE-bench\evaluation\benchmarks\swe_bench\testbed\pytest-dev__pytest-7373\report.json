{"all": ["testing/test_mark.py::TestFunctional::test_reevaluate_dynamic_expr", "testing/test_mark.py::TestMark::test_pytest_exists_in_namespace_all[py.test-mark]", "testing/test_mark.py::TestMark::test_pytest_exists_in_namespace_all[py.test-param]", "testing/test_mark.py::TestMark::test_pytest_exists_in_namespace_all[pytest-mark]", "testing/test_mark.py::TestMark::test_pytest_exists_in_namespace_all[pytest-param]", "testing/test_mark.py::TestMark::test_pytest_mark_notcallable", "testing/test_mark.py::TestMark::test_mark_with_param", "testing/test_mark.py::TestMark::test_pytest_mark_name_starts_with_underscore", "testing/test_mark.py::TestMarkDecorator::test__eq__[lhs0-rhs0-True]", "testing/test_mark.py::TestMarkDecorator::test__eq__[lhs1-rhs1-False]", "testing/test_mark.py::TestMarkDecorator::test__eq__[lhs2-bar-False]", "testing/test_mark.py::TestMarkDecorator::test__eq__[foo-rhs3-False]", "testing/test_mark.py::TestMarkDecorator::test_aliases", "testing/test_mark.py::test_addmarker_order", "testing/test_mark.py::test_pytest_param_id_requires_string", "testing/test_mark.py::test_pytest_param_id_allows_none_or_string[None]", "testing/test_mark.py::test_pytest_param_id_allows_none_or_string[hello", "testing/test_mark.py::test_marked_class_run_twice", "testing/test_mark.py::test_ini_markers", "testing/test_mark.py::test_markers_option", "testing/test_mark.py::test_ini_markers_whitespace", "testing/test_mark.py::test_marker_without_description", "testing/test_mark.py::test_markers_option_with_plugin_in_current_dir", "testing/test_mark.py::test_mark_on_pseudo_function", "testing/test_mark.py::test_strict_prohibits_unregistered_markers[--strict-markers]", "testing/test_mark.py::test_strict_prohibits_unregistered_markers[--strict]", "testing/test_mark.py::test_mark_option[xyz-expected_passed0]", "testing/test_mark.py::test_mark_option[(((", "testing/test_mark.py::test_mark_option[not", "testing/test_mark.py::test_mark_option[xyz", "testing/test_mark.py::test_mark_option[xyz2-expected_passed4]", "testing/test_mark.py::test_mark_option_custom[interface-expected_passed0]", "testing/test_mark.py::test_mark_option_custom[not", "testing/test_mark.py::test_keyword_option_custom[interface-expected_passed0]", "testing/test_mark.py::test_keyword_option_custom[not", "testing/test_mark.py::test_keyword_option_custom[pass-expected_passed2]", "testing/test_mark.py::test_keyword_option_custom[1", "testing/test_mark.py::test_keyword_option_considers_mark", "testing/test_mark.py::test_keyword_option_parametrize[None-expected_passed0]", "testing/test_mark.py::test_keyword_option_parametrize[[1.3]-expected_passed1]", "testing/test_mark.py::test_keyword_option_parametrize[2-3-expected_passed2]", "testing/test_mark.py::test_parametrize_with_module", "testing/test_mark.py::test_keyword_option_wrong_arguments[foo", "testing/test_mark.py::test_keyword_option_wrong_arguments[(foo-at", "testing/test_mark.py::test_keyword_option_wrong_arguments[or", "testing/test_mark.py::test_keyword_option_wrong_arguments[not", "testing/test_mark.py::test_parametrized_collected_from_command_line", "testing/test_mark.py::test_parametrized_collect_with_wrong_args", "testing/test_mark.py::test_parametrized_with_kwargs", "testing/test_mark.py::test_parametrize_iterator", "testing/test_mark.py::TestFunctional::test_merging_markers_deep", "testing/test_mark.py::TestFunctional::test_mark_decorator_subclass_does_not_propagate_to_base", "testing/test_mark.py::TestFunctional::test_mark_should_not_pass_to_siebling_class", "testing/test_mark.py::TestFunctional::test_mark_decorator_baseclasses_merged", "testing/test_mark.py::TestFunctional::test_mark_closest", "testing/test_mark.py::TestFunctional::test_mark_with_wrong_marker", "testing/test_mark.py::TestFunctional::test_mark_dynamically_in_funcarg", "testing/test_mark.py::TestFunctional::test_no_marker_match_on_unmarked_names", "testing/test_mark.py::TestFunctional::test_keywords_at_node_level", "testing/test_mark.py::TestFunctional::test_keyword_added_for_session", "testing/test_mark.py::TestFunctional::test_mark_from_parameters", "testing/test_mark.py::TestKeywordSelection::test_select_simple", "testing/test_mark.py::TestKeywordSelection::test_select_extra_keywords[xxx]", "testing/test_mark.py::TestKeywordSelection::test_select_extra_keywords[xxx", "testing/test_mark.py::TestKeywordSelection::test_select_extra_keywords[TestClass]", "testing/test_mark.py::TestKeywordSelection::test_select_extra_keywords[TestClass", "testing/test_mark.py::TestKeywordSelection::test_select_starton", "testing/test_mark.py::TestKeywordSelection::test_keyword_extra", "testing/test_mark.py::TestKeywordSelection::test_no_magic_values[__]", "testing/test_mark.py::TestKeywordSelection::test_no_magic_values[+]", "testing/test_mark.py::TestKeywordSelection::test_no_magic_values[..]", "testing/test_mark.py::TestKeywordSelection::test_no_match_directories_outside_the_suite", "testing/test_mark.py::test_parameterset_for_parametrize_marks[None]", "testing/test_mark.py::test_parameterset_for_parametrize_marks[]", "testing/test_mark.py::test_parameterset_for_parametrize_marks[skip]", "testing/test_mark.py::test_parameterset_for_parametrize_marks[xfail]", "testing/test_mark.py::test_parameterset_for_fail_at_collect", "testing/test_mark.py::test_parameterset_for_parametrize_bad_markname", "testing/test_mark.py::test_mark_expressions_no_smear", "testing/test_mark.py::test_markers_from_parametrize", "testing/test_mark.py::test_marker_expr_eval_failure_handling[NOT", "testing/test_mark.py::test_marker_expr_eval_failure_handling[bogus/]"], "FAIL_TO_PASS": {"success": [], "failure": ["testing/test_mark.py::TestFunctional::test_reevaluate_dynamic_expr"]}, "PASS_TO_PASS": {"success": [], "failure": ["testing/test_mark.py::TestMark::test_pytest_exists_in_namespace_all[py.test-mark]", "testing/test_mark.py::TestMark::test_pytest_exists_in_namespace_all[py.test-param]", "testing/test_mark.py::TestMark::test_pytest_exists_in_namespace_all[pytest-mark]", "testing/test_mark.py::TestMark::test_pytest_exists_in_namespace_all[pytest-param]", "testing/test_mark.py::TestMark::test_pytest_mark_notcallable", "testing/test_mark.py::TestMark::test_mark_with_param", "testing/test_mark.py::TestMark::test_pytest_mark_name_starts_with_underscore", "testing/test_mark.py::TestMarkDecorator::test__eq__[lhs0-rhs0-True]", "testing/test_mark.py::TestMarkDecorator::test__eq__[lhs1-rhs1-False]", "testing/test_mark.py::TestMarkDecorator::test__eq__[lhs2-bar-False]", "testing/test_mark.py::TestMarkDecorator::test__eq__[foo-rhs3-False]", "testing/test_mark.py::TestMarkDecorator::test_aliases", "testing/test_mark.py::test_addmarker_order", "testing/test_mark.py::test_pytest_param_id_requires_string", "testing/test_mark.py::test_pytest_param_id_allows_none_or_string[None]", "testing/test_mark.py::test_pytest_param_id_allows_none_or_string[hello", "testing/test_mark.py::test_marked_class_run_twice", "testing/test_mark.py::test_ini_markers", "testing/test_mark.py::test_markers_option", "testing/test_mark.py::test_ini_markers_whitespace", "testing/test_mark.py::test_marker_without_description", "testing/test_mark.py::test_markers_option_with_plugin_in_current_dir", "testing/test_mark.py::test_mark_on_pseudo_function", "testing/test_mark.py::test_strict_prohibits_unregistered_markers[--strict-markers]", "testing/test_mark.py::test_strict_prohibits_unregistered_markers[--strict]", "testing/test_mark.py::test_mark_option[xyz-expected_passed0]", "testing/test_mark.py::test_mark_option[(((", "testing/test_mark.py::test_mark_option[not", "testing/test_mark.py::test_mark_option[xyz", "testing/test_mark.py::test_mark_option[xyz2-expected_passed4]", "testing/test_mark.py::test_mark_option_custom[interface-expected_passed0]", "testing/test_mark.py::test_mark_option_custom[not", "testing/test_mark.py::test_keyword_option_custom[interface-expected_passed0]", "testing/test_mark.py::test_keyword_option_custom[not", "testing/test_mark.py::test_keyword_option_custom[pass-expected_passed2]", "testing/test_mark.py::test_keyword_option_custom[1", "testing/test_mark.py::test_keyword_option_considers_mark", "testing/test_mark.py::test_keyword_option_parametrize[None-expected_passed0]", "testing/test_mark.py::test_keyword_option_parametrize[[1.3]-expected_passed1]", "testing/test_mark.py::test_keyword_option_parametrize[2-3-expected_passed2]", "testing/test_mark.py::test_parametrize_with_module", "testing/test_mark.py::test_keyword_option_wrong_arguments[foo", "testing/test_mark.py::test_keyword_option_wrong_arguments[(foo-at", "testing/test_mark.py::test_keyword_option_wrong_arguments[or", "testing/test_mark.py::test_keyword_option_wrong_arguments[not", "testing/test_mark.py::test_parametrized_collected_from_command_line", "testing/test_mark.py::test_parametrized_collect_with_wrong_args", "testing/test_mark.py::test_parametrized_with_kwargs", "testing/test_mark.py::test_parametrize_iterator", "testing/test_mark.py::TestFunctional::test_merging_markers_deep", "testing/test_mark.py::TestFunctional::test_mark_decorator_subclass_does_not_propagate_to_base", "testing/test_mark.py::TestFunctional::test_mark_should_not_pass_to_siebling_class", "testing/test_mark.py::TestFunctional::test_mark_decorator_baseclasses_merged", "testing/test_mark.py::TestFunctional::test_mark_closest", "testing/test_mark.py::TestFunctional::test_mark_with_wrong_marker", "testing/test_mark.py::TestFunctional::test_mark_dynamically_in_funcarg", "testing/test_mark.py::TestFunctional::test_no_marker_match_on_unmarked_names", "testing/test_mark.py::TestFunctional::test_keywords_at_node_level", "testing/test_mark.py::TestFunctional::test_keyword_added_for_session", "testing/test_mark.py::TestFunctional::test_mark_from_parameters", "testing/test_mark.py::TestKeywordSelection::test_select_simple", "testing/test_mark.py::TestKeywordSelection::test_select_extra_keywords[xxx]", "testing/test_mark.py::TestKeywordSelection::test_select_extra_keywords[xxx", "testing/test_mark.py::TestKeywordSelection::test_select_extra_keywords[TestClass]", "testing/test_mark.py::TestKeywordSelection::test_select_extra_keywords[TestClass", "testing/test_mark.py::TestKeywordSelection::test_select_starton", "testing/test_mark.py::TestKeywordSelection::test_keyword_extra", "testing/test_mark.py::TestKeywordSelection::test_no_magic_values[__]", "testing/test_mark.py::TestKeywordSelection::test_no_magic_values[+]", "testing/test_mark.py::TestKeywordSelection::test_no_magic_values[..]", "testing/test_mark.py::TestKeywordSelection::test_no_match_directories_outside_the_suite", "testing/test_mark.py::test_parameterset_for_parametrize_marks[None]", "testing/test_mark.py::test_parameterset_for_parametrize_marks[]", "testing/test_mark.py::test_parameterset_for_parametrize_marks[skip]", "testing/test_mark.py::test_parameterset_for_parametrize_marks[xfail]", "testing/test_mark.py::test_parameterset_for_fail_at_collect", "testing/test_mark.py::test_parameterset_for_parametrize_bad_markname", "testing/test_mark.py::test_mark_expressions_no_smear", "testing/test_mark.py::test_markers_from_parametrize", "testing/test_mark.py::test_marker_expr_eval_failure_handling[NOT", "testing/test_mark.py::test_marker_expr_eval_failure_handling[bogus/]"]}, "FAIL_TO_FAIL": {"success": [], "failure": []}, "PASS_TO_FAIL": {"success": [], "failure": []}}