

# Démarrer avec OpenHands

Vous avez donc [installé OpenHands](./installation) et avez
[configuré votre LLM](./installation#setup). Et maintenant ?

OpenHands peut vous aider à aborder une grande variété de tâches d'ingénierie. Mais la technologie
est encore nouvelle, et nous sommes loin d'avoir des agents capables de prendre en charge des tâches
d'ingénierie vastes et compliquées sans aucune aide. Il est donc important de se faire une idée de ce que l'agent
fait bien, et où il pourrait avoir besoin d'un coup de main.

## Hello World

La première chose que vous voudrez peut-être essayer est un simple exemple "hello world".
Cela peut être plus compliqué qu'il n'y paraît !

Essayez de demander à l'agent :
> Veuillez écrire un script bash hello.sh qui affiche "hello world!"

Vous devriez constater que l'agent non seulement écrit le script, mais définit également les
permissions correctes et exécute le script pour vérifier la sortie.

Vous pouvez continuer à demander à l'agent d'affiner votre code. C'est une excellente façon de
travailler avec les agents. Commencez simplement, et itérez.

> Veuillez modifier hello.sh pour qu'il accepte un nom comme premier argument, mais par défaut "world"

Vous pouvez également travailler dans n'importe quel langage dont vous avez besoin, bien que l'agent puisse avoir besoin de passer du
temps à configurer son environnement !

> Veuillez convertir hello.sh en un script Ruby, et l'exécuter

## Construire à partir de zéro

Les agents se débrouillent exceptionnellement bien pour les tâches "greenfield" (tâches où ils n'ont pas besoin
de contexte sur une base de code existante) et ils peuvent simplement commencer à partir de zéro.

Il est préférable de commencer par une tâche simple, puis d'itérer. Il est également préférable d'être
aussi précis que possible sur ce que vous voulez, sur la pile technologique à utiliser, etc.

Par exemple, nous pourrions construire une application TODO :

> Veuillez créer une application basique de liste de tâches en React. Elle devrait être uniquement frontend, et tout l'état
> devrait être conservé dans localStorage.

Nous pouvons continuer à itérer sur l'application une fois le squelette en place :

> Veuillez permettre d'ajouter une date d'échéance optionnelle à chaque tâche

Tout comme avec le développement normal, il est bon de commiter et de pousser votre code fréquemment.
De cette façon, vous pouvez toujours revenir à un ancien état si l'agent dévie.
Vous pouvez demander à l'agent de commiter et de pousser pour vous :

> Veuillez commiter les changements et les pousser sur une nouvelle branche appelée "feature/due-dates"


## Ajouter du nouveau code

OpenHands peut également faire un excellent travail en ajoutant du nouveau code à une base de code existante.

Par exemple, vous pouvez demander à OpenHands d'ajouter une nouvelle action GitHub à votre projet
qui analyse votre code. OpenHands peut jeter un coup d'œil à votre base de code pour voir quel langage
il doit utiliser, mais ensuite il peut simplement déposer un nouveau fichier dans `./github/workflows/lint.yml`

> Veuillez ajouter une action GitHub qui analyse le code dans ce dépôt

Certaines tâches peuvent nécessiter un peu plus de contexte. Bien qu'OpenHands puisse utiliser `ls` et `grep`
pour rechercher dans votre base de code, fournir le contexte à l'avance lui permet d'aller plus vite,
et plus précisément. Et cela vous coûtera moins de tokens !

> Veuillez modifier ./backend/api/routes.js pour ajouter une nouvelle route qui renvoie une liste de toutes les tâches

> Veuillez ajouter un nouveau composant React qui affiche une liste de Widgets dans le répertoire ./frontend/components.
> Il devrait utiliser le composant Widget existant.

## Refactoring

OpenHands est excellent pour refactoriser du code existant, surtout par petits morceaux.
Vous ne voulez probablement pas essayer de réarchitecturer toute votre base de code, mais diviser
les longs fichiers et fonctions, renommer les variables, etc. ont tendance à très bien fonctionner.

> Veuillez renommer toutes les variables à une lettre dans ./app.go

> Veuillez diviser la fonction `build_and_deploy_widgets` en deux fonctions, `build_widgets` et `deploy_widgets` dans widget.php

> Veuillez diviser ./api/routes.js en fichiers séparés pour chaque route

## Corrections de bugs

OpenHands peut également vous aider à traquer et corriger des bugs dans votre code. Mais, comme tout
développeur le sait, la correction de bugs peut être extrêmement délicate, et souvent OpenHands aura besoin de plus de contexte.
Cela aide si vous avez diagnostiqué le bug, mais que vous voulez qu'OpenHands comprenne la logique.

> Actuellement, le champ email dans le point de terminaison `/subscribe` rejette les domaines .io. Veuillez corriger cela.

> La fonction `search_widgets` dans ./app.py effectue une recherche sensible à la casse. Veuillez la rendre insensible à la casse.

Il est souvent utile de faire du développement piloté par les tests lors de la correction de bugs avec un agent.
Vous pouvez demander à l'agent d'écrire un nouveau test, puis d'itérer jusqu'à ce qu'il corrige le bug :

> La fonction `hello` plante sur la chaîne vide. Veuillez écrire un test qui reproduit ce bug, puis corrigez le code pour qu'il passe.

## Plus

OpenHands est capable d'aider sur à peu près n'importe quelle tâche de codage. Mais il faut de la pratique
pour en tirer le meilleur parti. N'oubliez pas de :
* Garder vos tâches petites
* Être aussi précis que possible
* Fournir autant de contexte que possible
* Commiter et pousser fréquemment

Voir [Bonnes pratiques de prompting](./prompting/prompting-best-practices) pour plus de conseils sur la façon de tirer le meilleur parti d'OpenHands.
