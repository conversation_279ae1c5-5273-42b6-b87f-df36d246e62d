# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# Code generated by the Google Gen AI SDK generator DO NOT EDIT.

import datetime
import importlib
import json
import logging
import os
import re
import typing
from typing import (
    Any,
    Callable,
    ClassVar,
    Dict,
    List,
    Literal,
    Optional,
    Tuple,
    TypeVar,
    Union,
)
from google.genai import _common
from google.genai import types as genai_types
from pydantic import (
    ConfigDict,
    Field,
    PrivateAttr,
    computed_field,
    field_validator,
    model_validator,
)
from typing_extensions import TypedDict

logger = logging.getLogger("vertexai_genai.types")

__all__ = ["PrebuiltMetric"]  # noqa: F822


def __getattr__(name: str) -> typing.Any:
    if name == "PrebuiltMetric":
        module = importlib.import_module("._evals_utils", __package__)
        prebuilt_metric_obj = getattr(module, name)
        globals()[name] = prebuilt_metric_obj
        return prebuilt_metric_obj
    raise AttributeError(f"module '{__name__}' has no attribute '{name}'")


if typing.TYPE_CHECKING:
    import pandas as pd
else:
    pd: typing.Type = Any
    try:
        import pandas as pd
    except ImportError:
        pd = None
if typing.TYPE_CHECKING:
    import yaml
else:
    try:
        import yaml
    except ImportError:
        yaml = None

logger = logging.getLogger("vertexai_genai.types")

MetricSubclass = TypeVar("MetricSubclass", bound="Metric")


class PairwiseChoice(_common.CaseInSensitiveEnum):
    """Output only. Pairwise metric choice."""

    PAIRWISE_CHOICE_UNSPECIFIED = "PAIRWISE_CHOICE_UNSPECIFIED"
    """Unspecified prediction choice."""
    BASELINE = "BASELINE"
    """Baseline prediction wins"""
    CANDIDATE = "CANDIDATE"
    """Candidate prediction wins"""
    TIE = "TIE"
    """Winner cannot be determined"""


class Strategy(_common.CaseInSensitiveEnum):
    """Optional. This determines which type of scheduling strategy to use."""

    STRATEGY_UNSPECIFIED = "STRATEGY_UNSPECIFIED"
    """Strategy will default to STANDARD."""
    ON_DEMAND = "ON_DEMAND"
    """Deprecated. Regular on-demand provisioning strategy."""
    LOW_COST = "LOW_COST"
    """Deprecated. Low cost by making potential use of spot resources."""
    STANDARD = "STANDARD"
    """Standard provisioning strategy uses regular on-demand resources."""
    SPOT = "SPOT"
    """Spot provisioning strategy uses spot resources."""
    FLEX_START = "FLEX_START"
    """Flex Start strategy uses DWS to queue for resources."""


class AcceleratorType(_common.CaseInSensitiveEnum):
    """Immutable.

    The type of accelerator(s) that may be attached to the machine as per
    accelerator_count.
    """

    ACCELERATOR_TYPE_UNSPECIFIED = "ACCELERATOR_TYPE_UNSPECIFIED"
    """Unspecified accelerator type, which means no accelerator."""
    NVIDIA_TESLA_K80 = "NVIDIA_TESLA_K80"
    """Deprecated: Nvidia Tesla K80 GPU has reached end of support, see https://cloud.google.com/compute/docs/eol/k80-eol."""
    NVIDIA_TESLA_P100 = "NVIDIA_TESLA_P100"
    """Nvidia Tesla P100 GPU."""
    NVIDIA_TESLA_V100 = "NVIDIA_TESLA_V100"
    """Nvidia Tesla V100 GPU."""
    NVIDIA_TESLA_P4 = "NVIDIA_TESLA_P4"
    """Nvidia Tesla P4 GPU."""
    NVIDIA_TESLA_T4 = "NVIDIA_TESLA_T4"
    """Nvidia Tesla T4 GPU."""
    NVIDIA_TESLA_A100 = "NVIDIA_TESLA_A100"
    """Nvidia Tesla A100 GPU."""
    NVIDIA_A100_80GB = "NVIDIA_A100_80GB"
    """Nvidia A100 80GB GPU."""
    NVIDIA_L4 = "NVIDIA_L4"
    """Nvidia L4 GPU."""
    NVIDIA_H100_80GB = "NVIDIA_H100_80GB"
    """Nvidia H100 80Gb GPU."""
    NVIDIA_H100_MEGA_80GB = "NVIDIA_H100_MEGA_80GB"
    """Nvidia H100 Mega 80Gb GPU."""
    NVIDIA_H200_141GB = "NVIDIA_H200_141GB"
    """Nvidia H200 141Gb GPU."""
    NVIDIA_B200 = "NVIDIA_B200"
    """Nvidia B200 GPU."""
    TPU_V2 = "TPU_V2"
    """TPU v2."""
    TPU_V3 = "TPU_V3"
    """TPU v3."""
    TPU_V4_POD = "TPU_V4_POD"
    """TPU v4."""
    TPU_V5_LITEPOD = "TPU_V5_LITEPOD"
    """TPU v5."""


class Type(_common.CaseInSensitiveEnum):
    """Required. Specifies the reservation affinity type."""

    TYPE_UNSPECIFIED = "TYPE_UNSPECIFIED"
    """Default value. This should not be used."""
    NO_RESERVATION = "NO_RESERVATION"
    """Do not consume from any reserved capacity, only use on-demand."""
    ANY_RESERVATION = "ANY_RESERVATION"
    """Consume any reservation available, falling back to on-demand."""
    SPECIFIC_RESERVATION = "SPECIFIC_RESERVATION"
    """Consume from a specific reservation. When chosen, the reservation must be identified via the `key` and `values` fields."""


class JobState(_common.CaseInSensitiveEnum):
    """Output only. The detailed state of the job."""

    JOB_STATE_UNSPECIFIED = "JOB_STATE_UNSPECIFIED"
    """The job state is unspecified."""
    JOB_STATE_QUEUED = "JOB_STATE_QUEUED"
    """The job has been just created or resumed and processing has not yet begun."""
    JOB_STATE_PENDING = "JOB_STATE_PENDING"
    """The service is preparing to run the job."""
    JOB_STATE_RUNNING = "JOB_STATE_RUNNING"
    """The job is in progress."""
    JOB_STATE_SUCCEEDED = "JOB_STATE_SUCCEEDED"
    """The job completed successfully."""
    JOB_STATE_FAILED = "JOB_STATE_FAILED"
    """The job failed."""
    JOB_STATE_CANCELLING = "JOB_STATE_CANCELLING"
    """The job is being cancelled. From this state the job may only go to either `JOB_STATE_SUCCEEDED`, `JOB_STATE_FAILED` or `JOB_STATE_CANCELLED`."""
    JOB_STATE_CANCELLED = "JOB_STATE_CANCELLED"
    """The job has been cancelled."""
    JOB_STATE_PAUSED = "JOB_STATE_PAUSED"
    """The job has been stopped, and can be resumed."""
    JOB_STATE_EXPIRED = "JOB_STATE_EXPIRED"
    """The job has expired."""
    JOB_STATE_UPDATING = "JOB_STATE_UPDATING"
    """The job is being updated. Only jobs in the `RUNNING` state can be updated. After updating, the job goes back to the `RUNNING` state."""
    JOB_STATE_PARTIALLY_SUCCEEDED = "JOB_STATE_PARTIALLY_SUCCEEDED"
    """The job is partially succeeded, some results may be missing due to errors."""


class Outcome(_common.CaseInSensitiveEnum):
    """Required. Outcome of the code execution."""

    OUTCOME_UNSPECIFIED = "OUTCOME_UNSPECIFIED"
    """Unspecified status. This value should not be used."""
    OUTCOME_OK = "OUTCOME_OK"
    """Code execution completed successfully."""
    OUTCOME_FAILED = "OUTCOME_FAILED"
    """Code execution finished but with a failure. `stderr` should contain the reason."""
    OUTCOME_DEADLINE_EXCEEDED = "OUTCOME_DEADLINE_EXCEEDED"
    """Code execution ran for too long, and was cancelled. There may or may not be a partial output present."""


class Language(_common.CaseInSensitiveEnum):
    """Required. Programming language of the `code`."""

    LANGUAGE_UNSPECIFIED = "LANGUAGE_UNSPECIFIED"
    """Unspecified language. This value should not be used."""
    PYTHON = "PYTHON"
    """Python >= 3.10, with numpy and simpy available."""


class GenerateMemoriesResponseGeneratedMemoryAction(_common.CaseInSensitiveEnum):
    """The action to take."""

    ACTION_UNSPECIFIED = "ACTION_UNSPECIFIED"
    """The action is unspecified."""
    CREATED = "CREATED"
    """The memory was created."""
    UPDATED = "UPDATED"
    """The memory was updated.

      The `fact` field may not be updated if the existing fact is still accurate.
      """
    DELETED = "DELETED"
    """The memory was deleted."""


class BleuInstance(_common.BaseModel):
    """Bleu instance."""

    prediction: Optional[str] = Field(
        default=None, description="""Required. Output of the evaluated model."""
    )
    reference: Optional[str] = Field(
        default=None,
        description="""Required. Ground truth used to compare against the prediction.""",
    )


class BleuInstanceDict(TypedDict, total=False):
    """Bleu instance."""

    prediction: Optional[str]
    """Required. Output of the evaluated model."""

    reference: Optional[str]
    """Required. Ground truth used to compare against the prediction."""


BleuInstanceOrDict = Union[BleuInstance, BleuInstanceDict]


class BleuSpec(_common.BaseModel):
    """Spec for bleu metric."""

    use_effective_order: Optional[bool] = Field(
        default=None,
        description="""Optional. Whether to use_effective_order to compute bleu score.""",
    )


class BleuSpecDict(TypedDict, total=False):
    """Spec for bleu metric."""

    use_effective_order: Optional[bool]
    """Optional. Whether to use_effective_order to compute bleu score."""


BleuSpecOrDict = Union[BleuSpec, BleuSpecDict]


class BleuInput(_common.BaseModel):

    instances: Optional[list[BleuInstance]] = Field(
        default=None, description="""Required. Repeated bleu instances."""
    )
    metric_spec: Optional[BleuSpec] = Field(
        default=None, description="""Required. Spec for bleu score metric."""
    )


class BleuInputDict(TypedDict, total=False):

    instances: Optional[list[BleuInstanceDict]]
    """Required. Repeated bleu instances."""

    metric_spec: Optional[BleuSpecDict]
    """Required. Spec for bleu score metric."""


BleuInputOrDict = Union[BleuInput, BleuInputDict]


class ExactMatchInstance(_common.BaseModel):
    """Exact match instance."""

    prediction: Optional[str] = Field(
        default=None, description="""Required. Output of the evaluated model."""
    )
    reference: Optional[str] = Field(
        default=None,
        description="""Required. Ground truth used to compare against the prediction.""",
    )


class ExactMatchInstanceDict(TypedDict, total=False):
    """Exact match instance."""

    prediction: Optional[str]
    """Required. Output of the evaluated model."""

    reference: Optional[str]
    """Required. Ground truth used to compare against the prediction."""


ExactMatchInstanceOrDict = Union[ExactMatchInstance, ExactMatchInstanceDict]


class ExactMatchSpec(_common.BaseModel):
    """Spec for exact match metric."""

    pass


class ExactMatchSpecDict(TypedDict, total=False):
    """Spec for exact match metric."""

    pass


ExactMatchSpecOrDict = Union[ExactMatchSpec, ExactMatchSpecDict]


class ExactMatchInput(_common.BaseModel):

    instances: Optional[list[ExactMatchInstance]] = Field(
        default=None,
        description="""Required. Repeated exact match instances.""",
    )
    metric_spec: Optional[ExactMatchSpec] = Field(
        default=None, description="""Required. Spec for exact match metric."""
    )


class ExactMatchInputDict(TypedDict, total=False):

    instances: Optional[list[ExactMatchInstanceDict]]
    """Required. Repeated exact match instances."""

    metric_spec: Optional[ExactMatchSpecDict]
    """Required. Spec for exact match metric."""


ExactMatchInputOrDict = Union[ExactMatchInput, ExactMatchInputDict]


class RougeInstance(_common.BaseModel):
    """Rouge instance."""

    prediction: Optional[str] = Field(
        default=None, description="""Required. Output of the evaluated model."""
    )
    reference: Optional[str] = Field(
        default=None,
        description="""Required. Ground truth used to compare against the prediction.""",
    )


class RougeInstanceDict(TypedDict, total=False):
    """Rouge instance."""

    prediction: Optional[str]
    """Required. Output of the evaluated model."""

    reference: Optional[str]
    """Required. Ground truth used to compare against the prediction."""


RougeInstanceOrDict = Union[RougeInstance, RougeInstanceDict]


class RougeSpec(_common.BaseModel):
    """Spec for rouge metric."""

    rouge_type: Optional[str] = Field(
        default=None,
        description="""Optional. Supported rouge types are rougen[1-9], rougeL, and rougeLsum.""",
    )
    split_summaries: Optional[bool] = Field(
        default=None,
        description="""Optional. Whether to split summaries while using rougeLsum.""",
    )
    use_stemmer: Optional[bool] = Field(
        default=None,
        description="""Optional. Whether to use stemmer to compute rouge score.""",
    )


class RougeSpecDict(TypedDict, total=False):
    """Spec for rouge metric."""

    rouge_type: Optional[str]
    """Optional. Supported rouge types are rougen[1-9], rougeL, and rougeLsum."""

    split_summaries: Optional[bool]
    """Optional. Whether to split summaries while using rougeLsum."""

    use_stemmer: Optional[bool]
    """Optional. Whether to use stemmer to compute rouge score."""


RougeSpecOrDict = Union[RougeSpec, RougeSpecDict]


class RougeInput(_common.BaseModel):
    """Rouge input."""

    instances: Optional[list[RougeInstance]] = Field(
        default=None, description="""Required. Repeated rouge instances."""
    )
    metric_spec: Optional[RougeSpec] = Field(
        default=None, description="""Required. Spec for rouge score metric."""
    )


class RougeInputDict(TypedDict, total=False):
    """Rouge input."""

    instances: Optional[list[RougeInstanceDict]]
    """Required. Repeated rouge instances."""

    metric_spec: Optional[RougeSpecDict]
    """Required. Spec for rouge score metric."""


RougeInputOrDict = Union[RougeInput, RougeInputDict]


class PointwiseMetricInstance(_common.BaseModel):
    """Pointwise metric instance."""

    json_instance: Optional[str] = Field(
        default=None,
        description="""Instance specified as a json string. String key-value pairs are expected in the json_instance to render PointwiseMetricSpec.instance_prompt_template.""",
    )


class PointwiseMetricInstanceDict(TypedDict, total=False):
    """Pointwise metric instance."""

    json_instance: Optional[str]
    """Instance specified as a json string. String key-value pairs are expected in the json_instance to render PointwiseMetricSpec.instance_prompt_template."""


PointwiseMetricInstanceOrDict = Union[
    PointwiseMetricInstance, PointwiseMetricInstanceDict
]


class CustomOutputFormatConfig(_common.BaseModel):
    """Spec for custom output format configuration."""

    return_raw_output: Optional[bool] = Field(
        default=None, description="""Optional. Whether to return raw output."""
    )


class CustomOutputFormatConfigDict(TypedDict, total=False):
    """Spec for custom output format configuration."""

    return_raw_output: Optional[bool]
    """Optional. Whether to return raw output."""


CustomOutputFormatConfigOrDict = Union[
    CustomOutputFormatConfig, CustomOutputFormatConfigDict
]


class PointwiseMetricSpec(_common.BaseModel):
    """Spec for pointwise metric."""

    metric_prompt_template: Optional[str] = Field(
        default=None,
        description="""Required. Metric prompt template for pointwise metric.""",
    )
    custom_output_format_config: Optional[CustomOutputFormatConfig] = Field(
        default=None,
        description="""Optional. CustomOutputFormatConfig allows customization of metric output. By default, metrics return a score and explanation. When this config is set, the default output is replaced with either: - The raw output string. - A parsed output based on a user-defined schema. If a custom format is chosen, the `score` and `explanation` fields in the corresponding metric result will be empty.""",
    )
    system_instruction: Optional[str] = Field(
        default=None,
        description="""Optional. System instructions for pointwise metric.""",
    )


class PointwiseMetricSpecDict(TypedDict, total=False):
    """Spec for pointwise metric."""

    metric_prompt_template: Optional[str]
    """Required. Metric prompt template for pointwise metric."""

    custom_output_format_config: Optional[CustomOutputFormatConfigDict]
    """Optional. CustomOutputFormatConfig allows customization of metric output. By default, metrics return a score and explanation. When this config is set, the default output is replaced with either: - The raw output string. - A parsed output based on a user-defined schema. If a custom format is chosen, the `score` and `explanation` fields in the corresponding metric result will be empty."""

    system_instruction: Optional[str]
    """Optional. System instructions for pointwise metric."""


PointwiseMetricSpecOrDict = Union[PointwiseMetricSpec, PointwiseMetricSpecDict]


class PointwiseMetricInput(_common.BaseModel):
    """Pointwise metric input."""

    instance: Optional[PointwiseMetricInstance] = Field(
        default=None, description="""Required. Pointwise metric instance."""
    )
    metric_spec: Optional[PointwiseMetricSpec] = Field(
        default=None, description="""Required. Spec for pointwise metric."""
    )


class PointwiseMetricInputDict(TypedDict, total=False):
    """Pointwise metric input."""

    instance: Optional[PointwiseMetricInstanceDict]
    """Required. Pointwise metric instance."""

    metric_spec: Optional[PointwiseMetricSpecDict]
    """Required. Spec for pointwise metric."""


PointwiseMetricInputOrDict = Union[PointwiseMetricInput, PointwiseMetricInputDict]


class PairwiseMetricInstance(_common.BaseModel):
    """Pairwise metric instance."""

    json_instance: Optional[str] = Field(
        default=None,
        description="""Instance specified as a json string. String key-value pairs are expected in the json_instance to render PairwiseMetricSpec.instance_prompt_template.""",
    )


class PairwiseMetricInstanceDict(TypedDict, total=False):
    """Pairwise metric instance."""

    json_instance: Optional[str]
    """Instance specified as a json string. String key-value pairs are expected in the json_instance to render PairwiseMetricSpec.instance_prompt_template."""


PairwiseMetricInstanceOrDict = Union[PairwiseMetricInstance, PairwiseMetricInstanceDict]


class PairwiseMetricSpec(_common.BaseModel):
    """Spec for pairwise metric."""

    metric_prompt_template: Optional[str] = Field(
        default=None,
        description="""Required. Metric prompt template for pairwise metric.""",
    )
    baseline_response_field_name: Optional[str] = Field(
        default=None,
        description="""Optional. The field name of the baseline response.""",
    )
    candidate_response_field_name: Optional[str] = Field(
        default=None,
        description="""Optional. The field name of the candidate response.""",
    )
    custom_output_format_config: Optional[CustomOutputFormatConfig] = Field(
        default=None,
        description="""Optional. CustomOutputFormatConfig allows customization of metric output. When this config is set, the default output is replaced with the raw output string. If a custom format is chosen, the `pairwise_choice` and `explanation` fields in the corresponding metric result will be empty.""",
    )
    system_instruction: Optional[str] = Field(
        default=None,
        description="""Optional. System instructions for pairwise metric.""",
    )


class PairwiseMetricSpecDict(TypedDict, total=False):
    """Spec for pairwise metric."""

    metric_prompt_template: Optional[str]
    """Required. Metric prompt template for pairwise metric."""

    baseline_response_field_name: Optional[str]
    """Optional. The field name of the baseline response."""

    candidate_response_field_name: Optional[str]
    """Optional. The field name of the candidate response."""

    custom_output_format_config: Optional[CustomOutputFormatConfigDict]
    """Optional. CustomOutputFormatConfig allows customization of metric output. When this config is set, the default output is replaced with the raw output string. If a custom format is chosen, the `pairwise_choice` and `explanation` fields in the corresponding metric result will be empty."""

    system_instruction: Optional[str]
    """Optional. System instructions for pairwise metric."""


PairwiseMetricSpecOrDict = Union[PairwiseMetricSpec, PairwiseMetricSpecDict]


class PairwiseMetricInput(_common.BaseModel):
    """Pairwise metric instance."""

    instance: Optional[PairwiseMetricInstance] = Field(
        default=None, description="""Required. Pairwise metric instance."""
    )
    metric_spec: Optional[PairwiseMetricSpec] = Field(
        default=None, description="""Required. Spec for pairwise metric."""
    )


class PairwiseMetricInputDict(TypedDict, total=False):
    """Pairwise metric instance."""

    instance: Optional[PairwiseMetricInstanceDict]
    """Required. Pairwise metric instance."""

    metric_spec: Optional[PairwiseMetricSpecDict]
    """Required. Spec for pairwise metric."""


PairwiseMetricInputOrDict = Union[PairwiseMetricInput, PairwiseMetricInputDict]


class ToolCallValidInstance(_common.BaseModel):
    """Tool call valid instance."""

    prediction: Optional[str] = Field(
        default=None, description="""Required. Output of the evaluated model."""
    )
    reference: Optional[str] = Field(
        default=None,
        description="""Required. Ground truth used to compare against the prediction.""",
    )


class ToolCallValidInstanceDict(TypedDict, total=False):
    """Tool call valid instance."""

    prediction: Optional[str]
    """Required. Output of the evaluated model."""

    reference: Optional[str]
    """Required. Ground truth used to compare against the prediction."""


ToolCallValidInstanceOrDict = Union[ToolCallValidInstance, ToolCallValidInstanceDict]


class ToolCallValidSpec(_common.BaseModel):
    """Spec for tool call valid metric."""

    pass


class ToolCallValidSpecDict(TypedDict, total=False):
    """Spec for tool call valid metric."""

    pass


ToolCallValidSpecOrDict = Union[ToolCallValidSpec, ToolCallValidSpecDict]


class ToolCallValidInput(_common.BaseModel):
    """Tool call valid input."""

    instances: Optional[list[ToolCallValidInstance]] = Field(
        default=None,
        description="""Required. Repeated tool call valid instances.""",
    )
    metric_spec: Optional[ToolCallValidSpec] = Field(
        default=None,
        description="""Required. Spec for tool call valid metric.""",
    )


class ToolCallValidInputDict(TypedDict, total=False):
    """Tool call valid input."""

    instances: Optional[list[ToolCallValidInstanceDict]]
    """Required. Repeated tool call valid instances."""

    metric_spec: Optional[ToolCallValidSpecDict]
    """Required. Spec for tool call valid metric."""


ToolCallValidInputOrDict = Union[ToolCallValidInput, ToolCallValidInputDict]


class ToolNameMatchInstance(_common.BaseModel):
    """Tool name match instance."""

    prediction: Optional[str] = Field(
        default=None, description="""Required. Output of the evaluated model."""
    )
    reference: Optional[str] = Field(
        default=None,
        description="""Required. Ground truth used to compare against the prediction.""",
    )


class ToolNameMatchInstanceDict(TypedDict, total=False):
    """Tool name match instance."""

    prediction: Optional[str]
    """Required. Output of the evaluated model."""

    reference: Optional[str]
    """Required. Ground truth used to compare against the prediction."""


ToolNameMatchInstanceOrDict = Union[ToolNameMatchInstance, ToolNameMatchInstanceDict]


class ToolNameMatchSpec(_common.BaseModel):
    """Spec for tool name match metric."""

    pass


class ToolNameMatchSpecDict(TypedDict, total=False):
    """Spec for tool name match metric."""

    pass


ToolNameMatchSpecOrDict = Union[ToolNameMatchSpec, ToolNameMatchSpecDict]


class ToolNameMatchInput(_common.BaseModel):
    """Tool name match input."""

    instances: Optional[list[ToolNameMatchInstance]] = Field(
        default=None,
        description="""Required. Repeated tool name match instances.""",
    )
    metric_spec: Optional[ToolNameMatchSpec] = Field(
        default=None,
        description="""Required. Spec for tool name match metric.""",
    )


class ToolNameMatchInputDict(TypedDict, total=False):
    """Tool name match input."""

    instances: Optional[list[ToolNameMatchInstanceDict]]
    """Required. Repeated tool name match instances."""

    metric_spec: Optional[ToolNameMatchSpecDict]
    """Required. Spec for tool name match metric."""


ToolNameMatchInputOrDict = Union[ToolNameMatchInput, ToolNameMatchInputDict]


class ToolParameterKeyMatchInstance(_common.BaseModel):
    """Tool parameter key match instance."""

    prediction: Optional[str] = Field(
        default=None, description="""Required. Output of the evaluated model."""
    )
    reference: Optional[str] = Field(
        default=None,
        description="""Required. Ground truth used to compare against the prediction.""",
    )


class ToolParameterKeyMatchInstanceDict(TypedDict, total=False):
    """Tool parameter key match instance."""

    prediction: Optional[str]
    """Required. Output of the evaluated model."""

    reference: Optional[str]
    """Required. Ground truth used to compare against the prediction."""


ToolParameterKeyMatchInstanceOrDict = Union[
    ToolParameterKeyMatchInstance, ToolParameterKeyMatchInstanceDict
]


class ToolParameterKeyMatchSpec(_common.BaseModel):
    """Spec for tool parameter key match metric."""

    pass


class ToolParameterKeyMatchSpecDict(TypedDict, total=False):
    """Spec for tool parameter key match metric."""

    pass


ToolParameterKeyMatchSpecOrDict = Union[
    ToolParameterKeyMatchSpec, ToolParameterKeyMatchSpecDict
]


class ToolParameterKeyMatchInput(_common.BaseModel):
    """Tool parameter key match input."""

    instances: Optional[list[ToolParameterKeyMatchInstance]] = Field(
        default=None,
        description="""Required. Repeated tool parameter key match instances.""",
    )
    metric_spec: Optional[ToolParameterKeyMatchSpec] = Field(
        default=None,
        description="""Required. Spec for tool parameter key match metric.""",
    )


class ToolParameterKeyMatchInputDict(TypedDict, total=False):
    """Tool parameter key match input."""

    instances: Optional[list[ToolParameterKeyMatchInstanceDict]]
    """Required. Repeated tool parameter key match instances."""

    metric_spec: Optional[ToolParameterKeyMatchSpecDict]
    """Required. Spec for tool parameter key match metric."""


ToolParameterKeyMatchInputOrDict = Union[
    ToolParameterKeyMatchInput, ToolParameterKeyMatchInputDict
]


class ToolParameterKVMatchInstance(_common.BaseModel):
    """Tool parameter kv match instance."""

    prediction: Optional[str] = Field(
        default=None, description="""Required. Output of the evaluated model."""
    )
    reference: Optional[str] = Field(
        default=None,
        description="""Required. Ground truth used to compare against the prediction.""",
    )


class ToolParameterKVMatchInstanceDict(TypedDict, total=False):
    """Tool parameter kv match instance."""

    prediction: Optional[str]
    """Required. Output of the evaluated model."""

    reference: Optional[str]
    """Required. Ground truth used to compare against the prediction."""


ToolParameterKVMatchInstanceOrDict = Union[
    ToolParameterKVMatchInstance, ToolParameterKVMatchInstanceDict
]


class ToolParameterKVMatchSpec(_common.BaseModel):
    """Spec for tool parameter kv match metric."""

    use_strict_string_match: Optional[bool] = Field(
        default=None,
        description="""Optional. Whether to use STRICT string match on parameter values.""",
    )


class ToolParameterKVMatchSpecDict(TypedDict, total=False):
    """Spec for tool parameter kv match metric."""

    use_strict_string_match: Optional[bool]
    """Optional. Whether to use STRICT string match on parameter values."""


ToolParameterKVMatchSpecOrDict = Union[
    ToolParameterKVMatchSpec, ToolParameterKVMatchSpecDict
]


class ToolParameterKVMatchInput(_common.BaseModel):
    """Tool parameter kv match input."""

    instances: Optional[list[ToolParameterKVMatchInstance]] = Field(
        default=None,
        description="""Required. Repeated tool parameter key value match instances.""",
    )
    metric_spec: Optional[ToolParameterKVMatchSpec] = Field(
        default=None,
        description="""Required. Spec for tool parameter key value match metric.""",
    )


class ToolParameterKVMatchInputDict(TypedDict, total=False):
    """Tool parameter kv match input."""

    instances: Optional[list[ToolParameterKVMatchInstanceDict]]
    """Required. Repeated tool parameter key value match instances."""

    metric_spec: Optional[ToolParameterKVMatchSpecDict]
    """Required. Spec for tool parameter key value match metric."""


ToolParameterKVMatchInputOrDict = Union[
    ToolParameterKVMatchInput, ToolParameterKVMatchInputDict
]


class AutoraterConfig(_common.BaseModel):
    """The configs for autorater."""

    autorater_model: Optional[str] = Field(
        default=None,
        description="""Optional. The fully qualified name of the publisher model or tuned autorater endpoint to use. Publisher model format: `projects/{project}/locations/{location}/publishers/*/models/*` Tuned model endpoint format: `projects/{project}/locations/{location}/endpoints/{endpoint}`""",
    )
    flip_enabled: Optional[bool] = Field(
        default=None,
        description="""Optional. Default is true. Whether to flip the candidate and baseline responses. This is only applicable to the pairwise metric. If enabled, also provide PairwiseMetricSpec.candidate_response_field_name and PairwiseMetricSpec.baseline_response_field_name. When rendering PairwiseMetricSpec.metric_prompt_template, the candidate and baseline fields will be flipped for half of the samples to reduce bias.""",
    )
    sampling_count: Optional[int] = Field(
        default=None,
        description="""Optional. Number of samples for each instance in the dataset. If not specified, the default is 4. Minimum value is 1, maximum value is 32.""",
    )


class AutoraterConfigDict(TypedDict, total=False):
    """The configs for autorater."""

    autorater_model: Optional[str]
    """Optional. The fully qualified name of the publisher model or tuned autorater endpoint to use. Publisher model format: `projects/{project}/locations/{location}/publishers/*/models/*` Tuned model endpoint format: `projects/{project}/locations/{location}/endpoints/{endpoint}`"""

    flip_enabled: Optional[bool]
    """Optional. Default is true. Whether to flip the candidate and baseline responses. This is only applicable to the pairwise metric. If enabled, also provide PairwiseMetricSpec.candidate_response_field_name and PairwiseMetricSpec.baseline_response_field_name. When rendering PairwiseMetricSpec.metric_prompt_template, the candidate and baseline fields will be flipped for half of the samples to reduce bias."""

    sampling_count: Optional[int]
    """Optional. Number of samples for each instance in the dataset. If not specified, the default is 4. Minimum value is 1, maximum value is 32."""


AutoraterConfigOrDict = Union[AutoraterConfig, AutoraterConfigDict]


class HttpRetryOptions(_common.BaseModel):
    """HTTP retry options to be used in each of the requests."""

    attempts: Optional[int] = Field(
        default=None,
        description="""Maximum number of attempts, including the original request.
      If 0 or 1, it means no retries.""",
    )
    initial_delay: Optional[float] = Field(
        default=None,
        description="""Initial delay before the first retry, in fractions of a second.""",
    )
    max_delay: Optional[float] = Field(
        default=None,
        description="""Maximum delay between retries, in fractions of a second.""",
    )
    exp_base: Optional[float] = Field(
        default=None,
        description="""Multiplier by which the delay increases after each attempt.""",
    )
    jitter: Optional[float] = Field(
        default=None, description="""Randomness factor for the delay."""
    )
    http_status_codes: Optional[list[int]] = Field(
        default=None,
        description="""List of HTTP status codes that should trigger a retry.
      If not specified, a default set of retryable codes may be used.""",
    )


class HttpRetryOptionsDict(TypedDict, total=False):
    """HTTP retry options to be used in each of the requests."""

    attempts: Optional[int]
    """Maximum number of attempts, including the original request.
      If 0 or 1, it means no retries."""

    initial_delay: Optional[float]
    """Initial delay before the first retry, in fractions of a second."""

    max_delay: Optional[float]
    """Maximum delay between retries, in fractions of a second."""

    exp_base: Optional[float]
    """Multiplier by which the delay increases after each attempt."""

    jitter: Optional[float]
    """Randomness factor for the delay."""

    http_status_codes: Optional[list[int]]
    """List of HTTP status codes that should trigger a retry.
      If not specified, a default set of retryable codes may be used."""


HttpRetryOptionsOrDict = Union[HttpRetryOptions, HttpRetryOptionsDict]


class HttpOptions(_common.BaseModel):
    """HTTP options to be used in each of the requests."""

    base_url: Optional[str] = Field(
        default=None,
        description="""The base URL for the AI platform service endpoint.""",
    )
    api_version: Optional[str] = Field(
        default=None, description="""Specifies the version of the API to use."""
    )
    headers: Optional[dict[str, str]] = Field(
        default=None,
        description="""Additional HTTP headers to be sent with the request.""",
    )
    timeout: Optional[int] = Field(
        default=None, description="""Timeout for the request in milliseconds."""
    )
    client_args: Optional[dict[str, Any]] = Field(
        default=None, description="""Args passed to the HTTP client."""
    )
    async_client_args: Optional[dict[str, Any]] = Field(
        default=None, description="""Args passed to the async HTTP client."""
    )
    extra_body: Optional[dict[str, Any]] = Field(
        default=None,
        description="""Extra parameters to add to the request body.""",
    )
    retry_options: Optional[HttpRetryOptions] = Field(
        default=None, description="""HTTP retry options for the request."""
    )


class HttpOptionsDict(TypedDict, total=False):
    """HTTP options to be used in each of the requests."""

    base_url: Optional[str]
    """The base URL for the AI platform service endpoint."""

    api_version: Optional[str]
    """Specifies the version of the API to use."""

    headers: Optional[dict[str, str]]
    """Additional HTTP headers to be sent with the request."""

    timeout: Optional[int]
    """Timeout for the request in milliseconds."""

    client_args: Optional[dict[str, Any]]
    """Args passed to the HTTP client."""

    async_client_args: Optional[dict[str, Any]]
    """Args passed to the async HTTP client."""

    extra_body: Optional[dict[str, Any]]
    """Extra parameters to add to the request body."""

    retry_options: Optional[HttpRetryOptionsDict]
    """HTTP retry options for the request."""


HttpOptionsOrDict = Union[HttpOptions, HttpOptionsDict]


class EvaluateInstancesConfig(_common.BaseModel):
    """Config for evaluate instances."""

    http_options: Optional[HttpOptions] = Field(
        default=None, description="""Used to override HTTP request options."""
    )


class EvaluateInstancesConfigDict(TypedDict, total=False):
    """Config for evaluate instances."""

    http_options: Optional[HttpOptionsDict]
    """Used to override HTTP request options."""


EvaluateInstancesConfigOrDict = Union[
    EvaluateInstancesConfig, EvaluateInstancesConfigDict
]


class _EvaluateInstancesRequestParameters(_common.BaseModel):
    """Parameters for evaluating instances."""

    bleu_input: Optional[BleuInput] = Field(default=None, description="""""")
    exact_match_input: Optional[ExactMatchInput] = Field(
        default=None, description=""""""
    )
    rouge_input: Optional[RougeInput] = Field(default=None, description="""""")
    pointwise_metric_input: Optional[PointwiseMetricInput] = Field(
        default=None, description=""""""
    )
    pairwise_metric_input: Optional[PairwiseMetricInput] = Field(
        default=None, description=""""""
    )
    tool_call_valid_input: Optional[ToolCallValidInput] = Field(
        default=None, description=""""""
    )
    tool_name_match_input: Optional[ToolNameMatchInput] = Field(
        default=None, description=""""""
    )
    tool_parameter_key_match_input: Optional[ToolParameterKeyMatchInput] = Field(
        default=None, description=""""""
    )
    tool_parameter_kv_match_input: Optional[ToolParameterKVMatchInput] = Field(
        default=None, description=""""""
    )
    autorater_config: Optional[AutoraterConfig] = Field(
        default=None, description=""""""
    )
    config: Optional[EvaluateInstancesConfig] = Field(default=None, description="""""")


class _EvaluateInstancesRequestParametersDict(TypedDict, total=False):
    """Parameters for evaluating instances."""

    bleu_input: Optional[BleuInputDict]
    """"""

    exact_match_input: Optional[ExactMatchInputDict]
    """"""

    rouge_input: Optional[RougeInputDict]
    """"""

    pointwise_metric_input: Optional[PointwiseMetricInputDict]
    """"""

    pairwise_metric_input: Optional[PairwiseMetricInputDict]
    """"""

    tool_call_valid_input: Optional[ToolCallValidInputDict]
    """"""

    tool_name_match_input: Optional[ToolNameMatchInputDict]
    """"""

    tool_parameter_key_match_input: Optional[ToolParameterKeyMatchInputDict]
    """"""

    tool_parameter_kv_match_input: Optional[ToolParameterKVMatchInputDict]
    """"""

    autorater_config: Optional[AutoraterConfigDict]
    """"""

    config: Optional[EvaluateInstancesConfigDict]
    """"""


_EvaluateInstancesRequestParametersOrDict = Union[
    _EvaluateInstancesRequestParameters, _EvaluateInstancesRequestParametersDict
]


class BleuMetricValue(_common.BaseModel):
    """Bleu metric value for an instance."""

    score: Optional[float] = Field(
        default=None, description="""Output only. Bleu score."""
    )


class BleuMetricValueDict(TypedDict, total=False):
    """Bleu metric value for an instance."""

    score: Optional[float]
    """Output only. Bleu score."""


BleuMetricValueOrDict = Union[BleuMetricValue, BleuMetricValueDict]


class BleuResults(_common.BaseModel):
    """Results for bleu metric."""

    bleu_metric_values: Optional[list[BleuMetricValue]] = Field(
        default=None, description="""Output only. Bleu metric values."""
    )


class BleuResultsDict(TypedDict, total=False):
    """Results for bleu metric."""

    bleu_metric_values: Optional[list[BleuMetricValueDict]]
    """Output only. Bleu metric values."""


BleuResultsOrDict = Union[BleuResults, BleuResultsDict]


class CometResult(_common.BaseModel):
    """Spec for Comet result - calculates the comet score for the given instance using the version specified in the spec."""

    score: Optional[float] = Field(
        default=None,
        description="""Output only. Comet score. Range depends on version.""",
    )


class CometResultDict(TypedDict, total=False):
    """Spec for Comet result - calculates the comet score for the given instance using the version specified in the spec."""

    score: Optional[float]
    """Output only. Comet score. Range depends on version."""


CometResultOrDict = Union[CometResult, CometResultDict]


class ExactMatchMetricValue(_common.BaseModel):
    """Exact match metric value for an instance."""

    score: Optional[float] = Field(
        default=None, description="""Output only. Exact match score."""
    )


class ExactMatchMetricValueDict(TypedDict, total=False):
    """Exact match metric value for an instance."""

    score: Optional[float]
    """Output only. Exact match score."""


ExactMatchMetricValueOrDict = Union[ExactMatchMetricValue, ExactMatchMetricValueDict]


class ExactMatchResults(_common.BaseModel):
    """Results for exact match metric."""

    exact_match_metric_values: Optional[list[ExactMatchMetricValue]] = Field(
        default=None, description="""Output only. Exact match metric values."""
    )


class ExactMatchResultsDict(TypedDict, total=False):
    """Results for exact match metric."""

    exact_match_metric_values: Optional[list[ExactMatchMetricValueDict]]
    """Output only. Exact match metric values."""


ExactMatchResultsOrDict = Union[ExactMatchResults, ExactMatchResultsDict]


class MetricxResult(_common.BaseModel):
    """Spec for MetricX result - calculates the MetricX score for the given instance using the version specified in the spec."""

    score: Optional[float] = Field(
        default=None,
        description="""Output only. MetricX score. Range depends on version.""",
    )


class MetricxResultDict(TypedDict, total=False):
    """Spec for MetricX result - calculates the MetricX score for the given instance using the version specified in the spec."""

    score: Optional[float]
    """Output only. MetricX score. Range depends on version."""


MetricxResultOrDict = Union[MetricxResult, MetricxResultDict]


class RawOutput(_common.BaseModel):
    """Raw output."""

    raw_output: Optional[list[str]] = Field(
        default=None, description="""Output only. Raw output string."""
    )


class RawOutputDict(TypedDict, total=False):
    """Raw output."""

    raw_output: Optional[list[str]]
    """Output only. Raw output string."""


RawOutputOrDict = Union[RawOutput, RawOutputDict]


class CustomOutput(_common.BaseModel):
    """Spec for custom output."""

    raw_outputs: Optional[RawOutput] = Field(
        default=None, description="""Output only. List of raw output strings."""
    )


class CustomOutputDict(TypedDict, total=False):
    """Spec for custom output."""

    raw_outputs: Optional[RawOutputDict]
    """Output only. List of raw output strings."""


CustomOutputOrDict = Union[CustomOutput, CustomOutputDict]


class PairwiseMetricResult(_common.BaseModel):
    """Spec for pairwise metric result."""

    custom_output: Optional[CustomOutput] = Field(
        default=None, description="""Output only. Spec for custom output."""
    )
    explanation: Optional[str] = Field(
        default=None,
        description="""Output only. Explanation for pairwise metric score.""",
    )
    pairwise_choice: Optional[PairwiseChoice] = Field(
        default=None, description="""Output only. Pairwise metric choice."""
    )


class PairwiseMetricResultDict(TypedDict, total=False):
    """Spec for pairwise metric result."""

    custom_output: Optional[CustomOutputDict]
    """Output only. Spec for custom output."""

    explanation: Optional[str]
    """Output only. Explanation for pairwise metric score."""

    pairwise_choice: Optional[PairwiseChoice]
    """Output only. Pairwise metric choice."""


PairwiseMetricResultOrDict = Union[PairwiseMetricResult, PairwiseMetricResultDict]


class PointwiseMetricResult(_common.BaseModel):
    """Spec for pointwise metric result."""

    custom_output: Optional[CustomOutput] = Field(
        default=None, description="""Output only. Spec for custom output."""
    )
    explanation: Optional[str] = Field(
        default=None,
        description="""Output only. Explanation for pointwise metric score.""",
    )
    score: Optional[float] = Field(
        default=None, description="""Output only. Pointwise metric score."""
    )


class PointwiseMetricResultDict(TypedDict, total=False):
    """Spec for pointwise metric result."""

    custom_output: Optional[CustomOutputDict]
    """Output only. Spec for custom output."""

    explanation: Optional[str]
    """Output only. Explanation for pointwise metric score."""

    score: Optional[float]
    """Output only. Pointwise metric score."""


PointwiseMetricResultOrDict = Union[PointwiseMetricResult, PointwiseMetricResultDict]


class RougeMetricValue(_common.BaseModel):
    """Rouge metric value for an instance."""

    score: Optional[float] = Field(
        default=None, description="""Output only. Rouge score."""
    )


class RougeMetricValueDict(TypedDict, total=False):
    """Rouge metric value for an instance."""

    score: Optional[float]
    """Output only. Rouge score."""


RougeMetricValueOrDict = Union[RougeMetricValue, RougeMetricValueDict]


class RougeResults(_common.BaseModel):
    """Results for rouge metric."""

    rouge_metric_values: Optional[list[RougeMetricValue]] = Field(
        default=None, description="""Output only. Rouge metric values."""
    )


class RougeResultsDict(TypedDict, total=False):
    """Results for rouge metric."""

    rouge_metric_values: Optional[list[RougeMetricValueDict]]
    """Output only. Rouge metric values."""


RougeResultsOrDict = Union[RougeResults, RougeResultsDict]


class RubricCritiqueResult(_common.BaseModel):
    """Rubric critique result."""

    rubric: Optional[str] = Field(
        default=None, description="""Output only. Rubric to be evaluated."""
    )
    verdict: Optional[bool] = Field(
        default=None,
        description="""Output only. Verdict for the rubric - true if the rubric is met, false otherwise.""",
    )


class RubricCritiqueResultDict(TypedDict, total=False):
    """Rubric critique result."""

    rubric: Optional[str]
    """Output only. Rubric to be evaluated."""

    verdict: Optional[bool]
    """Output only. Verdict for the rubric - true if the rubric is met, false otherwise."""


RubricCritiqueResultOrDict = Union[RubricCritiqueResult, RubricCritiqueResultDict]


class RubricBasedInstructionFollowingResult(_common.BaseModel):
    """Result for RubricBasedInstructionFollowing metric."""

    rubric_critique_results: Optional[list[RubricCritiqueResult]] = Field(
        default=None,
        description="""Output only. List of per rubric critique results.""",
    )
    score: Optional[float] = Field(
        default=None,
        description="""Output only. Overall score for the instruction following.""",
    )


class RubricBasedInstructionFollowingResultDict(TypedDict, total=False):
    """Result for RubricBasedInstructionFollowing metric."""

    rubric_critique_results: Optional[list[RubricCritiqueResultDict]]
    """Output only. List of per rubric critique results."""

    score: Optional[float]
    """Output only. Overall score for the instruction following."""


RubricBasedInstructionFollowingResultOrDict = Union[
    RubricBasedInstructionFollowingResult,
    RubricBasedInstructionFollowingResultDict,
]


class SummarizationVerbosityResult(_common.BaseModel):
    """Spec for summarization verbosity result."""

    confidence: Optional[float] = Field(
        default=None,
        description="""Output only. Confidence for summarization verbosity score.""",
    )
    explanation: Optional[str] = Field(
        default=None,
        description="""Output only. Explanation for summarization verbosity score.""",
    )
    score: Optional[float] = Field(
        default=None,
        description="""Output only. Summarization Verbosity score.""",
    )


class SummarizationVerbosityResultDict(TypedDict, total=False):
    """Spec for summarization verbosity result."""

    confidence: Optional[float]
    """Output only. Confidence for summarization verbosity score."""

    explanation: Optional[str]
    """Output only. Explanation for summarization verbosity score."""

    score: Optional[float]
    """Output only. Summarization Verbosity score."""


SummarizationVerbosityResultOrDict = Union[
    SummarizationVerbosityResult, SummarizationVerbosityResultDict
]


class ToolCallValidMetricValue(_common.BaseModel):
    """Tool call valid metric value for an instance."""

    score: Optional[float] = Field(
        default=None, description="""Output only. Tool call valid score."""
    )


class ToolCallValidMetricValueDict(TypedDict, total=False):
    """Tool call valid metric value for an instance."""

    score: Optional[float]
    """Output only. Tool call valid score."""


ToolCallValidMetricValueOrDict = Union[
    ToolCallValidMetricValue, ToolCallValidMetricValueDict
]


class ToolCallValidResults(_common.BaseModel):
    """Results for tool call valid metric."""

    tool_call_valid_metric_values: Optional[list[ToolCallValidMetricValue]] = Field(
        default=None,
        description="""Output only. Tool call valid metric values.""",
    )


class ToolCallValidResultsDict(TypedDict, total=False):
    """Results for tool call valid metric."""

    tool_call_valid_metric_values: Optional[list[ToolCallValidMetricValueDict]]
    """Output only. Tool call valid metric values."""


ToolCallValidResultsOrDict = Union[ToolCallValidResults, ToolCallValidResultsDict]


class ToolNameMatchMetricValue(_common.BaseModel):
    """Tool name match metric value for an instance."""

    score: Optional[float] = Field(
        default=None, description="""Output only. Tool name match score."""
    )


class ToolNameMatchMetricValueDict(TypedDict, total=False):
    """Tool name match metric value for an instance."""

    score: Optional[float]
    """Output only. Tool name match score."""


ToolNameMatchMetricValueOrDict = Union[
    ToolNameMatchMetricValue, ToolNameMatchMetricValueDict
]


class ToolNameMatchResults(_common.BaseModel):
    """Results for tool name match metric."""

    tool_name_match_metric_values: Optional[list[ToolNameMatchMetricValue]] = Field(
        default=None,
        description="""Output only. Tool name match metric values.""",
    )


class ToolNameMatchResultsDict(TypedDict, total=False):
    """Results for tool name match metric."""

    tool_name_match_metric_values: Optional[list[ToolNameMatchMetricValueDict]]
    """Output only. Tool name match metric values."""


ToolNameMatchResultsOrDict = Union[ToolNameMatchResults, ToolNameMatchResultsDict]


class ToolParameterKeyMatchMetricValue(_common.BaseModel):
    """Tool parameter key match metric value for an instance."""

    score: Optional[float] = Field(
        default=None,
        description="""Output only. Tool parameter key match score.""",
    )


class ToolParameterKeyMatchMetricValueDict(TypedDict, total=False):
    """Tool parameter key match metric value for an instance."""

    score: Optional[float]
    """Output only. Tool parameter key match score."""


ToolParameterKeyMatchMetricValueOrDict = Union[
    ToolParameterKeyMatchMetricValue, ToolParameterKeyMatchMetricValueDict
]


class ToolParameterKeyMatchResults(_common.BaseModel):
    """Results for tool parameter key match metric."""

    tool_parameter_key_match_metric_values: Optional[
        list[ToolParameterKeyMatchMetricValue]
    ] = Field(
        default=None,
        description="""Output only. Tool parameter key match metric values.""",
    )


class ToolParameterKeyMatchResultsDict(TypedDict, total=False):
    """Results for tool parameter key match metric."""

    tool_parameter_key_match_metric_values: Optional[
        list[ToolParameterKeyMatchMetricValueDict]
    ]
    """Output only. Tool parameter key match metric values."""


ToolParameterKeyMatchResultsOrDict = Union[
    ToolParameterKeyMatchResults, ToolParameterKeyMatchResultsDict
]


class ToolParameterKVMatchMetricValue(_common.BaseModel):
    """Tool parameter key value match metric value for an instance."""

    score: Optional[float] = Field(
        default=None,
        description="""Output only. Tool parameter key value match score.""",
    )


class ToolParameterKVMatchMetricValueDict(TypedDict, total=False):
    """Tool parameter key value match metric value for an instance."""

    score: Optional[float]
    """Output only. Tool parameter key value match score."""


ToolParameterKVMatchMetricValueOrDict = Union[
    ToolParameterKVMatchMetricValue, ToolParameterKVMatchMetricValueDict
]


class ToolParameterKVMatchResults(_common.BaseModel):
    """Results for tool parameter key value match metric."""

    tool_parameter_kv_match_metric_values: Optional[
        list[ToolParameterKVMatchMetricValue]
    ] = Field(
        default=None,
        description="""Output only. Tool parameter key value match metric values.""",
    )


class ToolParameterKVMatchResultsDict(TypedDict, total=False):
    """Results for tool parameter key value match metric."""

    tool_parameter_kv_match_metric_values: Optional[
        list[ToolParameterKVMatchMetricValueDict]
    ]
    """Output only. Tool parameter key value match metric values."""


ToolParameterKVMatchResultsOrDict = Union[
    ToolParameterKVMatchResults, ToolParameterKVMatchResultsDict
]


class TrajectoryAnyOrderMatchMetricValue(_common.BaseModel):
    """TrajectoryAnyOrderMatch metric value for an instance."""

    score: Optional[float] = Field(
        default=None,
        description="""Output only. TrajectoryAnyOrderMatch score.""",
    )


class TrajectoryAnyOrderMatchMetricValueDict(TypedDict, total=False):
    """TrajectoryAnyOrderMatch metric value for an instance."""

    score: Optional[float]
    """Output only. TrajectoryAnyOrderMatch score."""


TrajectoryAnyOrderMatchMetricValueOrDict = Union[
    TrajectoryAnyOrderMatchMetricValue, TrajectoryAnyOrderMatchMetricValueDict
]


class TrajectoryAnyOrderMatchResults(_common.BaseModel):
    """Results for TrajectoryAnyOrderMatch metric."""

    trajectory_any_order_match_metric_values: Optional[
        list[TrajectoryAnyOrderMatchMetricValue]
    ] = Field(
        default=None,
        description="""Output only. TrajectoryAnyOrderMatch metric values.""",
    )


class TrajectoryAnyOrderMatchResultsDict(TypedDict, total=False):
    """Results for TrajectoryAnyOrderMatch metric."""

    trajectory_any_order_match_metric_values: Optional[
        list[TrajectoryAnyOrderMatchMetricValueDict]
    ]
    """Output only. TrajectoryAnyOrderMatch metric values."""


TrajectoryAnyOrderMatchResultsOrDict = Union[
    TrajectoryAnyOrderMatchResults, TrajectoryAnyOrderMatchResultsDict
]


class TrajectoryExactMatchMetricValue(_common.BaseModel):
    """TrajectoryExactMatch metric value for an instance."""

    score: Optional[float] = Field(
        default=None, description="""Output only. TrajectoryExactMatch score."""
    )


class TrajectoryExactMatchMetricValueDict(TypedDict, total=False):
    """TrajectoryExactMatch metric value for an instance."""

    score: Optional[float]
    """Output only. TrajectoryExactMatch score."""


TrajectoryExactMatchMetricValueOrDict = Union[
    TrajectoryExactMatchMetricValue, TrajectoryExactMatchMetricValueDict
]


class TrajectoryExactMatchResults(_common.BaseModel):
    """Results for TrajectoryExactMatch metric."""

    trajectory_exact_match_metric_values: Optional[
        list[TrajectoryExactMatchMetricValue]
    ] = Field(
        default=None,
        description="""Output only. TrajectoryExactMatch metric values.""",
    )


class TrajectoryExactMatchResultsDict(TypedDict, total=False):
    """Results for TrajectoryExactMatch metric."""

    trajectory_exact_match_metric_values: Optional[
        list[TrajectoryExactMatchMetricValueDict]
    ]
    """Output only. TrajectoryExactMatch metric values."""


TrajectoryExactMatchResultsOrDict = Union[
    TrajectoryExactMatchResults, TrajectoryExactMatchResultsDict
]


class TrajectoryInOrderMatchMetricValue(_common.BaseModel):
    """TrajectoryInOrderMatch metric value for an instance."""

    score: Optional[float] = Field(
        default=None,
        description="""Output only. TrajectoryInOrderMatch score.""",
    )


class TrajectoryInOrderMatchMetricValueDict(TypedDict, total=False):
    """TrajectoryInOrderMatch metric value for an instance."""

    score: Optional[float]
    """Output only. TrajectoryInOrderMatch score."""


TrajectoryInOrderMatchMetricValueOrDict = Union[
    TrajectoryInOrderMatchMetricValue, TrajectoryInOrderMatchMetricValueDict
]


class TrajectoryInOrderMatchResults(_common.BaseModel):
    """Results for TrajectoryInOrderMatch metric."""

    trajectory_in_order_match_metric_values: Optional[
        list[TrajectoryInOrderMatchMetricValue]
    ] = Field(
        default=None,
        description="""Output only. TrajectoryInOrderMatch metric values.""",
    )


class TrajectoryInOrderMatchResultsDict(TypedDict, total=False):
    """Results for TrajectoryInOrderMatch metric."""

    trajectory_in_order_match_metric_values: Optional[
        list[TrajectoryInOrderMatchMetricValueDict]
    ]
    """Output only. TrajectoryInOrderMatch metric values."""


TrajectoryInOrderMatchResultsOrDict = Union[
    TrajectoryInOrderMatchResults, TrajectoryInOrderMatchResultsDict
]


class TrajectoryPrecisionMetricValue(_common.BaseModel):
    """TrajectoryPrecision metric value for an instance."""

    score: Optional[float] = Field(
        default=None, description="""Output only. TrajectoryPrecision score."""
    )


class TrajectoryPrecisionMetricValueDict(TypedDict, total=False):
    """TrajectoryPrecision metric value for an instance."""

    score: Optional[float]
    """Output only. TrajectoryPrecision score."""


TrajectoryPrecisionMetricValueOrDict = Union[
    TrajectoryPrecisionMetricValue, TrajectoryPrecisionMetricValueDict
]


class TrajectoryPrecisionResults(_common.BaseModel):
    """Results for TrajectoryPrecision metric."""

    trajectory_precision_metric_values: Optional[
        list[TrajectoryPrecisionMetricValue]
    ] = Field(
        default=None,
        description="""Output only. TrajectoryPrecision metric values.""",
    )


class TrajectoryPrecisionResultsDict(TypedDict, total=False):
    """Results for TrajectoryPrecision metric."""

    trajectory_precision_metric_values: Optional[
        list[TrajectoryPrecisionMetricValueDict]
    ]
    """Output only. TrajectoryPrecision metric values."""


TrajectoryPrecisionResultsOrDict = Union[
    TrajectoryPrecisionResults, TrajectoryPrecisionResultsDict
]


class TrajectoryRecallMetricValue(_common.BaseModel):
    """TrajectoryRecall metric value for an instance."""

    score: Optional[float] = Field(
        default=None, description="""Output only. TrajectoryRecall score."""
    )


class TrajectoryRecallMetricValueDict(TypedDict, total=False):
    """TrajectoryRecall metric value for an instance."""

    score: Optional[float]
    """Output only. TrajectoryRecall score."""


TrajectoryRecallMetricValueOrDict = Union[
    TrajectoryRecallMetricValue, TrajectoryRecallMetricValueDict
]


class TrajectoryRecallResults(_common.BaseModel):
    """Results for TrajectoryRecall metric."""

    trajectory_recall_metric_values: Optional[
        list[TrajectoryRecallMetricValue]
    ] = Field(
        default=None,
        description="""Output only. TrajectoryRecall metric values.""",
    )


class TrajectoryRecallResultsDict(TypedDict, total=False):
    """Results for TrajectoryRecall metric."""

    trajectory_recall_metric_values: Optional[list[TrajectoryRecallMetricValueDict]]
    """Output only. TrajectoryRecall metric values."""


TrajectoryRecallResultsOrDict = Union[
    TrajectoryRecallResults, TrajectoryRecallResultsDict
]


class TrajectorySingleToolUseMetricValue(_common.BaseModel):
    """TrajectorySingleToolUse metric value for an instance."""

    score: Optional[float] = Field(
        default=None,
        description="""Output only. TrajectorySingleToolUse score.""",
    )


class TrajectorySingleToolUseMetricValueDict(TypedDict, total=False):
    """TrajectorySingleToolUse metric value for an instance."""

    score: Optional[float]
    """Output only. TrajectorySingleToolUse score."""


TrajectorySingleToolUseMetricValueOrDict = Union[
    TrajectorySingleToolUseMetricValue, TrajectorySingleToolUseMetricValueDict
]


class TrajectorySingleToolUseResults(_common.BaseModel):
    """Results for TrajectorySingleToolUse metric."""

    trajectory_single_tool_use_metric_values: Optional[
        list[TrajectorySingleToolUseMetricValue]
    ] = Field(
        default=None,
        description="""Output only. TrajectorySingleToolUse metric values.""",
    )


class TrajectorySingleToolUseResultsDict(TypedDict, total=False):
    """Results for TrajectorySingleToolUse metric."""

    trajectory_single_tool_use_metric_values: Optional[
        list[TrajectorySingleToolUseMetricValueDict]
    ]
    """Output only. TrajectorySingleToolUse metric values."""


TrajectorySingleToolUseResultsOrDict = Union[
    TrajectorySingleToolUseResults, TrajectorySingleToolUseResultsDict
]


class EvaluateInstancesResponse(_common.BaseModel):
    """Result of evaluating an LLM metric."""

    bleu_results: Optional[BleuResults] = Field(
        default=None, description="""Results for bleu metric."""
    )
    comet_result: Optional[CometResult] = Field(
        default=None,
        description="""Translation metrics. Result for Comet metric.""",
    )
    exact_match_results: Optional[ExactMatchResults] = Field(
        default=None,
        description="""Auto metric evaluation results. Results for exact match metric.""",
    )
    metricx_result: Optional[MetricxResult] = Field(
        default=None, description="""Result for Metricx metric."""
    )
    pairwise_metric_result: Optional[PairwiseMetricResult] = Field(
        default=None, description="""Result for pairwise metric."""
    )
    pointwise_metric_result: Optional[PointwiseMetricResult] = Field(
        default=None,
        description="""Generic metrics. Result for pointwise metric.""",
    )
    rouge_results: Optional[RougeResults] = Field(
        default=None, description="""Results for rouge metric."""
    )
    rubric_based_instruction_following_result: Optional[
        RubricBasedInstructionFollowingResult
    ] = Field(
        default=None,
        description="""Result for rubric based instruction following metric.""",
    )
    summarization_verbosity_result: Optional[SummarizationVerbosityResult] = Field(
        default=None,
        description="""Result for summarization verbosity metric.""",
    )
    tool_call_valid_results: Optional[ToolCallValidResults] = Field(
        default=None,
        description="""Tool call metrics. Results for tool call valid metric.""",
    )
    tool_name_match_results: Optional[ToolNameMatchResults] = Field(
        default=None, description="""Results for tool name match metric."""
    )
    tool_parameter_key_match_results: Optional[ToolParameterKeyMatchResults] = Field(
        default=None,
        description="""Results for tool parameter key match metric.""",
    )
    tool_parameter_kv_match_results: Optional[ToolParameterKVMatchResults] = Field(
        default=None,
        description="""Results for tool parameter key value match metric.""",
    )
    trajectory_any_order_match_results: Optional[
        TrajectoryAnyOrderMatchResults
    ] = Field(
        default=None,
        description="""Result for trajectory any order match metric.""",
    )
    trajectory_exact_match_results: Optional[TrajectoryExactMatchResults] = Field(
        default=None,
        description="""Result for trajectory exact match metric.""",
    )
    trajectory_in_order_match_results: Optional[TrajectoryInOrderMatchResults] = Field(
        default=None,
        description="""Result for trajectory in order match metric.""",
    )
    trajectory_precision_results: Optional[TrajectoryPrecisionResults] = Field(
        default=None, description="""Result for trajectory precision metric."""
    )
    trajectory_recall_results: Optional[TrajectoryRecallResults] = Field(
        default=None, description="""Results for trajectory recall metric."""
    )
    trajectory_single_tool_use_results: Optional[
        TrajectorySingleToolUseResults
    ] = Field(
        default=None,
        description="""Results for trajectory single tool use metric.""",
    )


class EvaluateInstancesResponseDict(TypedDict, total=False):
    """Result of evaluating an LLM metric."""

    bleu_results: Optional[BleuResultsDict]
    """Results for bleu metric."""

    comet_result: Optional[CometResultDict]
    """Translation metrics. Result for Comet metric."""

    exact_match_results: Optional[ExactMatchResultsDict]
    """Auto metric evaluation results. Results for exact match metric."""

    metricx_result: Optional[MetricxResultDict]
    """Result for Metricx metric."""

    pairwise_metric_result: Optional[PairwiseMetricResultDict]
    """Result for pairwise metric."""

    pointwise_metric_result: Optional[PointwiseMetricResultDict]
    """Generic metrics. Result for pointwise metric."""

    rouge_results: Optional[RougeResultsDict]
    """Results for rouge metric."""

    rubric_based_instruction_following_result: Optional[
        RubricBasedInstructionFollowingResultDict
    ]
    """Result for rubric based instruction following metric."""

    summarization_verbosity_result: Optional[SummarizationVerbosityResultDict]
    """Result for summarization verbosity metric."""

    tool_call_valid_results: Optional[ToolCallValidResultsDict]
    """Tool call metrics. Results for tool call valid metric."""

    tool_name_match_results: Optional[ToolNameMatchResultsDict]
    """Results for tool name match metric."""

    tool_parameter_key_match_results: Optional[ToolParameterKeyMatchResultsDict]
    """Results for tool parameter key match metric."""

    tool_parameter_kv_match_results: Optional[ToolParameterKVMatchResultsDict]
    """Results for tool parameter key value match metric."""

    trajectory_any_order_match_results: Optional[TrajectoryAnyOrderMatchResultsDict]
    """Result for trajectory any order match metric."""

    trajectory_exact_match_results: Optional[TrajectoryExactMatchResultsDict]
    """Result for trajectory exact match metric."""

    trajectory_in_order_match_results: Optional[TrajectoryInOrderMatchResultsDict]
    """Result for trajectory in order match metric."""

    trajectory_precision_results: Optional[TrajectoryPrecisionResultsDict]
    """Result for trajectory precision metric."""

    trajectory_recall_results: Optional[TrajectoryRecallResultsDict]
    """Results for trajectory recall metric."""

    trajectory_single_tool_use_results: Optional[TrajectorySingleToolUseResultsDict]
    """Results for trajectory single tool use metric."""


EvaluateInstancesResponseOrDict = Union[
    EvaluateInstancesResponse, EvaluateInstancesResponseDict
]


class OptimizeConfig(_common.BaseModel):
    """Config for Prompt Optimizer."""

    http_options: Optional[HttpOptions] = Field(
        default=None, description="""Used to override HTTP request options."""
    )


class OptimizeConfigDict(TypedDict, total=False):
    """Config for Prompt Optimizer."""

    http_options: Optional[HttpOptionsDict]
    """Used to override HTTP request options."""


OptimizeConfigOrDict = Union[OptimizeConfig, OptimizeConfigDict]


class _OptimizeRequestParameters(_common.BaseModel):
    """Parameters for the optimize_prompt method."""

    config: Optional[OptimizeConfig] = Field(default=None, description="""""")


class _OptimizeRequestParametersDict(TypedDict, total=False):
    """Parameters for the optimize_prompt method."""

    config: Optional[OptimizeConfigDict]
    """"""


_OptimizeRequestParametersOrDict = Union[
    _OptimizeRequestParameters, _OptimizeRequestParametersDict
]


class OptimizeResponse(_common.BaseModel):
    """Response for the optimize_prompt method."""

    pass


class OptimizeResponseDict(TypedDict, total=False):
    """Response for the optimize_prompt method."""

    pass


OptimizeResponseOrDict = Union[OptimizeResponse, OptimizeResponseDict]


class GcsDestination(_common.BaseModel):
    """The Google Cloud Storage location where the output is to be written to."""

    output_uri_prefix: Optional[str] = Field(
        default=None,
        description="""Required. Google Cloud Storage URI to output directory. If the uri doesn't end with '/', a '/' will be automatically appended. The directory is created if it doesn't exist.""",
    )


class GcsDestinationDict(TypedDict, total=False):
    """The Google Cloud Storage location where the output is to be written to."""

    output_uri_prefix: Optional[str]
    """Required. Google Cloud Storage URI to output directory. If the uri doesn't end with '/', a '/' will be automatically appended. The directory is created if it doesn't exist."""


GcsDestinationOrDict = Union[GcsDestination, GcsDestinationDict]


class DnsPeeringConfig(_common.BaseModel):
    """DNS peering configuration.

    These configurations are used to create DNS peering zones in the Vertex
    tenant project VPC, enabling resolution of records within the specified
    domain hosted in the target network's Cloud DNS.
    """

    domain: Optional[str] = Field(
        default=None,
        description="""Required. The DNS name suffix of the zone being peered to, e.g., "my-internal-domain.corp.". Must end with a dot.""",
    )
    target_network: Optional[str] = Field(
        default=None,
        description="""Required. The VPC network name in the target_project where the DNS zone specified by 'domain' is visible.""",
    )
    target_project: Optional[str] = Field(
        default=None,
        description="""Required. The project ID hosting the Cloud DNS managed zone that contains the 'domain'. The Vertex AI Service Agent requires the dns.peer role on this project.""",
    )


class DnsPeeringConfigDict(TypedDict, total=False):
    """DNS peering configuration.

    These configurations are used to create DNS peering zones in the Vertex
    tenant project VPC, enabling resolution of records within the specified
    domain hosted in the target network's Cloud DNS.
    """

    domain: Optional[str]
    """Required. The DNS name suffix of the zone being peered to, e.g., "my-internal-domain.corp.". Must end with a dot."""

    target_network: Optional[str]
    """Required. The VPC network name in the target_project where the DNS zone specified by 'domain' is visible."""

    target_project: Optional[str]
    """Required. The project ID hosting the Cloud DNS managed zone that contains the 'domain'. The Vertex AI Service Agent requires the dns.peer role on this project."""


DnsPeeringConfigOrDict = Union[DnsPeeringConfig, DnsPeeringConfigDict]


class PscInterfaceConfig(_common.BaseModel):
    """Configuration for PSC-I."""

    dns_peering_configs: Optional[list[DnsPeeringConfig]] = Field(
        default=None,
        description="""Optional. DNS peering configurations. When specified, Vertex AI will attempt to configure DNS peering zones in the tenant project VPC to resolve the specified domains using the target network's Cloud DNS. The user must grant the dns.peer role to the Vertex AI Service Agent on the target project.""",
    )
    network_attachment: Optional[str] = Field(
        default=None,
        description="""Optional. The name of the Compute Engine [network attachment](https://cloud.google.com/vpc/docs/about-network-attachments) to attach to the resource within the region and user project. To specify this field, you must have already [created a network attachment] (https://cloud.google.com/vpc/docs/create-manage-network-attachments#create-network-attachments). This field is only used for resources using PSC-I.""",
    )


class PscInterfaceConfigDict(TypedDict, total=False):
    """Configuration for PSC-I."""

    dns_peering_configs: Optional[list[DnsPeeringConfigDict]]
    """Optional. DNS peering configurations. When specified, Vertex AI will attempt to configure DNS peering zones in the tenant project VPC to resolve the specified domains using the target network's Cloud DNS. The user must grant the dns.peer role to the Vertex AI Service Agent on the target project."""

    network_attachment: Optional[str]
    """Optional. The name of the Compute Engine [network attachment](https://cloud.google.com/vpc/docs/about-network-attachments) to attach to the resource within the region and user project. To specify this field, you must have already [created a network attachment] (https://cloud.google.com/vpc/docs/create-manage-network-attachments#create-network-attachments). This field is only used for resources using PSC-I."""


PscInterfaceConfigOrDict = Union[PscInterfaceConfig, PscInterfaceConfigDict]


class Scheduling(_common.BaseModel):
    """All parameters related to queuing and scheduling of custom jobs."""

    disable_retries: Optional[bool] = Field(
        default=None,
        description="""Optional. Indicates if the job should retry for internal errors after the job starts running. If true, overrides `Scheduling.restart_job_on_worker_restart` to false.""",
    )
    max_wait_duration: Optional[str] = Field(
        default=None,
        description="""Optional. This is the maximum duration that a job will wait for the requested resources to be provisioned if the scheduling strategy is set to [Strategy.DWS_FLEX_START]. If set to 0, the job will wait indefinitely. The default is 24 hours.""",
    )
    restart_job_on_worker_restart: Optional[bool] = Field(
        default=None,
        description="""Optional. Restarts the entire CustomJob if a worker gets restarted. This feature can be used by distributed training jobs that are not resilient to workers leaving and joining a job.""",
    )
    strategy: Optional[Strategy] = Field(
        default=None,
        description="""Optional. This determines which type of scheduling strategy to use.""",
    )
    timeout: Optional[str] = Field(
        default=None,
        description="""Optional. The maximum job running time. The default is 7 days.""",
    )


class SchedulingDict(TypedDict, total=False):
    """All parameters related to queuing and scheduling of custom jobs."""

    disable_retries: Optional[bool]
    """Optional. Indicates if the job should retry for internal errors after the job starts running. If true, overrides `Scheduling.restart_job_on_worker_restart` to false."""

    max_wait_duration: Optional[str]
    """Optional. This is the maximum duration that a job will wait for the requested resources to be provisioned if the scheduling strategy is set to [Strategy.DWS_FLEX_START]. If set to 0, the job will wait indefinitely. The default is 24 hours."""

    restart_job_on_worker_restart: Optional[bool]
    """Optional. Restarts the entire CustomJob if a worker gets restarted. This feature can be used by distributed training jobs that are not resilient to workers leaving and joining a job."""

    strategy: Optional[Strategy]
    """Optional. This determines which type of scheduling strategy to use."""

    timeout: Optional[str]
    """Optional. The maximum job running time. The default is 7 days."""


SchedulingOrDict = Union[Scheduling, SchedulingDict]


class EnvVar(_common.BaseModel):
    """Represents an environment variable present in a Container or Python Module."""

    name: Optional[str] = Field(
        default=None,
        description="""Required. Name of the environment variable. Must be a valid C identifier.""",
    )
    value: Optional[str] = Field(
        default=None,
        description="""Required. Variables that reference a $(VAR_NAME) are expanded using the previous defined environment variables in the container and any service environment variables. If a variable cannot be resolved, the reference in the input string will be unchanged. The $(VAR_NAME) syntax can be escaped with a double $$, ie: $$(VAR_NAME). Escaped references will never be expanded, regardless of whether the variable exists or not.""",
    )


class EnvVarDict(TypedDict, total=False):
    """Represents an environment variable present in a Container or Python Module."""

    name: Optional[str]
    """Required. Name of the environment variable. Must be a valid C identifier."""

    value: Optional[str]
    """Required. Variables that reference a $(VAR_NAME) are expanded using the previous defined environment variables in the container and any service environment variables. If a variable cannot be resolved, the reference in the input string will be unchanged. The $(VAR_NAME) syntax can be escaped with a double $$, ie: $$(VAR_NAME). Escaped references will never be expanded, regardless of whether the variable exists or not."""


EnvVarOrDict = Union[EnvVar, EnvVarDict]


class ContainerSpec(_common.BaseModel):
    """The spec of a Container."""

    args: Optional[list[str]] = Field(
        default=None,
        description="""The arguments to be passed when starting the container.""",
    )
    command: Optional[list[str]] = Field(
        default=None,
        description="""The command to be invoked when the container is started. It overrides the entrypoint instruction in Dockerfile when provided.""",
    )
    env: Optional[list[EnvVar]] = Field(
        default=None,
        description="""Environment variables to be passed to the container. Maximum limit is 100.""",
    )
    image_uri: Optional[str] = Field(
        default=None,
        description="""Required. The URI of a container image in the Container Registry that is to be run on each worker replica.""",
    )


class ContainerSpecDict(TypedDict, total=False):
    """The spec of a Container."""

    args: Optional[list[str]]
    """The arguments to be passed when starting the container."""

    command: Optional[list[str]]
    """The command to be invoked when the container is started. It overrides the entrypoint instruction in Dockerfile when provided."""

    env: Optional[list[EnvVarDict]]
    """Environment variables to be passed to the container. Maximum limit is 100."""

    image_uri: Optional[str]
    """Required. The URI of a container image in the Container Registry that is to be run on each worker replica."""


ContainerSpecOrDict = Union[ContainerSpec, ContainerSpecDict]


class DiskSpec(_common.BaseModel):
    """Represents the spec of disk options."""

    boot_disk_size_gb: Optional[int] = Field(
        default=None,
        description="""Size in GB of the boot disk (default is 100GB).""",
    )
    boot_disk_type: Optional[str] = Field(
        default=None,
        description="""Type of the boot disk. For non-A3U machines, the default value is "pd-ssd", for A3U machines, the default value is "hyperdisk-balanced". Valid values: "pd-ssd" (Persistent Disk Solid State Drive), "pd-standard" (Persistent Disk Hard Disk Drive) or "hyperdisk-balanced".""",
    )


class DiskSpecDict(TypedDict, total=False):
    """Represents the spec of disk options."""

    boot_disk_size_gb: Optional[int]
    """Size in GB of the boot disk (default is 100GB)."""

    boot_disk_type: Optional[str]
    """Type of the boot disk. For non-A3U machines, the default value is "pd-ssd", for A3U machines, the default value is "hyperdisk-balanced". Valid values: "pd-ssd" (Persistent Disk Solid State Drive), "pd-standard" (Persistent Disk Hard Disk Drive) or "hyperdisk-balanced"."""


DiskSpecOrDict = Union[DiskSpec, DiskSpecDict]


class ReservationAffinity(_common.BaseModel):
    """A ReservationAffinity can be used to configure a Vertex AI resource (e.g., a DeployedModel) to draw its Compute Engine resources from a Shared Reservation, or exclusively from on-demand capacity."""

    key: Optional[str] = Field(
        default=None,
        description="""Optional. Corresponds to the label key of a reservation resource. To target a SPECIFIC_RESERVATION by name, use `compute.googleapis.com/reservation-name` as the key and specify the name of your reservation as its value.""",
    )
    reservation_affinity_type: Optional[Type] = Field(
        default=None,
        description="""Required. Specifies the reservation affinity type.""",
    )
    values: Optional[list[str]] = Field(
        default=None,
        description="""Optional. Corresponds to the label values of a reservation resource. This must be the full resource name of the reservation or reservation block.""",
    )


class ReservationAffinityDict(TypedDict, total=False):
    """A ReservationAffinity can be used to configure a Vertex AI resource (e.g., a DeployedModel) to draw its Compute Engine resources from a Shared Reservation, or exclusively from on-demand capacity."""

    key: Optional[str]
    """Optional. Corresponds to the label key of a reservation resource. To target a SPECIFIC_RESERVATION by name, use `compute.googleapis.com/reservation-name` as the key and specify the name of your reservation as its value."""

    reservation_affinity_type: Optional[Type]
    """Required. Specifies the reservation affinity type."""

    values: Optional[list[str]]
    """Optional. Corresponds to the label values of a reservation resource. This must be the full resource name of the reservation or reservation block."""


ReservationAffinityOrDict = Union[ReservationAffinity, ReservationAffinityDict]


class MachineSpec(_common.BaseModel):
    """Specification of a single machine."""

    accelerator_count: Optional[int] = Field(
        default=None,
        description="""The number of accelerators to attach to the machine.""",
    )
    accelerator_type: Optional[AcceleratorType] = Field(
        default=None,
        description="""Immutable. The type of accelerator(s) that may be attached to the machine as per accelerator_count.""",
    )
    machine_type: Optional[str] = Field(
        default=None,
        description="""Immutable. The type of the machine. See the [list of machine types supported for prediction](https://cloud.google.com/vertex-ai/docs/predictions/configure-compute#machine-types) See the [list of machine types supported for custom training](https://cloud.google.com/vertex-ai/docs/training/configure-compute#machine-types). For DeployedModel this field is optional, and the default value is `n1-standard-2`. For BatchPredictionJob or as part of WorkerPoolSpec this field is required.""",
    )
    multihost_gpu_node_count: Optional[int] = Field(
        default=None,
        description="""Optional. Immutable. The number of nodes per replica for multihost GPU deployments.""",
    )
    reservation_affinity: Optional[ReservationAffinity] = Field(
        default=None,
        description="""Optional. Immutable. Configuration controlling how this resource pool consumes reservation.""",
    )
    tpu_topology: Optional[str] = Field(
        default=None,
        description="""Immutable. The topology of the TPUs. Corresponds to the TPU topologies available from GKE. (Example: tpu_topology: "2x2x1").""",
    )


class MachineSpecDict(TypedDict, total=False):
    """Specification of a single machine."""

    accelerator_count: Optional[int]
    """The number of accelerators to attach to the machine."""

    accelerator_type: Optional[AcceleratorType]
    """Immutable. The type of accelerator(s) that may be attached to the machine as per accelerator_count."""

    machine_type: Optional[str]
    """Immutable. The type of the machine. See the [list of machine types supported for prediction](https://cloud.google.com/vertex-ai/docs/predictions/configure-compute#machine-types) See the [list of machine types supported for custom training](https://cloud.google.com/vertex-ai/docs/training/configure-compute#machine-types). For DeployedModel this field is optional, and the default value is `n1-standard-2`. For BatchPredictionJob or as part of WorkerPoolSpec this field is required."""

    multihost_gpu_node_count: Optional[int]
    """Optional. Immutable. The number of nodes per replica for multihost GPU deployments."""

    reservation_affinity: Optional[ReservationAffinityDict]
    """Optional. Immutable. Configuration controlling how this resource pool consumes reservation."""

    tpu_topology: Optional[str]
    """Immutable. The topology of the TPUs. Corresponds to the TPU topologies available from GKE. (Example: tpu_topology: "2x2x1")."""


MachineSpecOrDict = Union[MachineSpec, MachineSpecDict]


class NfsMount(_common.BaseModel):
    """Represents a mount configuration for Network File System (NFS) to mount."""

    mount_point: Optional[str] = Field(
        default=None,
        description="""Required. Destination mount path. The NFS will be mounted for the user under /mnt/nfs/""",
    )
    path: Optional[str] = Field(
        default=None,
        description="""Required. Source path exported from NFS server. Has to start with '/', and combined with the ip address, it indicates the source mount path in the form of `server:path`""",
    )
    server: Optional[str] = Field(
        default=None, description="""Required. IP address of the NFS server."""
    )


class NfsMountDict(TypedDict, total=False):
    """Represents a mount configuration for Network File System (NFS) to mount."""

    mount_point: Optional[str]
    """Required. Destination mount path. The NFS will be mounted for the user under /mnt/nfs/"""

    path: Optional[str]
    """Required. Source path exported from NFS server. Has to start with '/', and combined with the ip address, it indicates the source mount path in the form of `server:path`"""

    server: Optional[str]
    """Required. IP address of the NFS server."""


NfsMountOrDict = Union[NfsMount, NfsMountDict]


class PythonPackageSpec(_common.BaseModel):
    """The spec of a Python packaged code."""

    args: Optional[list[str]] = Field(
        default=None,
        description="""Command line arguments to be passed to the Python task.""",
    )
    env: Optional[list[EnvVar]] = Field(
        default=None,
        description="""Environment variables to be passed to the python module. Maximum limit is 100.""",
    )
    executor_image_uri: Optional[str] = Field(
        default=None,
        description="""Required. The URI of a container image in Artifact Registry that will run the provided Python package. Vertex AI provides a wide range of executor images with pre-installed packages to meet users' various use cases. See the list of [pre-built containers for training](https://cloud.google.com/vertex-ai/docs/training/pre-built-containers). You must use an image from this list.""",
    )
    package_uris: Optional[list[str]] = Field(
        default=None,
        description="""Required. The Google Cloud Storage location of the Python package files which are the training program and its dependent packages. The maximum number of package URIs is 100.""",
    )
    python_module: Optional[str] = Field(
        default=None,
        description="""Required. The Python module name to run after installing the packages.""",
    )


class PythonPackageSpecDict(TypedDict, total=False):
    """The spec of a Python packaged code."""

    args: Optional[list[str]]
    """Command line arguments to be passed to the Python task."""

    env: Optional[list[EnvVarDict]]
    """Environment variables to be passed to the python module. Maximum limit is 100."""

    executor_image_uri: Optional[str]
    """Required. The URI of a container image in Artifact Registry that will run the provided Python package. Vertex AI provides a wide range of executor images with pre-installed packages to meet users' various use cases. See the list of [pre-built containers for training](https://cloud.google.com/vertex-ai/docs/training/pre-built-containers). You must use an image from this list."""

    package_uris: Optional[list[str]]
    """Required. The Google Cloud Storage location of the Python package files which are the training program and its dependent packages. The maximum number of package URIs is 100."""

    python_module: Optional[str]
    """Required. The Python module name to run after installing the packages."""


PythonPackageSpecOrDict = Union[PythonPackageSpec, PythonPackageSpecDict]


class WorkerPoolSpec(_common.BaseModel):
    """Represents the spec of a worker pool in a job."""

    container_spec: Optional[ContainerSpec] = Field(
        default=None, description="""The custom container task."""
    )
    disk_spec: Optional[DiskSpec] = Field(default=None, description="""Disk spec.""")
    machine_spec: Optional[MachineSpec] = Field(
        default=None,
        description="""Optional. Immutable. The specification of a single machine.""",
    )
    nfs_mounts: Optional[list[NfsMount]] = Field(
        default=None, description="""Optional. List of NFS mount spec."""
    )
    python_package_spec: Optional[PythonPackageSpec] = Field(
        default=None, description="""The Python packaged task."""
    )
    replica_count: Optional[int] = Field(
        default=None,
        description="""Optional. The number of worker replicas to use for this worker pool.""",
    )


class WorkerPoolSpecDict(TypedDict, total=False):
    """Represents the spec of a worker pool in a job."""

    container_spec: Optional[ContainerSpecDict]
    """The custom container task."""

    disk_spec: Optional[DiskSpecDict]
    """Disk spec."""

    machine_spec: Optional[MachineSpecDict]
    """Optional. Immutable. The specification of a single machine."""

    nfs_mounts: Optional[list[NfsMountDict]]
    """Optional. List of NFS mount spec."""

    python_package_spec: Optional[PythonPackageSpecDict]
    """The Python packaged task."""

    replica_count: Optional[int]
    """Optional. The number of worker replicas to use for this worker pool."""


WorkerPoolSpecOrDict = Union[WorkerPoolSpec, WorkerPoolSpecDict]


class CustomJobSpec(_common.BaseModel):
    """Represents a job that runs custom workloads such as a Docker container or a Python package."""

    base_output_directory: Optional[GcsDestination] = Field(
        default=None,
        description="""The Cloud Storage location to store the output of this CustomJob or HyperparameterTuningJob. For HyperparameterTuningJob, the baseOutputDirectory of each child CustomJob backing a Trial is set to a subdirectory of name id under its parent HyperparameterTuningJob's baseOutputDirectory. The following Vertex AI environment variables will be passed to containers or python modules when this field is set: For CustomJob: * AIP_MODEL_DIR = `/model/` * AIP_CHECKPOINT_DIR = `/checkpoints/` * AIP_TENSORBOARD_LOG_DIR = `/logs/` For CustomJob backing a Trial of HyperparameterTuningJob: * AIP_MODEL_DIR = `//model/` * AIP_CHECKPOINT_DIR = `//checkpoints/` * AIP_TENSORBOARD_LOG_DIR = `//logs/`""",
    )
    enable_dashboard_access: Optional[bool] = Field(
        default=None,
        description="""Optional. Whether you want Vertex AI to enable access to the customized dashboard in training chief container. If set to `true`, you can access the dashboard at the URIs given by CustomJob.web_access_uris or Trial.web_access_uris (within HyperparameterTuningJob.trials).""",
    )
    enable_web_access: Optional[bool] = Field(
        default=None,
        description="""Optional. Whether you want Vertex AI to enable [interactive shell access](https://cloud.google.com/vertex-ai/docs/training/monitor-debug-interactive-shell) to training containers. If set to `true`, you can access interactive shells at the URIs given by CustomJob.web_access_uris or Trial.web_access_uris (within HyperparameterTuningJob.trials).""",
    )
    experiment: Optional[str] = Field(
        default=None,
        description="""Optional. The Experiment associated with this job. Format: `projects/{project}/locations/{location}/metadataStores/{metadataStores}/contexts/{experiment-name}`""",
    )
    experiment_run: Optional[str] = Field(
        default=None,
        description="""Optional. The Experiment Run associated with this job. Format: `projects/{project}/locations/{location}/metadataStores/{metadataStores}/contexts/{experiment-name}-{experiment-run-name}`""",
    )
    models: Optional[list[str]] = Field(
        default=None,
        description="""Optional. The name of the Model resources for which to generate a mapping to artifact URIs. Applicable only to some of the Google-provided custom jobs. Format: `projects/{project}/locations/{location}/models/{model}` In order to retrieve a specific version of the model, also provide the version ID or version alias. Example: `projects/{project}/locations/{location}/models/{model}@2` or `projects/{project}/locations/{location}/models/{model}@golden` If no version ID or alias is specified, the "default" version will be returned. The "default" version alias is created for the first version of the model, and can be moved to other versions later on. There will be exactly one default version.""",
    )
    network: Optional[str] = Field(
        default=None,
        description="""Optional. The full name of the Compute Engine [network](/compute/docs/networks-and-firewalls#networks) to which the Job should be peered. For example, `projects/12345/global/networks/myVPC`. [Format](/compute/docs/reference/rest/v1/networks/insert) is of the form `projects/{project}/global/networks/{network}`. Where {project} is a project number, as in `12345`, and {network} is a network name. To specify this field, you must have already [configured VPC Network Peering for Vertex AI](https://cloud.google.com/vertex-ai/docs/general/vpc-peering). If this field is left unspecified, the job is not peered with any network.""",
    )
    persistent_resource_id: Optional[str] = Field(
        default=None,
        description="""Optional. The ID of the PersistentResource in the same Project and Location which to run If this is specified, the job will be run on existing machines held by the PersistentResource instead of on-demand short-live machines. The network and CMEK configs on the job should be consistent with those on the PersistentResource, otherwise, the job will be rejected.""",
    )
    protected_artifact_location_id: Optional[str] = Field(
        default=None,
        description="""The ID of the location to store protected artifacts. e.g. us-central1. Populate only when the location is different than CustomJob location. List of supported locations: https://cloud.google.com/vertex-ai/docs/general/locations""",
    )
    psc_interface_config: Optional[PscInterfaceConfig] = Field(
        default=None,
        description="""Optional. Configuration for PSC-I for CustomJob.""",
    )
    reserved_ip_ranges: Optional[list[str]] = Field(
        default=None,
        description="""Optional. A list of names for the reserved ip ranges under the VPC network that can be used for this job. If set, we will deploy the job within the provided ip ranges. Otherwise, the job will be deployed to any ip ranges under the provided VPC network. Example: ['vertex-ai-ip-range'].""",
    )
    scheduling: Optional[Scheduling] = Field(
        default=None, description="""Scheduling options for a CustomJob."""
    )
    service_account: Optional[str] = Field(
        default=None,
        description="""Specifies the service account for workload run-as account. Users submitting jobs must have act-as permission on this run-as account. If unspecified, the [Vertex AI Custom Code Service Agent](https://cloud.google.com/vertex-ai/docs/general/access-control#service-agents) for the CustomJob's project is used.""",
    )
    tensorboard: Optional[str] = Field(
        default=None,
        description="""Optional. The name of a Vertex AI Tensorboard resource to which this CustomJob will upload Tensorboard logs. Format: `projects/{project}/locations/{location}/tensorboards/{tensorboard}`""",
    )
    worker_pool_specs: Optional[list[WorkerPoolSpec]] = Field(
        default=None,
        description="""Required. The spec of the worker pools including machine type and Docker image. All worker pools except the first one are optional and can be skipped by providing an empty value.""",
    )


class CustomJobSpecDict(TypedDict, total=False):
    """Represents a job that runs custom workloads such as a Docker container or a Python package."""

    base_output_directory: Optional[GcsDestinationDict]
    """The Cloud Storage location to store the output of this CustomJob or HyperparameterTuningJob. For HyperparameterTuningJob, the baseOutputDirectory of each child CustomJob backing a Trial is set to a subdirectory of name id under its parent HyperparameterTuningJob's baseOutputDirectory. The following Vertex AI environment variables will be passed to containers or python modules when this field is set: For CustomJob: * AIP_MODEL_DIR = `/model/` * AIP_CHECKPOINT_DIR = `/checkpoints/` * AIP_TENSORBOARD_LOG_DIR = `/logs/` For CustomJob backing a Trial of HyperparameterTuningJob: * AIP_MODEL_DIR = `//model/` * AIP_CHECKPOINT_DIR = `//checkpoints/` * AIP_TENSORBOARD_LOG_DIR = `//logs/`"""

    enable_dashboard_access: Optional[bool]
    """Optional. Whether you want Vertex AI to enable access to the customized dashboard in training chief container. If set to `true`, you can access the dashboard at the URIs given by CustomJob.web_access_uris or Trial.web_access_uris (within HyperparameterTuningJob.trials)."""

    enable_web_access: Optional[bool]
    """Optional. Whether you want Vertex AI to enable [interactive shell access](https://cloud.google.com/vertex-ai/docs/training/monitor-debug-interactive-shell) to training containers. If set to `true`, you can access interactive shells at the URIs given by CustomJob.web_access_uris or Trial.web_access_uris (within HyperparameterTuningJob.trials)."""

    experiment: Optional[str]
    """Optional. The Experiment associated with this job. Format: `projects/{project}/locations/{location}/metadataStores/{metadataStores}/contexts/{experiment-name}`"""

    experiment_run: Optional[str]
    """Optional. The Experiment Run associated with this job. Format: `projects/{project}/locations/{location}/metadataStores/{metadataStores}/contexts/{experiment-name}-{experiment-run-name}`"""

    models: Optional[list[str]]
    """Optional. The name of the Model resources for which to generate a mapping to artifact URIs. Applicable only to some of the Google-provided custom jobs. Format: `projects/{project}/locations/{location}/models/{model}` In order to retrieve a specific version of the model, also provide the version ID or version alias. Example: `projects/{project}/locations/{location}/models/{model}@2` or `projects/{project}/locations/{location}/models/{model}@golden` If no version ID or alias is specified, the "default" version will be returned. The "default" version alias is created for the first version of the model, and can be moved to other versions later on. There will be exactly one default version."""

    network: Optional[str]
    """Optional. The full name of the Compute Engine [network](/compute/docs/networks-and-firewalls#networks) to which the Job should be peered. For example, `projects/12345/global/networks/myVPC`. [Format](/compute/docs/reference/rest/v1/networks/insert) is of the form `projects/{project}/global/networks/{network}`. Where {project} is a project number, as in `12345`, and {network} is a network name. To specify this field, you must have already [configured VPC Network Peering for Vertex AI](https://cloud.google.com/vertex-ai/docs/general/vpc-peering). If this field is left unspecified, the job is not peered with any network."""

    persistent_resource_id: Optional[str]
    """Optional. The ID of the PersistentResource in the same Project and Location which to run If this is specified, the job will be run on existing machines held by the PersistentResource instead of on-demand short-live machines. The network and CMEK configs on the job should be consistent with those on the PersistentResource, otherwise, the job will be rejected."""

    protected_artifact_location_id: Optional[str]
    """The ID of the location to store protected artifacts. e.g. us-central1. Populate only when the location is different than CustomJob location. List of supported locations: https://cloud.google.com/vertex-ai/docs/general/locations"""

    psc_interface_config: Optional[PscInterfaceConfigDict]
    """Optional. Configuration for PSC-I for CustomJob."""

    reserved_ip_ranges: Optional[list[str]]
    """Optional. A list of names for the reserved ip ranges under the VPC network that can be used for this job. If set, we will deploy the job within the provided ip ranges. Otherwise, the job will be deployed to any ip ranges under the provided VPC network. Example: ['vertex-ai-ip-range']."""

    scheduling: Optional[SchedulingDict]
    """Scheduling options for a CustomJob."""

    service_account: Optional[str]
    """Specifies the service account for workload run-as account. Users submitting jobs must have act-as permission on this run-as account. If unspecified, the [Vertex AI Custom Code Service Agent](https://cloud.google.com/vertex-ai/docs/general/access-control#service-agents) for the CustomJob's project is used."""

    tensorboard: Optional[str]
    """Optional. The name of a Vertex AI Tensorboard resource to which this CustomJob will upload Tensorboard logs. Format: `projects/{project}/locations/{location}/tensorboards/{tensorboard}`"""

    worker_pool_specs: Optional[list[WorkerPoolSpecDict]]
    """Required. The spec of the worker pools including machine type and Docker image. All worker pools except the first one are optional and can be skipped by providing an empty value."""


CustomJobSpecOrDict = Union[CustomJobSpec, CustomJobSpecDict]


class EncryptionSpec(_common.BaseModel):
    """Represents a customer-managed encryption key spec that can be applied to a top-level resource."""

    kms_key_name: Optional[str] = Field(
        default=None,
        description="""Required. The Cloud KMS resource identifier of the customer managed encryption key used to protect a resource. Has the form: `projects/my-project/locations/my-region/keyRings/my-kr/cryptoKeys/my-key`. The key needs to be in the same region as where the compute resource is created.""",
    )


class EncryptionSpecDict(TypedDict, total=False):
    """Represents a customer-managed encryption key spec that can be applied to a top-level resource."""

    kms_key_name: Optional[str]
    """Required. The Cloud KMS resource identifier of the customer managed encryption key used to protect a resource. Has the form: `projects/my-project/locations/my-region/keyRings/my-kr/cryptoKeys/my-key`. The key needs to be in the same region as where the compute resource is created."""


EncryptionSpecOrDict = Union[EncryptionSpec, EncryptionSpecDict]


class GoogleRpcStatus(_common.BaseModel):
    """The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs.

    It is used by [gRPC](https://github.com/grpc). Each `Status` message
    contains three pieces of data: error code, error message, and error details.
    You can find out more about this error model and how to work with it in the
    [API Design Guide](https://cloud.google.com/apis/design/errors).
    """

    code: Optional[int] = Field(
        default=None,
        description="""The status code, which should be an enum value of google.rpc.Code.""",
    )
    details: Optional[list[dict[str, Any]]] = Field(
        default=None,
        description="""A list of messages that carry the error details. There is a common set of message types for APIs to use.""",
    )
    message: Optional[str] = Field(
        default=None,
        description="""A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.""",
    )


class GoogleRpcStatusDict(TypedDict, total=False):
    """The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs.

    It is used by [gRPC](https://github.com/grpc). Each `Status` message
    contains three pieces of data: error code, error message, and error details.
    You can find out more about this error model and how to work with it in the
    [API Design Guide](https://cloud.google.com/apis/design/errors).
    """

    code: Optional[int]
    """The status code, which should be an enum value of google.rpc.Code."""

    details: Optional[list[dict[str, Any]]]
    """A list of messages that carry the error details. There is a common set of message types for APIs to use."""

    message: Optional[str]
    """A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client."""


GoogleRpcStatusOrDict = Union[GoogleRpcStatus, GoogleRpcStatusDict]


class CustomJob(_common.BaseModel):
    """Represents a job that runs custom workloads such as a Docker container or a Python package."""

    display_name: Optional[str] = Field(
        default=None,
        description="""Required. The display name of the CustomJob. The name can be up to 128 characters long and can consist of any UTF-8 characters.""",
    )
    job_spec: Optional[CustomJobSpec] = Field(
        default=None, description="""Required. Job spec."""
    )
    create_time: Optional[datetime.datetime] = Field(
        default=None,
        description="""Output only. Time when the CustomJob was created.""",
    )
    encryption_spec: Optional[EncryptionSpec] = Field(
        default=None,
        description="""Customer-managed encryption key options for a CustomJob. If this is set, then all resources created by the CustomJob will be encrypted with the provided encryption key.""",
    )
    end_time: Optional[datetime.datetime] = Field(
        default=None,
        description="""Output only. Time when the CustomJob entered any of the following states: `JOB_STATE_SUCCEEDED`, `JOB_STATE_FAILED`, `JOB_STATE_CANCELLED`.""",
    )
    error: Optional[GoogleRpcStatus] = Field(
        default=None,
        description="""Output only. Only populated when job's state is `JOB_STATE_FAILED` or `JOB_STATE_CANCELLED`.""",
    )
    labels: Optional[dict[str, str]] = Field(
        default=None,
        description="""The labels with user-defined metadata to organize CustomJobs. Label keys and values can be no longer than 64 characters (Unicode codepoints), can only contain lowercase letters, numeric characters, underscores and dashes. International characters are allowed. See https://goo.gl/xmQnxf for more information and examples of labels.""",
    )
    name: Optional[str] = Field(
        default=None,
        description="""Output only. Resource name of a CustomJob.""",
    )
    satisfies_pzi: Optional[bool] = Field(
        default=None, description="""Output only. Reserved for future use."""
    )
    satisfies_pzs: Optional[bool] = Field(
        default=None, description="""Output only. Reserved for future use."""
    )
    start_time: Optional[datetime.datetime] = Field(
        default=None,
        description="""Output only. Time when the CustomJob for the first time entered the `JOB_STATE_RUNNING` state.""",
    )
    state: Optional[JobState] = Field(
        default=None,
        description="""Output only. The detailed state of the job.""",
    )
    update_time: Optional[datetime.datetime] = Field(
        default=None,
        description="""Output only. Time when the CustomJob was most recently updated.""",
    )
    web_access_uris: Optional[dict[str, str]] = Field(
        default=None,
        description="""Output only. URIs for accessing [interactive shells](https://cloud.google.com/vertex-ai/docs/training/monitor-debug-interactive-shell) (one URI for each training node). Only available if job_spec.enable_web_access is `true`. The keys are names of each node in the training job; for example, `workerpool0-0` for the primary node, `workerpool1-0` for the first node in the second worker pool, and `workerpool1-1` for the second node in the second worker pool. The values are the URIs for each node's interactive shell.""",
    )


class CustomJobDict(TypedDict, total=False):
    """Represents a job that runs custom workloads such as a Docker container or a Python package."""

    display_name: Optional[str]
    """Required. The display name of the CustomJob. The name can be up to 128 characters long and can consist of any UTF-8 characters."""

    job_spec: Optional[CustomJobSpecDict]
    """Required. Job spec."""

    create_time: Optional[datetime.datetime]
    """Output only. Time when the CustomJob was created."""

    encryption_spec: Optional[EncryptionSpecDict]
    """Customer-managed encryption key options for a CustomJob. If this is set, then all resources created by the CustomJob will be encrypted with the provided encryption key."""

    end_time: Optional[datetime.datetime]
    """Output only. Time when the CustomJob entered any of the following states: `JOB_STATE_SUCCEEDED`, `JOB_STATE_FAILED`, `JOB_STATE_CANCELLED`."""

    error: Optional[GoogleRpcStatusDict]
    """Output only. Only populated when job's state is `JOB_STATE_FAILED` or `JOB_STATE_CANCELLED`."""

    labels: Optional[dict[str, str]]
    """The labels with user-defined metadata to organize CustomJobs. Label keys and values can be no longer than 64 characters (Unicode codepoints), can only contain lowercase letters, numeric characters, underscores and dashes. International characters are allowed. See https://goo.gl/xmQnxf for more information and examples of labels."""

    name: Optional[str]
    """Output only. Resource name of a CustomJob."""

    satisfies_pzi: Optional[bool]
    """Output only. Reserved for future use."""

    satisfies_pzs: Optional[bool]
    """Output only. Reserved for future use."""

    start_time: Optional[datetime.datetime]
    """Output only. Time when the CustomJob for the first time entered the `JOB_STATE_RUNNING` state."""

    state: Optional[JobState]
    """Output only. The detailed state of the job."""

    update_time: Optional[datetime.datetime]
    """Output only. Time when the CustomJob was most recently updated."""

    web_access_uris: Optional[dict[str, str]]
    """Output only. URIs for accessing [interactive shells](https://cloud.google.com/vertex-ai/docs/training/monitor-debug-interactive-shell) (one URI for each training node). Only available if job_spec.enable_web_access is `true`. The keys are names of each node in the training job; for example, `workerpool0-0` for the primary node, `workerpool1-0` for the first node in the second worker pool, and `workerpool1-1` for the second node in the second worker pool. The values are the URIs for each node's interactive shell."""


CustomJobOrDict = Union[CustomJob, CustomJobDict]


class BaseConfig(_common.BaseModel):

    http_options: Optional[HttpOptions] = Field(
        default=None, description="""Used to override HTTP request options."""
    )


class BaseConfigDict(TypedDict, total=False):

    http_options: Optional[HttpOptionsDict]
    """Used to override HTTP request options."""


BaseConfigOrDict = Union[BaseConfig, BaseConfigDict]


class _CustomJobParameters(_common.BaseModel):
    """Represents a job that runs custom workloads such as a Docker container or a Python package."""

    custom_job: Optional[CustomJob] = Field(default=None, description="""""")
    config: Optional[BaseConfig] = Field(default=None, description="""""")


class _CustomJobParametersDict(TypedDict, total=False):
    """Represents a job that runs custom workloads such as a Docker container or a Python package."""

    custom_job: Optional[CustomJobDict]
    """"""

    config: Optional[BaseConfigDict]
    """"""


_CustomJobParametersOrDict = Union[_CustomJobParameters, _CustomJobParametersDict]


class _GetCustomJobParameters(_common.BaseModel):
    """Represents a job that runs custom workloads such as a Docker container or a Python package."""

    name: Optional[str] = Field(default=None, description="""""")
    config: Optional[BaseConfig] = Field(default=None, description="""""")


class _GetCustomJobParametersDict(TypedDict, total=False):
    """Represents a job that runs custom workloads such as a Docker container or a Python package."""

    name: Optional[str]
    """"""

    config: Optional[BaseConfigDict]
    """"""


_GetCustomJobParametersOrDict = Union[
    _GetCustomJobParameters, _GetCustomJobParametersDict
]


class SecretRef(_common.BaseModel):
    """Reference to a secret stored in the Cloud Secret Manager that will provide the value for this environment variable."""

    secret: Optional[str] = Field(
        default=None,
        description="""Required. The name of the secret in Cloud Secret Manager. Format: {secret_name}.""",
    )
    version: Optional[str] = Field(
        default=None,
        description="""The Cloud Secret Manager secret version. Can be 'latest' for the latest version, an integer for a specific version, or a version alias.""",
    )


class SecretRefDict(TypedDict, total=False):
    """Reference to a secret stored in the Cloud Secret Manager that will provide the value for this environment variable."""

    secret: Optional[str]
    """Required. The name of the secret in Cloud Secret Manager. Format: {secret_name}."""

    version: Optional[str]
    """The Cloud Secret Manager secret version. Can be 'latest' for the latest version, an integer for a specific version, or a version alias."""


SecretRefOrDict = Union[SecretRef, SecretRefDict]


class SecretEnvVar(_common.BaseModel):
    """Represents an environment variable where the value is a secret in Cloud Secret Manager."""

    name: Optional[str] = Field(
        default=None,
        description="""Required. Name of the secret environment variable.""",
    )
    secret_ref: Optional[SecretRef] = Field(
        default=None,
        description="""Required. Reference to a secret stored in the Cloud Secret Manager that will provide the value for this environment variable.""",
    )


class SecretEnvVarDict(TypedDict, total=False):
    """Represents an environment variable where the value is a secret in Cloud Secret Manager."""

    name: Optional[str]
    """Required. Name of the secret environment variable."""

    secret_ref: Optional[SecretRefDict]
    """Required. Reference to a secret stored in the Cloud Secret Manager that will provide the value for this environment variable."""


SecretEnvVarOrDict = Union[SecretEnvVar, SecretEnvVarDict]


class ReasoningEngineSpecDeploymentSpec(_common.BaseModel):
    """The specification of a Reasoning Engine deployment."""

    env: Optional[list[EnvVar]] = Field(
        default=None,
        description="""Optional. Environment variables to be set with the Reasoning Engine deployment. The environment variables can be updated through the UpdateReasoningEngine API.""",
    )
    secret_env: Optional[list[SecretEnvVar]] = Field(
        default=None,
        description="""Optional. Environment variables where the value is a secret in Cloud Secret Manager. To use this feature, add 'Secret Manager Secret Accessor' role (roles/secretmanager.secretAccessor) to AI Platform Reasoning Engine Service Agent.""",
    )


class ReasoningEngineSpecDeploymentSpecDict(TypedDict, total=False):
    """The specification of a Reasoning Engine deployment."""

    env: Optional[list[EnvVarDict]]
    """Optional. Environment variables to be set with the Reasoning Engine deployment. The environment variables can be updated through the UpdateReasoningEngine API."""

    secret_env: Optional[list[SecretEnvVarDict]]
    """Optional. Environment variables where the value is a secret in Cloud Secret Manager. To use this feature, add 'Secret Manager Secret Accessor' role (roles/secretmanager.secretAccessor) to AI Platform Reasoning Engine Service Agent."""


ReasoningEngineSpecDeploymentSpecOrDict = Union[
    ReasoningEngineSpecDeploymentSpec, ReasoningEngineSpecDeploymentSpecDict
]


class ReasoningEngineSpecPackageSpec(_common.BaseModel):
    """User provided package spec like pickled object and package requirements."""

    dependency_files_gcs_uri: Optional[str] = Field(
        default=None,
        description="""Optional. The Cloud Storage URI of the dependency files in tar.gz format.""",
    )
    pickle_object_gcs_uri: Optional[str] = Field(
        default=None,
        description="""Optional. The Cloud Storage URI of the pickled python object.""",
    )
    python_version: Optional[str] = Field(
        default=None,
        description="""Optional. The Python version. Currently support 3.8, 3.9, 3.10, 3.11. If not specified, default value is 3.10.""",
    )
    requirements_gcs_uri: Optional[str] = Field(
        default=None,
        description="""Optional. The Cloud Storage URI of the `requirements.txt` file""",
    )


class ReasoningEngineSpecPackageSpecDict(TypedDict, total=False):
    """User provided package spec like pickled object and package requirements."""

    dependency_files_gcs_uri: Optional[str]
    """Optional. The Cloud Storage URI of the dependency files in tar.gz format."""

    pickle_object_gcs_uri: Optional[str]
    """Optional. The Cloud Storage URI of the pickled python object."""

    python_version: Optional[str]
    """Optional. The Python version. Currently support 3.8, 3.9, 3.10, 3.11. If not specified, default value is 3.10."""

    requirements_gcs_uri: Optional[str]
    """Optional. The Cloud Storage URI of the `requirements.txt` file"""


ReasoningEngineSpecPackageSpecOrDict = Union[
    ReasoningEngineSpecPackageSpec, ReasoningEngineSpecPackageSpecDict
]


class ReasoningEngineSpec(_common.BaseModel):
    """The specification of a Reasoning Engine."""

    agent_framework: Optional[str] = Field(
        default=None,
        description="""Optional. The OSS agent framework used to develop the agent. Currently supported values: "google-adk", "langchain", "langgraph", "ag2", "llama-index", "custom".""",
    )
    class_methods: Optional[list[dict[str, Any]]] = Field(
        default=None,
        description="""Optional. Declarations for object class methods in OpenAPI specification format.""",
    )
    deployment_spec: Optional[ReasoningEngineSpecDeploymentSpec] = Field(
        default=None,
        description="""Optional. The specification of a Reasoning Engine deployment.""",
    )
    package_spec: Optional[ReasoningEngineSpecPackageSpec] = Field(
        default=None,
        description="""Optional. User provided package spec of the ReasoningEngine. Ignored when users directly specify a deployment image through `deployment_spec.first_party_image_override`, but keeping the field_behavior to avoid introducing breaking changes.""",
    )


class ReasoningEngineSpecDict(TypedDict, total=False):
    """The specification of a Reasoning Engine."""

    agent_framework: Optional[str]
    """Optional. The OSS agent framework used to develop the agent. Currently supported values: "google-adk", "langchain", "langgraph", "ag2", "llama-index", "custom"."""

    class_methods: Optional[list[dict[str, Any]]]
    """Optional. Declarations for object class methods in OpenAPI specification format."""

    deployment_spec: Optional[ReasoningEngineSpecDeploymentSpecDict]
    """Optional. The specification of a Reasoning Engine deployment."""

    package_spec: Optional[ReasoningEngineSpecPackageSpecDict]
    """Optional. User provided package spec of the ReasoningEngine. Ignored when users directly specify a deployment image through `deployment_spec.first_party_image_override`, but keeping the field_behavior to avoid introducing breaking changes."""


ReasoningEngineSpecOrDict = Union[ReasoningEngineSpec, ReasoningEngineSpecDict]


class CreateAgentEngineConfig(_common.BaseModel):
    """Config for create agent engine."""

    http_options: Optional[HttpOptions] = Field(
        default=None, description="""Used to override HTTP request options."""
    )
    display_name: Optional[str] = Field(
        default=None,
        description="""The user-defined name of the Agent Engine.

      The display name can be up to 128 characters long and can comprise any
      UTF-8 characters.
      """,
    )
    description: Optional[str] = Field(
        default=None, description="""The description of the Agent Engine."""
    )
    spec: Optional[ReasoningEngineSpec] = Field(
        default=None,
        description="""Optional. Configurations of the ReasoningEngine.""",
    )


class CreateAgentEngineConfigDict(TypedDict, total=False):
    """Config for create agent engine."""

    http_options: Optional[HttpOptionsDict]
    """Used to override HTTP request options."""

    display_name: Optional[str]
    """The user-defined name of the Agent Engine.

      The display name can be up to 128 characters long and can comprise any
      UTF-8 characters.
      """

    description: Optional[str]
    """The description of the Agent Engine."""

    spec: Optional[ReasoningEngineSpecDict]
    """Optional. Configurations of the ReasoningEngine."""


CreateAgentEngineConfigOrDict = Union[
    CreateAgentEngineConfig, CreateAgentEngineConfigDict
]


class _CreateAgentEngineRequestParameters(_common.BaseModel):
    """Parameters for creating agent engines."""

    config: Optional[CreateAgentEngineConfig] = Field(default=None, description="""""")


class _CreateAgentEngineRequestParametersDict(TypedDict, total=False):
    """Parameters for creating agent engines."""

    config: Optional[CreateAgentEngineConfigDict]
    """"""


_CreateAgentEngineRequestParametersOrDict = Union[
    _CreateAgentEngineRequestParameters, _CreateAgentEngineRequestParametersDict
]


class ReasoningEngineContextSpecMemoryBankConfigGenerationConfig(_common.BaseModel):
    """Configuration for how to generate memories."""

    model: Optional[str] = Field(
        default=None,
        description="""Required. The model used to generate memories. Format: `projects/{project}/locations/{location}/publishers/google/models/{model}` or `projects/{project}/locations/{location}/endpoints/{endpoint}`.""",
    )


class ReasoningEngineContextSpecMemoryBankConfigGenerationConfigDict(
    TypedDict, total=False
):
    """Configuration for how to generate memories."""

    model: Optional[str]
    """Required. The model used to generate memories. Format: `projects/{project}/locations/{location}/publishers/google/models/{model}` or `projects/{project}/locations/{location}/endpoints/{endpoint}`."""


ReasoningEngineContextSpecMemoryBankConfigGenerationConfigOrDict = Union[
    ReasoningEngineContextSpecMemoryBankConfigGenerationConfig,
    ReasoningEngineContextSpecMemoryBankConfigGenerationConfigDict,
]


class ReasoningEngineContextSpecMemoryBankConfigSimilaritySearchConfig(
    _common.BaseModel
):
    """Configuration for how to perform similarity search on memories."""

    embedding_model: Optional[str] = Field(
        default=None,
        description="""Required. The model used to generate embeddings to lookup similar memories. Format: `projects/{project}/locations/{location}/publishers/google/models/{model}` or `projects/{project}/locations/{location}/endpoints/{endpoint}`.""",
    )


class ReasoningEngineContextSpecMemoryBankConfigSimilaritySearchConfigDict(
    TypedDict, total=False
):
    """Configuration for how to perform similarity search on memories."""

    embedding_model: Optional[str]
    """Required. The model used to generate embeddings to lookup similar memories. Format: `projects/{project}/locations/{location}/publishers/google/models/{model}` or `projects/{project}/locations/{location}/endpoints/{endpoint}`."""


ReasoningEngineContextSpecMemoryBankConfigSimilaritySearchConfigOrDict = Union[
    ReasoningEngineContextSpecMemoryBankConfigSimilaritySearchConfig,
    ReasoningEngineContextSpecMemoryBankConfigSimilaritySearchConfigDict,
]


class ReasoningEngineContextSpecMemoryBankConfig(_common.BaseModel):
    """Specification for a Memory Bank."""

    generation_config: Optional[
        ReasoningEngineContextSpecMemoryBankConfigGenerationConfig
    ] = Field(
        default=None,
        description="""Optional. Configuration for how to generate memories for the Memory Bank.""",
    )
    similarity_search_config: Optional[
        ReasoningEngineContextSpecMemoryBankConfigSimilaritySearchConfig
    ] = Field(
        default=None,
        description="""Optional. Configuration for how to perform similarity search on memories. If not set, the Memory Bank will use the default embedding model `text-embedding-005`.""",
    )


class ReasoningEngineContextSpecMemoryBankConfigDict(TypedDict, total=False):
    """Specification for a Memory Bank."""

    generation_config: Optional[
        ReasoningEngineContextSpecMemoryBankConfigGenerationConfigDict
    ]
    """Optional. Configuration for how to generate memories for the Memory Bank."""

    similarity_search_config: Optional[
        ReasoningEngineContextSpecMemoryBankConfigSimilaritySearchConfigDict
    ]
    """Optional. Configuration for how to perform similarity search on memories. If not set, the Memory Bank will use the default embedding model `text-embedding-005`."""


ReasoningEngineContextSpecMemoryBankConfigOrDict = Union[
    ReasoningEngineContextSpecMemoryBankConfig,
    ReasoningEngineContextSpecMemoryBankConfigDict,
]


class ReasoningEngineContextSpec(_common.BaseModel):
    """Configuration for how Agent Engine sub-resources should manage context."""

    memory_bank_config: Optional[ReasoningEngineContextSpecMemoryBankConfig] = Field(
        default=None,
        description="""Optional. Specification for a Memory Bank, which manages memories for the Agent Engine.""",
    )


class ReasoningEngineContextSpecDict(TypedDict, total=False):
    """Configuration for how Agent Engine sub-resources should manage context."""

    memory_bank_config: Optional[ReasoningEngineContextSpecMemoryBankConfigDict]
    """Optional. Specification for a Memory Bank, which manages memories for the Agent Engine."""


ReasoningEngineContextSpecOrDict = Union[
    ReasoningEngineContextSpec, ReasoningEngineContextSpecDict
]


class ReasoningEngine(_common.BaseModel):
    """An agent engine."""

    context_spec: Optional[ReasoningEngineContextSpec] = Field(
        default=None,
        description="""Optional. Configuration for how Agent Engine sub-resources should manage context.""",
    )
    create_time: Optional[datetime.datetime] = Field(
        default=None,
        description="""Output only. Timestamp when this ReasoningEngine was created.""",
    )
    description: Optional[str] = Field(
        default=None,
        description="""Optional. The description of the ReasoningEngine.""",
    )
    display_name: Optional[str] = Field(
        default=None,
        description="""Required. The display name of the ReasoningEngine.""",
    )
    etag: Optional[str] = Field(
        default=None,
        description="""Optional. Used to perform consistent read-modify-write updates. If not set, a blind "overwrite" update happens.""",
    )
    name: Optional[str] = Field(
        default=None,
        description="""Identifier. The resource name of the ReasoningEngine. Format: `projects/{project}/locations/{location}/reasoningEngines/{reasoning_engine}`""",
    )
    spec: Optional[ReasoningEngineSpec] = Field(
        default=None,
        description="""Optional. Configurations of the ReasoningEngine""",
    )
    update_time: Optional[datetime.datetime] = Field(
        default=None,
        description="""Output only. Timestamp when this ReasoningEngine was most recently updated.""",
    )


class ReasoningEngineDict(TypedDict, total=False):
    """An agent engine."""

    context_spec: Optional[ReasoningEngineContextSpecDict]
    """Optional. Configuration for how Agent Engine sub-resources should manage context."""

    create_time: Optional[datetime.datetime]
    """Output only. Timestamp when this ReasoningEngine was created."""

    description: Optional[str]
    """Optional. The description of the ReasoningEngine."""

    display_name: Optional[str]
    """Required. The display name of the ReasoningEngine."""

    etag: Optional[str]
    """Optional. Used to perform consistent read-modify-write updates. If not set, a blind "overwrite" update happens."""

    name: Optional[str]
    """Identifier. The resource name of the ReasoningEngine. Format: `projects/{project}/locations/{location}/reasoningEngines/{reasoning_engine}`"""

    spec: Optional[ReasoningEngineSpecDict]
    """Optional. Configurations of the ReasoningEngine"""

    update_time: Optional[datetime.datetime]
    """Output only. Timestamp when this ReasoningEngine was most recently updated."""


ReasoningEngineOrDict = Union[ReasoningEngine, ReasoningEngineDict]


class AgentEngineOperation(_common.BaseModel):
    """Operation that has an agent engine as a response."""

    name: Optional[str] = Field(
        default=None,
        description="""The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.""",
    )
    metadata: Optional[dict[str, Any]] = Field(
        default=None,
        description="""Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata.  Any method that returns a long-running operation should document the metadata type, if any.""",
    )
    done: Optional[bool] = Field(
        default=None,
        description="""If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.""",
    )
    error: Optional[dict[str, Any]] = Field(
        default=None,
        description="""The error result of the operation in case of failure or cancellation.""",
    )
    response: Optional[ReasoningEngine] = Field(
        default=None, description="""The created Agent Engine."""
    )


class AgentEngineOperationDict(TypedDict, total=False):
    """Operation that has an agent engine as a response."""

    name: Optional[str]
    """The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`."""

    metadata: Optional[dict[str, Any]]
    """Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata.  Any method that returns a long-running operation should document the metadata type, if any."""

    done: Optional[bool]
    """If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available."""

    error: Optional[dict[str, Any]]
    """The error result of the operation in case of failure or cancellation."""

    response: Optional[ReasoningEngineDict]
    """The created Agent Engine."""


AgentEngineOperationOrDict = Union[AgentEngineOperation, AgentEngineOperationDict]


class AgentEngineMemoryConfig(_common.BaseModel):
    """Config for creating a Memory."""

    http_options: Optional[HttpOptions] = Field(
        default=None, description="""Used to override HTTP request options."""
    )
    display_name: Optional[str] = Field(
        default=None, description="""The display name of the memory."""
    )
    description: Optional[str] = Field(
        default=None, description="""The description of the memory."""
    )
    wait_for_completion: Optional[bool] = Field(
        default=True,
        description="""Waits for the operation to complete before returning.""",
    )


class AgentEngineMemoryConfigDict(TypedDict, total=False):
    """Config for creating a Memory."""

    http_options: Optional[HttpOptionsDict]
    """Used to override HTTP request options."""

    display_name: Optional[str]
    """The display name of the memory."""

    description: Optional[str]
    """The description of the memory."""

    wait_for_completion: Optional[bool]
    """Waits for the operation to complete before returning."""


AgentEngineMemoryConfigOrDict = Union[
    AgentEngineMemoryConfig, AgentEngineMemoryConfigDict
]


class _CreateAgentEngineMemoryRequestParameters(_common.BaseModel):
    """Parameters for creating Agent Engine Memories."""

    name: Optional[str] = Field(
        default=None,
        description="""Name of the agent engine to create the memory under.""",
    )
    fact: Optional[str] = Field(
        default=None,
        description="""The fact of the memory.

      This is the semantic knowledge extracted from the source content).""",
    )
    scope: Optional[dict[str, str]] = Field(
        default=None,
        description="""The scope of the memory.

      Memories are isolated within their scope. The scope is defined when
      creating or generating memories. Up to 5 key-value pairs are accepted,
      andscope values cannot contain the wildcard character '*'.""",
    )
    config: Optional[AgentEngineMemoryConfig] = Field(default=None, description="""""")


class _CreateAgentEngineMemoryRequestParametersDict(TypedDict, total=False):
    """Parameters for creating Agent Engine Memories."""

    name: Optional[str]
    """Name of the agent engine to create the memory under."""

    fact: Optional[str]
    """The fact of the memory.

      This is the semantic knowledge extracted from the source content)."""

    scope: Optional[dict[str, str]]
    """The scope of the memory.

      Memories are isolated within their scope. The scope is defined when
      creating or generating memories. Up to 5 key-value pairs are accepted,
      andscope values cannot contain the wildcard character '*'."""

    config: Optional[AgentEngineMemoryConfigDict]
    """"""


_CreateAgentEngineMemoryRequestParametersOrDict = Union[
    _CreateAgentEngineMemoryRequestParameters,
    _CreateAgentEngineMemoryRequestParametersDict,
]


class Memory(_common.BaseModel):
    """A memory."""

    create_time: Optional[datetime.datetime] = Field(
        default=None,
        description="""Output only. Timestamp when this Memory was created.""",
    )
    description: Optional[str] = Field(
        default=None, description="""Optional. Description of the Memory."""
    )
    display_name: Optional[str] = Field(
        default=None, description="""Optional. Display name of the Memory."""
    )
    fact: Optional[str] = Field(
        default=None,
        description="""Required. Semantic knowledge extracted from the source content.""",
    )
    name: Optional[str] = Field(
        default=None,
        description="""Identifier. The resource name of the Memory. Format: `projects/{project}/locations/{location}/reasoningEngines/{reasoning_engine}/memories/{memory}`""",
    )
    scope: Optional[dict[str, str]] = Field(
        default=None,
        description="""Required. Immutable. The scope of the Memory. Memories are isolated within their scope. The scope is defined when creating or generating memories. Scope values cannot contain the wildcard character '*'.""",
    )
    update_time: Optional[datetime.datetime] = Field(
        default=None,
        description="""Output only. Timestamp when this Memory was most recently updated.""",
    )


class MemoryDict(TypedDict, total=False):
    """A memory."""

    create_time: Optional[datetime.datetime]
    """Output only. Timestamp when this Memory was created."""

    description: Optional[str]
    """Optional. Description of the Memory."""

    display_name: Optional[str]
    """Optional. Display name of the Memory."""

    fact: Optional[str]
    """Required. Semantic knowledge extracted from the source content."""

    name: Optional[str]
    """Identifier. The resource name of the Memory. Format: `projects/{project}/locations/{location}/reasoningEngines/{reasoning_engine}/memories/{memory}`"""

    scope: Optional[dict[str, str]]
    """Required. Immutable. The scope of the Memory. Memories are isolated within their scope. The scope is defined when creating or generating memories. Scope values cannot contain the wildcard character '*'."""

    update_time: Optional[datetime.datetime]
    """Output only. Timestamp when this Memory was most recently updated."""


MemoryOrDict = Union[Memory, MemoryDict]


class AgentEngineMemoryOperation(_common.BaseModel):
    """Operation that has an agent engine memory as a response."""

    name: Optional[str] = Field(
        default=None,
        description="""The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.""",
    )
    metadata: Optional[dict[str, Any]] = Field(
        default=None,
        description="""Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata.  Any method that returns a long-running operation should document the metadata type, if any.""",
    )
    done: Optional[bool] = Field(
        default=None,
        description="""If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.""",
    )
    error: Optional[dict[str, Any]] = Field(
        default=None,
        description="""The error result of the operation in case of failure or cancellation.""",
    )
    response: Optional[Memory] = Field(
        default=None, description="""The Agent Engine Memory."""
    )


class AgentEngineMemoryOperationDict(TypedDict, total=False):
    """Operation that has an agent engine memory as a response."""

    name: Optional[str]
    """The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`."""

    metadata: Optional[dict[str, Any]]
    """Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata.  Any method that returns a long-running operation should document the metadata type, if any."""

    done: Optional[bool]
    """If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available."""

    error: Optional[dict[str, Any]]
    """The error result of the operation in case of failure or cancellation."""

    response: Optional[MemoryDict]
    """The Agent Engine Memory."""


AgentEngineMemoryOperationOrDict = Union[
    AgentEngineMemoryOperation, AgentEngineMemoryOperationDict
]


class DeleteAgentEngineConfig(_common.BaseModel):
    """Config for deleting agent engine."""

    http_options: Optional[HttpOptions] = Field(
        default=None, description="""Used to override HTTP request options."""
    )


class DeleteAgentEngineConfigDict(TypedDict, total=False):
    """Config for deleting agent engine."""

    http_options: Optional[HttpOptionsDict]
    """Used to override HTTP request options."""


DeleteAgentEngineConfigOrDict = Union[
    DeleteAgentEngineConfig, DeleteAgentEngineConfigDict
]


class _DeleteAgentEngineRequestParameters(_common.BaseModel):
    """Parameters for deleting agent engines."""

    name: Optional[str] = Field(
        default=None, description="""Name of the agent engine."""
    )
    force: Optional[bool] = Field(
        default=False,
        description="""If set to true, any child resources will also be deleted.""",
    )
    config: Optional[DeleteAgentEngineConfig] = Field(default=None, description="""""")


class _DeleteAgentEngineRequestParametersDict(TypedDict, total=False):
    """Parameters for deleting agent engines."""

    name: Optional[str]
    """Name of the agent engine."""

    force: Optional[bool]
    """If set to true, any child resources will also be deleted."""

    config: Optional[DeleteAgentEngineConfigDict]
    """"""


_DeleteAgentEngineRequestParametersOrDict = Union[
    _DeleteAgentEngineRequestParameters, _DeleteAgentEngineRequestParametersDict
]


class DeleteAgentEngineOperation(_common.BaseModel):
    """Operation for deleting agent engines."""

    name: Optional[str] = Field(
        default=None,
        description="""The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.""",
    )
    metadata: Optional[dict[str, Any]] = Field(
        default=None,
        description="""Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata.  Any method that returns a long-running operation should document the metadata type, if any.""",
    )
    done: Optional[bool] = Field(
        default=None,
        description="""If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.""",
    )
    error: Optional[dict[str, Any]] = Field(
        default=None,
        description="""The error result of the operation in case of failure or cancellation.""",
    )


class DeleteAgentEngineOperationDict(TypedDict, total=False):
    """Operation for deleting agent engines."""

    name: Optional[str]
    """The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`."""

    metadata: Optional[dict[str, Any]]
    """Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata.  Any method that returns a long-running operation should document the metadata type, if any."""

    done: Optional[bool]
    """If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available."""

    error: Optional[dict[str, Any]]
    """The error result of the operation in case of failure or cancellation."""


DeleteAgentEngineOperationOrDict = Union[
    DeleteAgentEngineOperation, DeleteAgentEngineOperationDict
]


class DeleteAgentEngineMemoryConfig(_common.BaseModel):
    """Config for deleting an Agent Engine Memory."""

    http_options: Optional[HttpOptions] = Field(
        default=None, description="""Used to override HTTP request options."""
    )


class DeleteAgentEngineMemoryConfigDict(TypedDict, total=False):
    """Config for deleting an Agent Engine Memory."""

    http_options: Optional[HttpOptionsDict]
    """Used to override HTTP request options."""


DeleteAgentEngineMemoryConfigOrDict = Union[
    DeleteAgentEngineMemoryConfig, DeleteAgentEngineMemoryConfigDict
]


class _DeleteAgentEngineMemoryRequestParameters(_common.BaseModel):
    """Parameters for deleting agent engines."""

    name: Optional[str] = Field(
        default=None,
        description="""Name of the agent engine memory to delete.""",
    )
    config: Optional[DeleteAgentEngineMemoryConfig] = Field(
        default=None, description=""""""
    )


class _DeleteAgentEngineMemoryRequestParametersDict(TypedDict, total=False):
    """Parameters for deleting agent engines."""

    name: Optional[str]
    """Name of the agent engine memory to delete."""

    config: Optional[DeleteAgentEngineMemoryConfigDict]
    """"""


_DeleteAgentEngineMemoryRequestParametersOrDict = Union[
    _DeleteAgentEngineMemoryRequestParameters,
    _DeleteAgentEngineMemoryRequestParametersDict,
]


class DeleteAgentEngineMemoryOperation(_common.BaseModel):
    """Operation for deleting agent engines."""

    name: Optional[str] = Field(
        default=None,
        description="""The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.""",
    )
    metadata: Optional[dict[str, Any]] = Field(
        default=None,
        description="""Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata.  Any method that returns a long-running operation should document the metadata type, if any.""",
    )
    done: Optional[bool] = Field(
        default=None,
        description="""If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.""",
    )
    error: Optional[dict[str, Any]] = Field(
        default=None,
        description="""The error result of the operation in case of failure or cancellation.""",
    )


class DeleteAgentEngineMemoryOperationDict(TypedDict, total=False):
    """Operation for deleting agent engines."""

    name: Optional[str]
    """The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`."""

    metadata: Optional[dict[str, Any]]
    """Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata.  Any method that returns a long-running operation should document the metadata type, if any."""

    done: Optional[bool]
    """If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available."""

    error: Optional[dict[str, Any]]
    """The error result of the operation in case of failure or cancellation."""


DeleteAgentEngineMemoryOperationOrDict = Union[
    DeleteAgentEngineMemoryOperation, DeleteAgentEngineMemoryOperationDict
]


class GenerateMemoriesRequestVertexSessionSource(_common.BaseModel):
    """The vertex session source for generating memories."""

    end_time: Optional[datetime.datetime] = Field(
        default=None,
        description="""Optional. End time (exclusive) of the time range. If not set, the end time is unbounded.""",
    )
    session: Optional[str] = Field(
        default=None,
        description="""Required. The resource name of the Session to generate memories for. Format: `projects/{project}/locations/{location}/reasoningEngines/{reasoning_engine}/sessions/{session}`""",
    )
    start_time: Optional[datetime.datetime] = Field(
        default=None,
        description="""Optional. Time range to define which session events should be used to generate memories. Start time (inclusive) of the time range. If not set, the start time is unbounded.""",
    )


class GenerateMemoriesRequestVertexSessionSourceDict(TypedDict, total=False):
    """The vertex session source for generating memories."""

    end_time: Optional[datetime.datetime]
    """Optional. End time (exclusive) of the time range. If not set, the end time is unbounded."""

    session: Optional[str]
    """Required. The resource name of the Session to generate memories for. Format: `projects/{project}/locations/{location}/reasoningEngines/{reasoning_engine}/sessions/{session}`"""

    start_time: Optional[datetime.datetime]
    """Optional. Time range to define which session events should be used to generate memories. Start time (inclusive) of the time range. If not set, the start time is unbounded."""


GenerateMemoriesRequestVertexSessionSourceOrDict = Union[
    GenerateMemoriesRequestVertexSessionSource,
    GenerateMemoriesRequestVertexSessionSourceDict,
]


class CodeExecutionResult(_common.BaseModel):
    """Result of executing the [ExecutableCode].

    Only generated when using the [CodeExecution] tool, and always follows a
    `part` containing the [ExecutableCode].
    """

    outcome: Optional[Outcome] = Field(
        default=None, description="""Required. Outcome of the code execution."""
    )
    output: Optional[str] = Field(
        default=None,
        description="""Optional. Contains stdout when code execution is successful, stderr or other description otherwise.""",
    )


class CodeExecutionResultDict(TypedDict, total=False):
    """Result of executing the [ExecutableCode].

    Only generated when using the [CodeExecution] tool, and always follows a
    `part` containing the [ExecutableCode].
    """

    outcome: Optional[Outcome]
    """Required. Outcome of the code execution."""

    output: Optional[str]
    """Optional. Contains stdout when code execution is successful, stderr or other description otherwise."""


CodeExecutionResultOrDict = Union[CodeExecutionResult, CodeExecutionResultDict]


class ExecutableCode(_common.BaseModel):
    """Code generated by the model that is meant to be executed, and the result returned to the model.

    Generated when using the [CodeExecution] tool, in which the code will be
    automatically executed, and a corresponding [CodeExecutionResult] will also
    be generated.
    """

    code: Optional[str] = Field(
        default=None, description="""Required. The code to be executed."""
    )
    language: Optional[Language] = Field(
        default=None,
        description="""Required. Programming language of the `code`.""",
    )


class ExecutableCodeDict(TypedDict, total=False):
    """Code generated by the model that is meant to be executed, and the result returned to the model.

    Generated when using the [CodeExecution] tool, in which the code will be
    automatically executed, and a corresponding [CodeExecutionResult] will also
    be generated.
    """

    code: Optional[str]
    """Required. The code to be executed."""

    language: Optional[Language]
    """Required. Programming language of the `code`."""


ExecutableCodeOrDict = Union[ExecutableCode, ExecutableCodeDict]


class FileData(_common.BaseModel):
    """URI based data."""

    display_name: Optional[str] = Field(
        default=None,
        description="""Optional. Display name of the file data. Used to provide a label or filename to distinguish file datas. This field is only returned in PromptMessage for prompt management. It is currently used in the Gemini GenerateContent calls only when server side tools (code_execution, google_search, and url_context) are enabled.""",
    )
    file_uri: Optional[str] = Field(default=None, description="""Required. URI.""")
    mime_type: Optional[str] = Field(
        default=None,
        description="""Required. The IANA standard MIME type of the source data.""",
    )


class FileDataDict(TypedDict, total=False):
    """URI based data."""

    display_name: Optional[str]
    """Optional. Display name of the file data. Used to provide a label or filename to distinguish file datas. This field is only returned in PromptMessage for prompt management. It is currently used in the Gemini GenerateContent calls only when server side tools (code_execution, google_search, and url_context) are enabled."""

    file_uri: Optional[str]
    """Required. URI."""

    mime_type: Optional[str]
    """Required. The IANA standard MIME type of the source data."""


FileDataOrDict = Union[FileData, FileDataDict]


class FunctionCall(_common.BaseModel):
    """A predicted [FunctionCall] returned from the model that contains a string representing the [FunctionDeclaration.name] and a structured JSON object containing the parameters and their values."""

    args: Optional[dict[str, Any]] = Field(
        default=None,
        description="""Optional. The function parameters and values in JSON object format. See [FunctionDeclaration.parameters] for parameter details.""",
    )
    id: Optional[str] = Field(
        default=None,
        description="""Optional. The unique id of the function call. If populated, the client to execute the `function_call` and return the response with the matching `id`.""",
    )
    name: Optional[str] = Field(
        default=None,
        description="""Required. The name of the function to call. Matches [FunctionDeclaration.name].""",
    )


class FunctionCallDict(TypedDict, total=False):
    """A predicted [FunctionCall] returned from the model that contains a string representing the [FunctionDeclaration.name] and a structured JSON object containing the parameters and their values."""

    args: Optional[dict[str, Any]]
    """Optional. The function parameters and values in JSON object format. See [FunctionDeclaration.parameters] for parameter details."""

    id: Optional[str]
    """Optional. The unique id of the function call. If populated, the client to execute the `function_call` and return the response with the matching `id`."""

    name: Optional[str]
    """Required. The name of the function to call. Matches [FunctionDeclaration.name]."""


FunctionCallOrDict = Union[FunctionCall, FunctionCallDict]


class FunctionResponse(_common.BaseModel):
    """The result output from a [FunctionCall] that contains a string representing the [FunctionDeclaration.name] and a structured JSON object containing any output from the function is used as context to the model.

    This should contain the result of a [FunctionCall] made based on model
    prediction.
    """

    id: Optional[str] = Field(
        default=None,
        description="""Optional. The id of the function call this response is for. Populated by the client to match the corresponding function call `id`.""",
    )
    name: Optional[str] = Field(
        default=None,
        description="""Required. The name of the function to call. Matches [FunctionDeclaration.name] and [FunctionCall.name].""",
    )
    response: Optional[dict[str, Any]] = Field(
        default=None,
        description="""Required. The function response in JSON object format. Use "output" key to specify function output and "error" key to specify error details (if any). If "output" and "error" keys are not specified, then whole "response" is treated as function output.""",
    )


class FunctionResponseDict(TypedDict, total=False):
    """The result output from a [FunctionCall] that contains a string representing the [FunctionDeclaration.name] and a structured JSON object containing any output from the function is used as context to the model.

    This should contain the result of a [FunctionCall] made based on model
    prediction.
    """

    id: Optional[str]
    """Optional. The id of the function call this response is for. Populated by the client to match the corresponding function call `id`."""

    name: Optional[str]
    """Required. The name of the function to call. Matches [FunctionDeclaration.name] and [FunctionCall.name]."""

    response: Optional[dict[str, Any]]
    """Required. The function response in JSON object format. Use "output" key to specify function output and "error" key to specify error details (if any). If "output" and "error" keys are not specified, then whole "response" is treated as function output."""


FunctionResponseOrDict = Union[FunctionResponse, FunctionResponseDict]


class Blob(_common.BaseModel):
    """Content blob."""

    data: Optional[bytes] = Field(default=None, description="""Required. Raw bytes.""")
    display_name: Optional[str] = Field(
        default=None,
        description="""Optional. Display name of the blob. Used to provide a label or filename to distinguish blobs. This field is only returned in PromptMessage for prompt management. It is currently used in the Gemini GenerateContent calls only when server side tools (code_execution, google_search, and url_context) are enabled.""",
    )
    mime_type: Optional[str] = Field(
        default=None,
        description="""Required. The IANA standard MIME type of the source data.""",
    )


class BlobDict(TypedDict, total=False):
    """Content blob."""

    data: Optional[bytes]
    """Required. Raw bytes."""

    display_name: Optional[str]
    """Optional. Display name of the blob. Used to provide a label or filename to distinguish blobs. This field is only returned in PromptMessage for prompt management. It is currently used in the Gemini GenerateContent calls only when server side tools (code_execution, google_search, and url_context) are enabled."""

    mime_type: Optional[str]
    """Required. The IANA standard MIME type of the source data."""


BlobOrDict = Union[Blob, BlobDict]


class VideoMetadata(_common.BaseModel):
    """Metadata describes the input video content."""

    end_offset: Optional[str] = Field(
        default=None, description="""Optional. The end offset of the video."""
    )
    start_offset: Optional[str] = Field(
        default=None, description="""Optional. The start offset of the video."""
    )


class VideoMetadataDict(TypedDict, total=False):
    """Metadata describes the input video content."""

    end_offset: Optional[str]
    """Optional. The end offset of the video."""

    start_offset: Optional[str]
    """Optional. The start offset of the video."""


VideoMetadataOrDict = Union[VideoMetadata, VideoMetadataDict]


class Part(_common.BaseModel):
    """A datatype containing media that is part of a multi-part `Content` message.

    A `Part` consists of data which has an associated datatype. A `Part` can
    only contain one of the accepted types in `Part.data`. A `Part` must have a
    fixed IANA MIME type identifying the type and subtype of the media if
    `inline_data` or `file_data` field is filled with raw bytes.
    """

    code_execution_result: Optional[CodeExecutionResult] = Field(
        default=None,
        description="""Optional. Result of executing the [ExecutableCode].""",
    )
    executable_code: Optional[ExecutableCode] = Field(
        default=None,
        description="""Optional. Code generated by the model that is meant to be executed.""",
    )
    file_data: Optional[FileData] = Field(
        default=None, description="""Optional. URI based data."""
    )
    function_call: Optional[FunctionCall] = Field(
        default=None,
        description="""Optional. A predicted [FunctionCall] returned from the model that contains a string representing the [FunctionDeclaration.name] with the parameters and their values.""",
    )
    function_response: Optional[FunctionResponse] = Field(
        default=None,
        description="""Optional. The result output of a [FunctionCall] that contains a string representing the [FunctionDeclaration.name] and a structured JSON object containing any output from the function call. It is used as context to the model.""",
    )
    inline_data: Optional[Blob] = Field(
        default=None, description="""Optional. Inlined bytes data."""
    )
    text: Optional[str] = Field(
        default=None, description="""Optional. Text part (can be code)."""
    )
    thought: Optional[bool] = Field(
        default=None,
        description="""Optional. Indicates if the part is thought from the model.""",
    )
    thought_signature: Optional[bytes] = Field(
        default=None,
        description="""Optional. An opaque signature for the thought so it can be reused in subsequent requests.""",
    )
    video_metadata: Optional[VideoMetadata] = Field(
        default=None,
        description="""Optional. Video metadata. The metadata should only be specified while the video data is presented in inline_data or file_data.""",
    )


class PartDict(TypedDict, total=False):
    """A datatype containing media that is part of a multi-part `Content` message.

    A `Part` consists of data which has an associated datatype. A `Part` can
    only contain one of the accepted types in `Part.data`. A `Part` must have a
    fixed IANA MIME type identifying the type and subtype of the media if
    `inline_data` or `file_data` field is filled with raw bytes.
    """

    code_execution_result: Optional[CodeExecutionResultDict]
    """Optional. Result of executing the [ExecutableCode]."""

    executable_code: Optional[ExecutableCodeDict]
    """Optional. Code generated by the model that is meant to be executed."""

    file_data: Optional[FileDataDict]
    """Optional. URI based data."""

    function_call: Optional[FunctionCallDict]
    """Optional. A predicted [FunctionCall] returned from the model that contains a string representing the [FunctionDeclaration.name] with the parameters and their values."""

    function_response: Optional[FunctionResponseDict]
    """Optional. The result output of a [FunctionCall] that contains a string representing the [FunctionDeclaration.name] and a structured JSON object containing any output from the function call. It is used as context to the model."""

    inline_data: Optional[BlobDict]
    """Optional. Inlined bytes data."""

    text: Optional[str]
    """Optional. Text part (can be code)."""

    thought: Optional[bool]
    """Optional. Indicates if the part is thought from the model."""

    thought_signature: Optional[bytes]
    """Optional. An opaque signature for the thought so it can be reused in subsequent requests."""

    video_metadata: Optional[VideoMetadataDict]
    """Optional. Video metadata. The metadata should only be specified while the video data is presented in inline_data or file_data."""


PartOrDict = Union[Part, PartDict]


class Content(_common.BaseModel):
    """The base structured datatype containing multi-part content of a message.

    A `Content` includes a `role` field designating the producer of the
    `Content` and a `parts` field containing multi-part data that contains the
    content of the message turn.
    """

    parts: Optional[list[Part]] = Field(
        default=None,
        description="""Required. Ordered `Parts` that constitute a single message. Parts may have different IANA MIME types.""",
    )
    role: Optional[str] = Field(
        default=None,
        description="""Optional. The producer of the content. Must be either 'user' or 'model'. Useful to set for multi-turn conversations, otherwise can be left blank or unset.""",
    )


class ContentDict(TypedDict, total=False):
    """The base structured datatype containing multi-part content of a message.

    A `Content` includes a `role` field designating the producer of the
    `Content` and a `parts` field containing multi-part data that contains the
    content of the message turn.
    """

    parts: Optional[list[PartDict]]
    """Required. Ordered `Parts` that constitute a single message. Parts may have different IANA MIME types."""

    role: Optional[str]
    """Optional. The producer of the content. Must be either 'user' or 'model'. Useful to set for multi-turn conversations, otherwise can be left blank or unset."""


ContentOrDict = Union[Content, ContentDict]


class GenerateMemoriesRequestDirectContentsSourceEvent(_common.BaseModel):
    """A single piece of conversation from which to generate memories."""

    content: Optional[Content] = Field(
        default=None,
        description="""Required. A single piece of content from which to generate memories.""",
    )


class GenerateMemoriesRequestDirectContentsSourceEventDict(TypedDict, total=False):
    """A single piece of conversation from which to generate memories."""

    content: Optional[ContentDict]
    """Required. A single piece of content from which to generate memories."""


GenerateMemoriesRequestDirectContentsSourceEventOrDict = Union[
    GenerateMemoriesRequestDirectContentsSourceEvent,
    GenerateMemoriesRequestDirectContentsSourceEventDict,
]


class GenerateMemoriesRequestDirectContentsSource(_common.BaseModel):
    """The direct contents source for generating memories."""

    events: Optional[list[GenerateMemoriesRequestDirectContentsSourceEvent]] = Field(
        default=None,
        description="""Required. The source content (i.e. chat history) to generate memories from.""",
    )


class GenerateMemoriesRequestDirectContentsSourceDict(TypedDict, total=False):
    """The direct contents source for generating memories."""

    events: Optional[list[GenerateMemoriesRequestDirectContentsSourceEventDict]]
    """Required. The source content (i.e. chat history) to generate memories from."""


GenerateMemoriesRequestDirectContentsSourceOrDict = Union[
    GenerateMemoriesRequestDirectContentsSource,
    GenerateMemoriesRequestDirectContentsSourceDict,
]


class GenerateAgentEngineMemoriesConfig(_common.BaseModel):
    """Config for generating memories."""

    http_options: Optional[HttpOptions] = Field(
        default=None, description="""Used to override HTTP request options."""
    )
    disable_consolidation: Optional[bool] = Field(
        default=None,
        description="""Whether to disable consolidation of memories.

      If true, generated memories will not be consolidated with existing
      memories; all generated memories will be added as new memories regardless
      of whether they are duplicates of or contradictory to existing memories.
      By default, memory consolidation is enabled.""",
    )
    wait_for_completion: Optional[bool] = Field(
        default=True,
        description="""Waits for the operation to complete before returning.""",
    )


class GenerateAgentEngineMemoriesConfigDict(TypedDict, total=False):
    """Config for generating memories."""

    http_options: Optional[HttpOptionsDict]
    """Used to override HTTP request options."""

    disable_consolidation: Optional[bool]
    """Whether to disable consolidation of memories.

      If true, generated memories will not be consolidated with existing
      memories; all generated memories will be added as new memories regardless
      of whether they are duplicates of or contradictory to existing memories.
      By default, memory consolidation is enabled."""

    wait_for_completion: Optional[bool]
    """Waits for the operation to complete before returning."""


GenerateAgentEngineMemoriesConfigOrDict = Union[
    GenerateAgentEngineMemoriesConfig, GenerateAgentEngineMemoriesConfigDict
]


class _GenerateAgentEngineMemoriesRequestParameters(_common.BaseModel):
    """Parameters for generating agent engine memories."""

    name: Optional[str] = Field(
        default=None,
        description="""Name of the agent engine to generate memories for.""",
    )
    vertex_session_source: Optional[GenerateMemoriesRequestVertexSessionSource] = Field(
        default=None,
        description="""The vertex session source of the memories that should be generated.""",
    )
    direct_contents_source: Optional[
        GenerateMemoriesRequestDirectContentsSource
    ] = Field(
        default=None,
        description="""The direct contents source of the memories that should be generated.""",
    )
    scope: Optional[dict[str, str]] = Field(
        default=None,
        description="""The scope of the memories that should be generated.

      Memories will be consolidated across memories with the same scope. Must be
      provided unless the scope is defined in the source content. If `scope` is
      provided, it will override the scope defined in the source content. Scope
      values cannot contain the wildcard character '*'.""",
    )
    config: Optional[GenerateAgentEngineMemoriesConfig] = Field(
        default=None, description=""""""
    )


class _GenerateAgentEngineMemoriesRequestParametersDict(TypedDict, total=False):
    """Parameters for generating agent engine memories."""

    name: Optional[str]
    """Name of the agent engine to generate memories for."""

    vertex_session_source: Optional[GenerateMemoriesRequestVertexSessionSourceDict]
    """The vertex session source of the memories that should be generated."""

    direct_contents_source: Optional[GenerateMemoriesRequestDirectContentsSourceDict]
    """The direct contents source of the memories that should be generated."""

    scope: Optional[dict[str, str]]
    """The scope of the memories that should be generated.

      Memories will be consolidated across memories with the same scope. Must be
      provided unless the scope is defined in the source content. If `scope` is
      provided, it will override the scope defined in the source content. Scope
      values cannot contain the wildcard character '*'."""

    config: Optional[GenerateAgentEngineMemoriesConfigDict]
    """"""


_GenerateAgentEngineMemoriesRequestParametersOrDict = Union[
    _GenerateAgentEngineMemoriesRequestParameters,
    _GenerateAgentEngineMemoriesRequestParametersDict,
]


class GenerateMemoriesResponseGeneratedMemory(_common.BaseModel):
    """A memmory that was generated."""

    memory: Optional[Memory] = Field(
        default=None, description="""The generated memory."""
    )
    action: Optional[GenerateMemoriesResponseGeneratedMemoryAction] = Field(
        default=None, description="""The action to take."""
    )


class GenerateMemoriesResponseGeneratedMemoryDict(TypedDict, total=False):
    """A memmory that was generated."""

    memory: Optional[MemoryDict]
    """The generated memory."""

    action: Optional[GenerateMemoriesResponseGeneratedMemoryAction]
    """The action to take."""


GenerateMemoriesResponseGeneratedMemoryOrDict = Union[
    GenerateMemoriesResponseGeneratedMemory,
    GenerateMemoriesResponseGeneratedMemoryDict,
]


class GenerateMemoriesResponse(_common.BaseModel):
    """The response for generating memories."""

    generated_memories: Optional[list[GenerateMemoriesResponseGeneratedMemory]] = Field(
        default=None, description="""The generated memories."""
    )


class GenerateMemoriesResponseDict(TypedDict, total=False):
    """The response for generating memories."""

    generated_memories: Optional[list[GenerateMemoriesResponseGeneratedMemoryDict]]
    """The generated memories."""


GenerateMemoriesResponseOrDict = Union[
    GenerateMemoriesResponse, GenerateMemoriesResponseDict
]


class AgentEngineGenerateMemoriesOperation(_common.BaseModel):
    """Operation that generates memories for an agent engine."""

    name: Optional[str] = Field(
        default=None,
        description="""The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.""",
    )
    metadata: Optional[dict[str, Any]] = Field(
        default=None,
        description="""Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata.  Any method that returns a long-running operation should document the metadata type, if any.""",
    )
    done: Optional[bool] = Field(
        default=None,
        description="""If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.""",
    )
    error: Optional[dict[str, Any]] = Field(
        default=None,
        description="""The error result of the operation in case of failure or cancellation.""",
    )
    response: Optional[GenerateMemoriesResponse] = Field(
        default=None, description="""The response for generating memories."""
    )


class AgentEngineGenerateMemoriesOperationDict(TypedDict, total=False):
    """Operation that generates memories for an agent engine."""

    name: Optional[str]
    """The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`."""

    metadata: Optional[dict[str, Any]]
    """Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata.  Any method that returns a long-running operation should document the metadata type, if any."""

    done: Optional[bool]
    """If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available."""

    error: Optional[dict[str, Any]]
    """The error result of the operation in case of failure or cancellation."""

    response: Optional[GenerateMemoriesResponseDict]
    """The response for generating memories."""


AgentEngineGenerateMemoriesOperationOrDict = Union[
    AgentEngineGenerateMemoriesOperation,
    AgentEngineGenerateMemoriesOperationDict,
]


class GetAgentEngineConfig(_common.BaseModel):
    """Config for create agent engine."""

    http_options: Optional[HttpOptions] = Field(
        default=None, description="""Used to override HTTP request options."""
    )


class GetAgentEngineConfigDict(TypedDict, total=False):
    """Config for create agent engine."""

    http_options: Optional[HttpOptionsDict]
    """Used to override HTTP request options."""


GetAgentEngineConfigOrDict = Union[GetAgentEngineConfig, GetAgentEngineConfigDict]


class _GetAgentEngineRequestParameters(_common.BaseModel):
    """Parameters for getting agent engines."""

    name: Optional[str] = Field(
        default=None, description="""Name of the agent engine."""
    )
    config: Optional[GetAgentEngineConfig] = Field(default=None, description="""""")


class _GetAgentEngineRequestParametersDict(TypedDict, total=False):
    """Parameters for getting agent engines."""

    name: Optional[str]
    """Name of the agent engine."""

    config: Optional[GetAgentEngineConfigDict]
    """"""


_GetAgentEngineRequestParametersOrDict = Union[
    _GetAgentEngineRequestParameters, _GetAgentEngineRequestParametersDict
]


class GetAgentEngineMemoryConfig(_common.BaseModel):
    """Config for getting an Agent Engine Memory."""

    http_options: Optional[HttpOptions] = Field(
        default=None, description="""Used to override HTTP request options."""
    )


class GetAgentEngineMemoryConfigDict(TypedDict, total=False):
    """Config for getting an Agent Engine Memory."""

    http_options: Optional[HttpOptionsDict]
    """Used to override HTTP request options."""


GetAgentEngineMemoryConfigOrDict = Union[
    GetAgentEngineMemoryConfig, GetAgentEngineMemoryConfigDict
]


class _GetAgentEngineMemoryRequestParameters(_common.BaseModel):
    """Parameters for getting an agent engine."""

    name: Optional[str] = Field(
        default=None, description="""Name of the agent engine."""
    )
    config: Optional[GetAgentEngineMemoryConfig] = Field(
        default=None, description=""""""
    )


class _GetAgentEngineMemoryRequestParametersDict(TypedDict, total=False):
    """Parameters for getting an agent engine."""

    name: Optional[str]
    """Name of the agent engine."""

    config: Optional[GetAgentEngineMemoryConfigDict]
    """"""


_GetAgentEngineMemoryRequestParametersOrDict = Union[
    _GetAgentEngineMemoryRequestParameters,
    _GetAgentEngineMemoryRequestParametersDict,
]


class ListAgentEngineConfig(_common.BaseModel):
    """Config for listing agent engines."""

    http_options: Optional[HttpOptions] = Field(
        default=None, description="""Used to override HTTP request options."""
    )
    page_size: Optional[int] = Field(default=None, description="""""")
    page_token: Optional[str] = Field(default=None, description="""""")
    filter: Optional[str] = Field(
        default=None,
        description="""An expression for filtering the results of the request.
      For field names both snake_case and camelCase are supported.""",
    )


class ListAgentEngineConfigDict(TypedDict, total=False):
    """Config for listing agent engines."""

    http_options: Optional[HttpOptionsDict]
    """Used to override HTTP request options."""

    page_size: Optional[int]
    """"""

    page_token: Optional[str]
    """"""

    filter: Optional[str]
    """An expression for filtering the results of the request.
      For field names both snake_case and camelCase are supported."""


ListAgentEngineConfigOrDict = Union[ListAgentEngineConfig, ListAgentEngineConfigDict]


class _ListAgentEngineRequestParameters(_common.BaseModel):
    """Parameters for listing agent engines."""

    config: Optional[ListAgentEngineConfig] = Field(default=None, description="""""")


class _ListAgentEngineRequestParametersDict(TypedDict, total=False):
    """Parameters for listing agent engines."""

    config: Optional[ListAgentEngineConfigDict]
    """"""


_ListAgentEngineRequestParametersOrDict = Union[
    _ListAgentEngineRequestParameters, _ListAgentEngineRequestParametersDict
]


class ListReasoningEnginesResponse(_common.BaseModel):
    """Response for listing agent engines."""

    next_page_token: Optional[str] = Field(default=None, description="""""")
    reasoning_engines: Optional[list[ReasoningEngine]] = Field(
        default=None,
        description="""List of agent engines.
      """,
    )


class ListReasoningEnginesResponseDict(TypedDict, total=False):
    """Response for listing agent engines."""

    next_page_token: Optional[str]
    """"""

    reasoning_engines: Optional[list[ReasoningEngineDict]]
    """List of agent engines.
      """


ListReasoningEnginesResponseOrDict = Union[
    ListReasoningEnginesResponse, ListReasoningEnginesResponseDict
]


class ListAgentEngineMemoryConfig(_common.BaseModel):
    """Config for listing agent engine memories."""

    http_options: Optional[HttpOptions] = Field(
        default=None, description="""Used to override HTTP request options."""
    )
    page_size: Optional[int] = Field(default=None, description="""""")
    page_token: Optional[str] = Field(default=None, description="""""")
    filter: Optional[str] = Field(
        default=None,
        description="""An expression for filtering the results of the request.
      For field names both snake_case and camelCase are supported.""",
    )


class ListAgentEngineMemoryConfigDict(TypedDict, total=False):
    """Config for listing agent engine memories."""

    http_options: Optional[HttpOptionsDict]
    """Used to override HTTP request options."""

    page_size: Optional[int]
    """"""

    page_token: Optional[str]
    """"""

    filter: Optional[str]
    """An expression for filtering the results of the request.
      For field names both snake_case and camelCase are supported."""


ListAgentEngineMemoryConfigOrDict = Union[
    ListAgentEngineMemoryConfig, ListAgentEngineMemoryConfigDict
]


class _ListAgentEngineMemoryRequestParameters(_common.BaseModel):
    """Parameters for listing agent engines."""

    name: Optional[str] = Field(
        default=None, description="""Name of the agent engine."""
    )
    config: Optional[ListAgentEngineMemoryConfig] = Field(
        default=None, description=""""""
    )


class _ListAgentEngineMemoryRequestParametersDict(TypedDict, total=False):
    """Parameters for listing agent engines."""

    name: Optional[str]
    """Name of the agent engine."""

    config: Optional[ListAgentEngineMemoryConfigDict]
    """"""


_ListAgentEngineMemoryRequestParametersOrDict = Union[
    _ListAgentEngineMemoryRequestParameters,
    _ListAgentEngineMemoryRequestParametersDict,
]


class ListReasoningEnginesMemoriesResponse(_common.BaseModel):
    """Response for listing agent engine memories."""

    next_page_token: Optional[str] = Field(default=None, description="""""")
    memories: Optional[list[Memory]] = Field(
        default=None, description="""List of agent engine memories."""
    )


class ListReasoningEnginesMemoriesResponseDict(TypedDict, total=False):
    """Response for listing agent engine memories."""

    next_page_token: Optional[str]
    """"""

    memories: Optional[list[MemoryDict]]
    """List of agent engine memories."""


ListReasoningEnginesMemoriesResponseOrDict = Union[
    ListReasoningEnginesMemoriesResponse,
    ListReasoningEnginesMemoriesResponseDict,
]


class GetAgentEngineOperationConfig(_common.BaseModel):

    http_options: Optional[HttpOptions] = Field(
        default=None, description="""Used to override HTTP request options."""
    )


class GetAgentEngineOperationConfigDict(TypedDict, total=False):

    http_options: Optional[HttpOptionsDict]
    """Used to override HTTP request options."""


GetAgentEngineOperationConfigOrDict = Union[
    GetAgentEngineOperationConfig, GetAgentEngineOperationConfigDict
]


class _GetAgentEngineOperationParameters(_common.BaseModel):
    """Parameters for getting an operation with an agent engine as a response."""

    operation_name: Optional[str] = Field(
        default=None,
        description="""The server-assigned name for the operation.""",
    )
    config: Optional[GetAgentEngineOperationConfig] = Field(
        default=None,
        description="""Used to override the default configuration.""",
    )


class _GetAgentEngineOperationParametersDict(TypedDict, total=False):
    """Parameters for getting an operation with an agent engine as a response."""

    operation_name: Optional[str]
    """The server-assigned name for the operation."""

    config: Optional[GetAgentEngineOperationConfigDict]
    """Used to override the default configuration."""


_GetAgentEngineOperationParametersOrDict = Union[
    _GetAgentEngineOperationParameters, _GetAgentEngineOperationParametersDict
]


class _GetAgentEngineMemoryOperationParameters(_common.BaseModel):
    """Parameters for getting an operation with a memory as a response."""

    operation_name: Optional[str] = Field(
        default=None,
        description="""The server-assigned name for the operation.""",
    )
    config: Optional[GetAgentEngineOperationConfig] = Field(
        default=None,
        description="""Used to override the default configuration.""",
    )


class _GetAgentEngineMemoryOperationParametersDict(TypedDict, total=False):
    """Parameters for getting an operation with a memory as a response."""

    operation_name: Optional[str]
    """The server-assigned name for the operation."""

    config: Optional[GetAgentEngineOperationConfigDict]
    """Used to override the default configuration."""


_GetAgentEngineMemoryOperationParametersOrDict = Union[
    _GetAgentEngineMemoryOperationParameters,
    _GetAgentEngineMemoryOperationParametersDict,
]


class _GetAgentEngineGenerateMemoriesOperationParameters(_common.BaseModel):
    """Parameters for getting an operation with generated memories as a response."""

    operation_name: Optional[str] = Field(
        default=None,
        description="""The server-assigned name for the operation.""",
    )
    config: Optional[GetAgentEngineOperationConfig] = Field(
        default=None,
        description="""Used to override the default configuration.""",
    )


class _GetAgentEngineGenerateMemoriesOperationParametersDict(TypedDict, total=False):
    """Parameters for getting an operation with generated memories as a response."""

    operation_name: Optional[str]
    """The server-assigned name for the operation."""

    config: Optional[GetAgentEngineOperationConfigDict]
    """Used to override the default configuration."""


_GetAgentEngineGenerateMemoriesOperationParametersOrDict = Union[
    _GetAgentEngineGenerateMemoriesOperationParameters,
    _GetAgentEngineGenerateMemoriesOperationParametersDict,
]


class QueryAgentEngineConfig(_common.BaseModel):
    """Config for querying agent engines."""

    http_options: Optional[HttpOptions] = Field(
        default=None, description="""Used to override HTTP request options."""
    )
    class_method: Optional[str] = Field(
        default=None, description="""The class method to call."""
    )
    input: Optional[dict[str, Any]] = Field(
        default=None, description="""The input to the class method."""
    )
    include_all_fields: Optional[bool] = Field(default=False, description="""""")


class QueryAgentEngineConfigDict(TypedDict, total=False):
    """Config for querying agent engines."""

    http_options: Optional[HttpOptionsDict]
    """Used to override HTTP request options."""

    class_method: Optional[str]
    """The class method to call."""

    input: Optional[dict[str, Any]]
    """The input to the class method."""

    include_all_fields: Optional[bool]
    """"""


QueryAgentEngineConfigOrDict = Union[QueryAgentEngineConfig, QueryAgentEngineConfigDict]


class _QueryAgentEngineRequestParameters(_common.BaseModel):
    """Parameters for querying agent engines."""

    name: Optional[str] = Field(
        default=None, description="""Name of the agent engine."""
    )
    config: Optional[QueryAgentEngineConfig] = Field(default=None, description="""""")


class _QueryAgentEngineRequestParametersDict(TypedDict, total=False):
    """Parameters for querying agent engines."""

    name: Optional[str]
    """Name of the agent engine."""

    config: Optional[QueryAgentEngineConfigDict]
    """"""


_QueryAgentEngineRequestParametersOrDict = Union[
    _QueryAgentEngineRequestParameters, _QueryAgentEngineRequestParametersDict
]


class QueryReasoningEngineResponse(_common.BaseModel):
    """The response for querying an agent engine."""

    output: Optional[Any] = Field(
        default=None,
        description="""Response provided by users in JSON object format.""",
    )


class QueryReasoningEngineResponseDict(TypedDict, total=False):
    """The response for querying an agent engine."""

    output: Optional[Any]
    """Response provided by users in JSON object format."""


QueryReasoningEngineResponseOrDict = Union[
    QueryReasoningEngineResponse, QueryReasoningEngineResponseDict
]


class RetrieveMemoriesRequestSimilaritySearchParams(_common.BaseModel):
    """The parameters for semantic similarity search based retrieval."""

    search_query: Optional[str] = Field(
        default=None,
        description="""Required. Query to use for similarity search retrieval. If provided, then the parent ReasoningEngine must have ReasoningEngineContextSpec.MemoryBankConfig.SimilaritySearchConfig set.""",
    )
    top_k: Optional[int] = Field(
        default=None,
        description="""Optional. The maximum number of memories to return. The service may return fewer than this value. If unspecified, at most 3 memories will be returned. The maximum value is 100; values above 100 will be coerced to 100.""",
    )


class RetrieveMemoriesRequestSimilaritySearchParamsDict(TypedDict, total=False):
    """The parameters for semantic similarity search based retrieval."""

    search_query: Optional[str]
    """Required. Query to use for similarity search retrieval. If provided, then the parent ReasoningEngine must have ReasoningEngineContextSpec.MemoryBankConfig.SimilaritySearchConfig set."""

    top_k: Optional[int]
    """Optional. The maximum number of memories to return. The service may return fewer than this value. If unspecified, at most 3 memories will be returned. The maximum value is 100; values above 100 will be coerced to 100."""


RetrieveMemoriesRequestSimilaritySearchParamsOrDict = Union[
    RetrieveMemoriesRequestSimilaritySearchParams,
    RetrieveMemoriesRequestSimilaritySearchParamsDict,
]


class RetrieveMemoriesRequestSimpleRetrievalParams(_common.BaseModel):
    """The parameters for simple (non-similarity search) retrieval."""

    page_size: Optional[int] = Field(
        default=None,
        description="""Optional. The maximum number of memories to return. The service may return fewer than this value. If unspecified, at most 3 memories will be returned. The maximum value is 100; values above 100 will be coerced to 100.""",
    )
    page_token: Optional[str] = Field(
        default=None,
        description="""Optional. A page token, received from a previous `RetrieveMemories` call. Provide this to retrieve the subsequent page.""",
    )


class RetrieveMemoriesRequestSimpleRetrievalParamsDict(TypedDict, total=False):
    """The parameters for simple (non-similarity search) retrieval."""

    page_size: Optional[int]
    """Optional. The maximum number of memories to return. The service may return fewer than this value. If unspecified, at most 3 memories will be returned. The maximum value is 100; values above 100 will be coerced to 100."""

    page_token: Optional[str]
    """Optional. A page token, received from a previous `RetrieveMemories` call. Provide this to retrieve the subsequent page."""


RetrieveMemoriesRequestSimpleRetrievalParamsOrDict = Union[
    RetrieveMemoriesRequestSimpleRetrievalParams,
    RetrieveMemoriesRequestSimpleRetrievalParamsDict,
]


class RetrieveAgentEngineMemoriesConfig(_common.BaseModel):
    """Config for retrieving memories."""

    http_options: Optional[HttpOptions] = Field(
        default=None, description="""Used to override HTTP request options."""
    )


class RetrieveAgentEngineMemoriesConfigDict(TypedDict, total=False):
    """Config for retrieving memories."""

    http_options: Optional[HttpOptionsDict]
    """Used to override HTTP request options."""


RetrieveAgentEngineMemoriesConfigOrDict = Union[
    RetrieveAgentEngineMemoriesConfig, RetrieveAgentEngineMemoriesConfigDict
]


class _RetrieveAgentEngineMemoriesRequestParameters(_common.BaseModel):
    """Parameters for retrieving agent engine memories."""

    name: Optional[str] = Field(
        default=None,
        description="""Name of the agent engine to retrieve memories from.""",
    )
    scope: Optional[dict[str, str]] = Field(
        default=None,
        description="""The scope of the memories to retrieve.

      A memory must have exactly the same scope as the scope provided here to be
      retrieved (i.e. same keys and values). Order does not matter, but it is
      case-sensitive.""",
    )
    similarity_search_params: Optional[
        RetrieveMemoriesRequestSimilaritySearchParams
    ] = Field(
        default=None,
        description="""Parameters for semantic similarity search based retrieval.""",
    )
    simple_retrieval_params: Optional[
        RetrieveMemoriesRequestSimpleRetrievalParams
    ] = Field(
        default=None,
        description="""Parameters for simple (non-similarity search) retrieval.""",
    )
    config: Optional[RetrieveAgentEngineMemoriesConfig] = Field(
        default=None, description=""""""
    )


class _RetrieveAgentEngineMemoriesRequestParametersDict(TypedDict, total=False):
    """Parameters for retrieving agent engine memories."""

    name: Optional[str]
    """Name of the agent engine to retrieve memories from."""

    scope: Optional[dict[str, str]]
    """The scope of the memories to retrieve.

      A memory must have exactly the same scope as the scope provided here to be
      retrieved (i.e. same keys and values). Order does not matter, but it is
      case-sensitive."""

    similarity_search_params: Optional[
        RetrieveMemoriesRequestSimilaritySearchParamsDict
    ]
    """Parameters for semantic similarity search based retrieval."""

    simple_retrieval_params: Optional[RetrieveMemoriesRequestSimpleRetrievalParamsDict]
    """Parameters for simple (non-similarity search) retrieval."""

    config: Optional[RetrieveAgentEngineMemoriesConfigDict]
    """"""


_RetrieveAgentEngineMemoriesRequestParametersOrDict = Union[
    _RetrieveAgentEngineMemoriesRequestParameters,
    _RetrieveAgentEngineMemoriesRequestParametersDict,
]


class RetrieveMemoriesResponseRetrievedMemory(_common.BaseModel):
    """A retrieved memory."""

    distance: Optional[float] = Field(
        default=None,
        description="""The distance between the query and the retrieved Memory. Smaller values indicate more similar memories. This is only set if similarity search was used for retrieval.""",
    )
    memory: Optional[Memory] = Field(
        default=None, description="""The retrieved Memory."""
    )


class RetrieveMemoriesResponseRetrievedMemoryDict(TypedDict, total=False):
    """A retrieved memory."""

    distance: Optional[float]
    """The distance between the query and the retrieved Memory. Smaller values indicate more similar memories. This is only set if similarity search was used for retrieval."""

    memory: Optional[MemoryDict]
    """The retrieved Memory."""


RetrieveMemoriesResponseRetrievedMemoryOrDict = Union[
    RetrieveMemoriesResponseRetrievedMemory,
    RetrieveMemoriesResponseRetrievedMemoryDict,
]


class RetrieveMemoriesResponse(_common.BaseModel):
    """The response for retrieving memories."""

    next_page_token: Optional[str] = Field(
        default=None,
        description="""A token that can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages. This token is not set if similarity search was used for retrieval.""",
    )
    retrieved_memories: Optional[list[RetrieveMemoriesResponseRetrievedMemory]] = Field(
        default=None, description="""The retrieved memories."""
    )


class RetrieveMemoriesResponseDict(TypedDict, total=False):
    """The response for retrieving memories."""

    next_page_token: Optional[str]
    """A token that can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages. This token is not set if similarity search was used for retrieval."""

    retrieved_memories: Optional[list[RetrieveMemoriesResponseRetrievedMemoryDict]]
    """The retrieved memories."""


RetrieveMemoriesResponseOrDict = Union[
    RetrieveMemoriesResponse, RetrieveMemoriesResponseDict
]


class UpdateAgentEngineConfig(_common.BaseModel):
    """Config for updating agent engine."""

    http_options: Optional[HttpOptions] = Field(
        default=None, description="""Used to override HTTP request options."""
    )
    display_name: Optional[str] = Field(
        default=None,
        description="""The user-defined name of the Agent Engine.

      The display name can be up to 128 characters long and can comprise any
      UTF-8 characters.
      """,
    )
    description: Optional[str] = Field(
        default=None, description="""The description of the Agent Engine."""
    )
    spec: Optional[ReasoningEngineSpec] = Field(
        default=None,
        description="""Optional. Configurations of the ReasoningEngine.""",
    )
    update_mask: Optional[str] = Field(
        default=None,
        description="""The update mask to apply. For the `FieldMask` definition, see
      https://protobuf.dev/reference/protobuf/google.protobuf/#field-mask.""",
    )


class UpdateAgentEngineConfigDict(TypedDict, total=False):
    """Config for updating agent engine."""

    http_options: Optional[HttpOptionsDict]
    """Used to override HTTP request options."""

    display_name: Optional[str]
    """The user-defined name of the Agent Engine.

      The display name can be up to 128 characters long and can comprise any
      UTF-8 characters.
      """

    description: Optional[str]
    """The description of the Agent Engine."""

    spec: Optional[ReasoningEngineSpecDict]
    """Optional. Configurations of the ReasoningEngine."""

    update_mask: Optional[str]
    """The update mask to apply. For the `FieldMask` definition, see
      https://protobuf.dev/reference/protobuf/google.protobuf/#field-mask."""


UpdateAgentEngineConfigOrDict = Union[
    UpdateAgentEngineConfig, UpdateAgentEngineConfigDict
]


class _UpdateAgentEngineRequestParameters(_common.BaseModel):
    """Parameters for updating agent engines."""

    name: Optional[str] = Field(
        default=None, description="""Name of the agent engine."""
    )
    config: Optional[UpdateAgentEngineConfig] = Field(default=None, description="""""")


class _UpdateAgentEngineRequestParametersDict(TypedDict, total=False):
    """Parameters for updating agent engines."""

    name: Optional[str]
    """Name of the agent engine."""

    config: Optional[UpdateAgentEngineConfigDict]
    """"""


_UpdateAgentEngineRequestParametersOrDict = Union[
    _UpdateAgentEngineRequestParameters, _UpdateAgentEngineRequestParametersDict
]


class UpdateAgentEngineMemoryConfig(_common.BaseModel):
    """Config for updating agent engine memory."""

    http_options: Optional[HttpOptions] = Field(
        default=None, description="""Used to override HTTP request options."""
    )
    display_name: Optional[str] = Field(
        default=None, description="""The display name of the memory."""
    )
    description: Optional[str] = Field(
        default=None, description="""The description of the memory."""
    )
    wait_for_completion: Optional[bool] = Field(
        default=True,
        description="""Waits for the operation to complete before returning.""",
    )
    update_mask: Optional[str] = Field(
        default=None,
        description="""The update mask to apply. For the `FieldMask` definition, see
      https://protobuf.dev/reference/protobuf/google.protobuf/#field-mask.""",
    )


class UpdateAgentEngineMemoryConfigDict(TypedDict, total=False):
    """Config for updating agent engine memory."""

    http_options: Optional[HttpOptionsDict]
    """Used to override HTTP request options."""

    display_name: Optional[str]
    """The display name of the memory."""

    description: Optional[str]
    """The description of the memory."""

    wait_for_completion: Optional[bool]
    """Waits for the operation to complete before returning."""

    update_mask: Optional[str]
    """The update mask to apply. For the `FieldMask` definition, see
      https://protobuf.dev/reference/protobuf/google.protobuf/#field-mask."""


UpdateAgentEngineMemoryConfigOrDict = Union[
    UpdateAgentEngineMemoryConfig, UpdateAgentEngineMemoryConfigDict
]


class _UpdateAgentEngineMemoryRequestParameters(_common.BaseModel):
    """Parameters for updating agent engine memories."""

    name: Optional[str] = Field(
        default=None,
        description="""Name of the agent engine memory to update.""",
    )
    fact: Optional[str] = Field(
        default=None,
        description="""The updated fact of the memory.

      This is the semantic knowledge extracted from the source content.""",
    )
    scope: Optional[dict[str, str]] = Field(
        default=None,
        description="""The updated scope of the memory.

      Memories are isolated within their scope. The scope is defined when
      creating or generating memories. Up to 5 key-value pairs are accepted,
      and scope values cannot contain the wildcard character '*'.""",
    )
    config: Optional[UpdateAgentEngineMemoryConfig] = Field(
        default=None, description=""""""
    )


class _UpdateAgentEngineMemoryRequestParametersDict(TypedDict, total=False):
    """Parameters for updating agent engine memories."""

    name: Optional[str]
    """Name of the agent engine memory to update."""

    fact: Optional[str]
    """The updated fact of the memory.

      This is the semantic knowledge extracted from the source content."""

    scope: Optional[dict[str, str]]
    """The updated scope of the memory.

      Memories are isolated within their scope. The scope is defined when
      creating or generating memories. Up to 5 key-value pairs are accepted,
      and scope values cannot contain the wildcard character '*'."""

    config: Optional[UpdateAgentEngineMemoryConfigDict]
    """"""


_UpdateAgentEngineMemoryRequestParametersOrDict = Union[
    _UpdateAgentEngineMemoryRequestParameters,
    _UpdateAgentEngineMemoryRequestParametersDict,
]


class PromptOptimizerVAPOConfig(_common.BaseModel):
    """VAPO Prompt Optimizer Config."""

    config_path: Optional[str] = Field(
        default=None,
        description="""The gcs path to the config file, e.g. gs://bucket/config.json.""",
    )
    service_account: Optional[str] = Field(
        default=None,
        description="""The service account to use for the custom job. Cannot be provided at the same time as service_account_project_number.""",
    )
    service_account_project_number: Optional[Union[int, str]] = Field(
        default=None,
        description="""The project number used to construct the default service account:{service_account_project_number}-<EMAIL> be provided at the same time as "service_account".""",
    )
    wait_for_completion: Optional[bool] = Field(
        default=True,
        description="""Whether to wait for the job tocomplete. Ignored for async jobs.""",
    )
    optimizer_job_display_name: Optional[str] = Field(
        default=None,
        description="""The display name of the optimization job. If not provided, a display name in the format of "vapo-optimizer-{timestamp}" will be used.""",
    )


class PromptOptimizerVAPOConfigDict(TypedDict, total=False):
    """VAPO Prompt Optimizer Config."""

    config_path: Optional[str]
    """The gcs path to the config file, e.g. gs://bucket/config.json."""

    service_account: Optional[str]
    """The service account to use for the custom job. Cannot be provided at the same time as service_account_project_number."""

    service_account_project_number: Optional[Union[int, str]]
    """The project number used to construct the default service account:{service_account_project_number}-<EMAIL> be provided at the same time as "service_account"."""

    wait_for_completion: Optional[bool]
    """Whether to wait for the job tocomplete. Ignored for async jobs."""

    optimizer_job_display_name: Optional[str]
    """The display name of the optimization job. If not provided, a display name in the format of "vapo-optimizer-{timestamp}" will be used."""


PromptOptimizerVAPOConfigOrDict = Union[
    PromptOptimizerVAPOConfig, PromptOptimizerVAPOConfigDict
]


class PromptTemplate(_common.BaseModel):
    """A prompt template for creating prompts with variables."""

    text: Optional[str] = Field(
        default=None, description="""The prompt template text."""
    )
    _VARIABLE_NAME_REGEX: ClassVar[str] = r"\{([_a-zA-Z][_a-zA-Z0-9]*)\}"

    @field_validator("text")
    @classmethod
    def text_must_not_be_empty(cls, value: str) -> str:
        if not value.strip():
            raise ValueError(
                "Prompt template text cannot be empty or consist only of" " whitespace."
            )
        return value

    @computed_field  # type: ignore[prop-decorator]
    @property
    def variables(self) -> set[str]:
        return set(re.findall(self._VARIABLE_NAME_REGEX, self.text))

    def _split_template_by_variables(self) -> list[Tuple[str, str]]:
        parts = []
        last_end = 0
        for match in re.finditer(self._VARIABLE_NAME_REGEX, self.text):
            start, end = match.span()
            var_name = match.group(1)
            if start > last_end and self.text:
                parts.append(("text", self.text[last_end:start]))
            parts.append(("var", var_name))
            last_end = end
        if last_end < len(self.text) and self.text:
            parts.append(("text", self.text[last_end:]))
        return parts

    def _merge_adjacent_text_parts(
        self, parts: list[genai_types.Part]
    ) -> list[genai_types.Part]:
        if not parts:
            return []

        merged = []
        current_text_buffer = []

        for part in parts:
            is_purely_text = part.text is not None and all(
                getattr(part, field) is None
                for field in part.model_fields
                if field != "text"
            )

            if is_purely_text:
                current_text_buffer.append(part.text)
            else:
                if current_text_buffer:
                    merged.append(genai_types.Part(text="".join(current_text_buffer)))
                    current_text_buffer = []
                merged.append(part)

        if current_text_buffer:
            merged.append(genai_types.Part(text="".join(current_text_buffer)))

        return merged

    def _is_multimodal_json_string(
        self,
        value: Any,
    ) -> bool:
        """Checks if the input value is a multimodal JSON string."""
        if not isinstance(value, str):
            return False
        try:
            data = json.loads(value)
            # Check for the specific structure: {"contents": [{"parts": [...]}]}
            # or {"parts": [...]} if assemble returns a single Content JSON
            if isinstance(data, dict):
                if "contents" in data and isinstance(data["contents"], list):
                    if not data["contents"]:
                        return False
                    first_content = data["contents"][0]
                    if isinstance(first_content, dict) and "parts" in first_content:
                        try:
                            genai_types.Content.model_validate(first_content)
                            return True
                        except ValueError:
                            return False
                # Adding a check if 'data' itself is a Content-like object with parts
                elif "parts" in data and isinstance(data["parts"], list):
                    try:
                        genai_types.Content.model_validate(data)
                        return True
                    except ValueError:
                        return False
            return False
        except json.JSONDecodeError:
            return False

    def _parse_multimodal_json_string_into_parts(
        self,
        value: str,
    ) -> list[genai_types.Part]:
        """Parses a multimodal JSON string and returns its list of Parts."""
        try:
            content = genai_types.Content.model_validate_json(value)
            return content.parts if content.parts is not None else [genai_types.Part()]
        except Exception:
            return [genai_types.Part(text=value)]

    def assemble(self, **kwargs: Any) -> str:
        """Assembles the prompt template with the given keyword arguments.

        Supports both text and multimodal content. The `assemble` method
        substitutes variables from the prompt template text with provided
        values.

        Key Behaviors of `assemble()`:
        1.  Variable Substitution: Replaces all defined variables with their
            corresponding keyword argument values. Raises ValueError if a
            template
            variable is missing a value or if an extraneous kwarg is provided.
        2.  Multimodal Handling:
            - Detects if any variable's value is a JSON string representing
            multimodal content (specifically, `{"contents": [{"parts": [...]}]}`
            or `{"role": "user", "parts": [...]}`).
            - If multimodal content is detected for a variable, its `Part`
            objects
            are extracted and inserted into the assembled sequence.
            - Text segments from the template and simple text variable values
            become `Part(text=...)`.
        3.  Output Format:
            - If ALL substituted variables were simple text AND the assembled
            result (after merging adjacent text parts) consists of a single,
            purely textual `Part`, `assemble()` returns a raw Python string.
            - Otherwise (if any variable was multimodal, or if the assembly
            results in multiple parts or non-textual parts), `assemble()`
            returns
            a JSON string representing a single `google.genai.types.Content`
            object with `role="user"` and the assembled parts.
        4.  Text Part Merging: Consecutively assembled text parts are
            automatically merged into a single text `Part` to create a more
            concise list of parts.

        This dual output format (raw string or JSON string of `Content`) allows
        the downstream inference functions to seamlessly handle both simple text
        prompts and more complex multimodal prompts generated from the same
        templating mechanism.
        """
        current_variables = self.variables
        for var_name_in_kwarg in kwargs:
            if var_name_in_kwarg not in current_variables:
                raise ValueError(
                    f"Invalid variable name '{var_name_in_kwarg}' provided to"
                    " assemble. Valid variables in template are:"
                    f" {current_variables}"
                )
        # Check if all template variables are provided in kwargs
        for tpl_var in current_variables:
            if tpl_var not in kwargs:
                raise ValueError(f"Missing value for template variable '{tpl_var}'.")

        template_segments = self._split_template_by_variables()

        raw_assembled_parts: list[genai_types.Part] = []
        contains_multimodal_variable_type = False

        for segment_type, segment_value in template_segments:
            if segment_type == "text":
                if segment_value:
                    raw_assembled_parts.append(genai_types.Part(text=segment_value))
            elif segment_type == "var":
                var_value = kwargs.get(segment_value)

                str_var_value = str(var_value)

                if self._is_multimodal_json_string(str_var_value):
                    multimodal_parts = self._parse_multimodal_json_string_into_parts(
                        str_var_value
                    )
                    if multimodal_parts:
                        contains_multimodal_variable_type = True
                        raw_assembled_parts.extend(multimodal_parts)
                    else:
                        raw_assembled_parts.append(genai_types.Part(text=str_var_value))
                else:
                    raw_assembled_parts.append(genai_types.Part(text=str_var_value))

        final_assembled_parts = self._merge_adjacent_text_parts(raw_assembled_parts)

        # Condition for returning raw text string:
        # 1. No multimodal variable was *originally* a multimodal JSON string.
        # 2. After merging, there's exactly one part.
        # 3. That single part is purely textual.
        if (
            not contains_multimodal_variable_type
            and len(final_assembled_parts) == 1
            and final_assembled_parts[0].text is not None
            and all(
                getattr(final_assembled_parts[0], field) is None
                for field in final_assembled_parts[0].model_fields
                if field not in ["text", "role"]
            )
        ):
            return final_assembled_parts[0].text

        # Otherwise, construct a Content object (as JSON string).
        final_content_obj = genai_types.Content(parts=final_assembled_parts)
        return final_content_obj.model_dump_json(exclude_none=True)

    def __str__(self) -> str:
        return self.text if self.text else ""

    def __repr__(self) -> str:
        return f"PromptTemplate(text='{self.text}')"


class MetricPromptBuilder(PromptTemplate):
    """Builder class for structured LLM-based metric prompt template."""

    criteria: Optional[dict[str, str]] = Field(
        None,
        description="""A dictionary of criteria used to evaluate the model responses.
      The keys are criterion names, and the values are the corresponding
      criterion definitions.
      """,
    )

    rating_scores: Optional[dict[str, str]] = Field(
        None,
        description="""A dictionary mapping of rating score names to their definitions.""",
    )

    @staticmethod
    def _get_default_instruction() -> str:
        """Returns the default instruction for evaluation."""
        return (
            "You are an expert evaluator. Your task is to evaluate the quality"
            " of the responses generated by AI models. We will provide you with"
            " the user prompt and an AI-generated responses.\nYou should first"
            " read the user input carefully for analyzing the task, and then"
            " evaluate the quality of the responses based on the Criteria"
            " provided in the Evaluation section below.\nYou will assign the"
            " response a rating following the Rating Scores and Evaluation"
            " Steps. Give step by step explanations for your rating, and only"
            " choose ratings from the Rating Scores."
        )

    instruction: Optional[str] = Field(
        default_factory=lambda: MetricPromptBuilder._get_default_instruction(),
        description="""The general instruction to guide the model in performing the evaluation.
    If not provided, a default instruction for evaluation will be used.
    """,
    )

    metric_definition: Optional[str] = Field(
        None,
        description="""An optional high-level description of the metric to be evaluated.
      If not provided, this field will not be included in the prompt template.
      """,
    )

    @staticmethod
    def _get_default_evaluation_steps() -> dict[str, str]:
        """Returns the default evaluation steps for metric evaluation."""
        return {
            "Step 1": (
                "Assess the response in aspects of all criteria provided."
                " Provide assessment according to each criterion."
            ),
            "Step 2": (
                "Score based on the Rating Scores. Give a brief rationale to"
                " explain your evaluation considering each individual"
                " criterion."
            ),
        }

    evaluation_steps: Optional[dict[str, str]] = Field(
        default_factory=lambda: MetricPromptBuilder._get_default_evaluation_steps(),
        description="""An optional dictionary of evaluation steps.
      The keys are the names of the evaluation steps, and the values are
      descriptions of the corresponding evaluation steps. If not provided,
      default metric evaluation steps will be used.
      """,
    )

    few_shot_examples: Optional[list[str]] = Field(
        None,
        description="""An optional list of few-shot examples to guide the model's evaluation.
      These examples demonstrate how to apply the criteria, rating scores,
      and evaluation steps to assess model responses. Providing few-shot examples
      can improve the accuracy of the evaluation. If not provided, this field
      will not be included in the prompt template.
      """,
    )

    @staticmethod
    def _serialize_dict_in_order(elements: Optional[dict[str, str]]) -> str:
        """Serializes dictionary to ordered string value without brackets."""
        if elements is None:
            return ""
        return "\n".join(f"{key}: {value}" for key, value in sorted(elements.items()))

    @model_validator(mode="before")
    @classmethod
    def _prepare_fields_and_construct_text(cls, data: Any) -> Any:
        """Pydantic model validator (before mode) to prepare and construct prompt text.

        This validator performs the following actions:
          1. Apply default logic for fields (instruction, evaluation_steps).
          2. Construct the 'text' string from all components.
          3. Ensure 'text' is in the data dictionary for PromptTemplate
          initialization.

        Args:
            data: Input data for the model, either a dictionary or an existing
              model instance.

        Returns:
            Processed data dictionary with the 'text' field constructed.
        """
        if not isinstance(data, dict):
            return data

        if "text" in data:
            raise ValueError(
                "The 'text' field is automatically constructed and should not"
                " be provided manually."
            )

        if data.get("criteria") is None or data.get("rating_scores") is None:
            raise ValueError(
                "Both 'criteria' and 'rating_scores' are required to construct"
                " theLLM-based metric prompt template text."
            )

        instruction = data.get("instruction", cls._get_default_instruction())
        metric_definition = data.get("metric_definition")
        evaluation_steps = data.get(
            "evaluation_steps", cls._get_default_evaluation_steps()
        )
        criteria = data.get("criteria")
        rating_scores = data.get("rating_scores")
        few_shot_examples = data.get("few_shot_examples")

        template_parts = [
            "# Instruction",
            instruction,
            "\n",
            "# Evaluation",
        ]

        sections = {
            "Metric Definition": metric_definition,
            "Criteria": cls._serialize_dict_in_order(criteria),
            "Rating Scores": cls._serialize_dict_in_order(rating_scores),
            "Evaluation Steps": cls._serialize_dict_in_order(evaluation_steps),
            "Evaluation Examples": (
                "\n".join(few_shot_examples) if few_shot_examples else None
            ),
        }

        for title, content in sections.items():
            if content:
                template_parts.extend([f"## {title}", f"{content}\n"])

        template_parts.extend(
            [
                "\n",
                "# User Inputs and AI-generated Response",
                "## User Prompt",
                "<prompt>{prompt}</prompt>",
                "\n",
                "## AI-generated Response",
                "<response>{response}</response>",
            ]
        )

        constructed_text = "\n".join(template_parts)

        data["text"] = constructed_text
        return data

    def __str__(self) -> str:
        """Returns the fully constructed prompt template text."""
        return self.text if self.text else ""


class PromptTemplateDict(TypedDict, total=False):
    """A prompt template for creating prompts with variables."""

    text: Optional[str]
    """The prompt template text."""


PromptTemplateOrDict = Union[PromptTemplate, PromptTemplateDict]


class EvalRunInferenceConfig(_common.BaseModel):
    """Optional parameters for inference."""

    dest: Optional[str] = Field(
        default=None,
        description="""The destination path for the inference results.""",
    )
    prompt_template: Optional[Union[str, PromptTemplate]] = Field(
        default=None,
        description="""The prompt template to use for inference.""",
    )
    generate_content_config: Optional[genai_types.GenerateContentConfig] = Field(
        default=None,
        description="""The config for the generate content call.""",
    )


class EvalRunInferenceConfigDict(TypedDict, total=False):
    """Optional parameters for inference."""

    dest: Optional[str]
    """The destination path for the inference results."""

    prompt_template: Optional[Union[str, PromptTemplateDict]]
    """The prompt template to use for inference."""

    generate_content_config: Optional[genai_types.GenerateContentConfig]
    """The config for the generate content call."""


EvalRunInferenceConfigOrDict = Union[EvalRunInferenceConfig, EvalRunInferenceConfigDict]


class Metric(_common.BaseModel):
    """The metric used for evaluation."""

    name: Optional[str] = Field(default=None, description="""The name of the metric.""")
    custom_function: Optional[Callable] = Field(
        default=None,
        description="""The custom function that defines the end-to-end logic for metric computation.""",
    )
    prompt_template: Optional[str] = Field(
        default=None, description="""The prompt template for the metric."""
    )
    judge_model: Optional[str] = Field(
        default=None, description="""The judge model for the metric."""
    )
    judge_model_sampling_count: Optional[int] = Field(
        default=None, description="""The sampling count for the judge model."""
    )
    judge_model_system_instruction: Optional[str] = Field(
        default=None,
        description="""The system instruction for the judge model.""",
    )
    return_raw_output: Optional[bool] = Field(
        default=None,
        description="""Whether to return the raw output from the judge model.""",
    )
    parse_and_reduce_fn: Optional[Callable] = Field(
        default=None,
        description="""The parse and reduce function for the judge model.""",
    )
    aggregate_summary_fn: Optional[Callable] = Field(
        default=None,
        description="""The aggregate summary function for the judge model.""",
    )

    # Allow extra fields to support metric-specific config fields.
    model_config = ConfigDict(extra="allow")

    _is_predefined: bool = PrivateAttr(default=False)
    """A boolean indicating whether the metric is predefined."""

    _config_source: Optional[str] = PrivateAttr(default=None)
    """An optional string indicating the source of the metric configuration."""

    _version: Optional[str] = PrivateAttr(default=None)
    """An optional string indicating the version of the metric."""

    @model_validator(mode="after")
    @classmethod
    def validate_name(cls, model: "Metric") -> "Metric":
        if not model.name:
            raise ValueError("Metric name cannot be empty.")
        model.name = model.name.lower()
        return model

    def to_yaml_file(self, file_path: str, version: Optional[str] = None) -> None:
        """Dumps the metric object to a YAML file.

        Args:
            file_path: The path to the YAML file.
            version: Optional version string to include in the YAML output.

        Raises:
            ImportError: If the pyyaml library is not installed.
        """
        if yaml is None:
            raise ImportError(
                "YAML serialization requires the pyyaml library. Please install"
                " it using 'pip install google-cloud-aiplatform[evaluation]'."
            )

        fields_to_exclude_callables = set()
        for field_name, field_info in self.model_fields.items():
            annotation = field_info.annotation
            origin = typing.get_origin(annotation)

            is_field_callable_type = False
            if annotation is Callable or origin is Callable:
                is_field_callable_type = True
            elif origin is Union:
                args = typing.get_args(annotation)
                if any(
                    arg is Callable or typing.get_origin(arg) is Callable
                    for arg in args
                ):
                    is_field_callable_type = True

            if is_field_callable_type:
                fields_to_exclude_callables.add(field_name)

        data_to_dump = self.model_dump(
            exclude_unset=True,
            exclude_none=True,
            mode="json",
            exclude=fields_to_exclude_callables
            if fields_to_exclude_callables
            else None,
        )

        if version:
            data_to_dump["version"] = version

        with open(file_path, "w", encoding="utf-8") as f:
            yaml.dump(data_to_dump, f, sort_keys=False, allow_unicode=True)


class LLMMetric(Metric):
    """A metric that uses LLM-as-a-judge for evaluation."""

    @field_validator("prompt_template", mode="before")
    @classmethod
    def validate_prompt_template(cls, value: Union[str, "MetricPromptBuilder"]) -> str:
        """Validates prompt template to be a non-empty string."""
        if value is None:
            raise ValueError("Prompt template cannot be empty.")
        if isinstance(value, MetricPromptBuilder):
            value = str(value)
        if not value.strip():
            raise ValueError("Prompt template cannot be an empty string.")
        return value

    @field_validator("judge_model_sampling_count")
    @classmethod
    def validate_judge_model_sampling_count(cls, value: Optional[int]) -> Optional[int]:
        """Validates judge_model_sampling_count to be between 1 and 32."""
        if value is not None and (value < 1 or value > 32):
            raise ValueError("judge_model_sampling_count must be between 1 and 32.")
        return value

    @classmethod
    def load(cls, config_path: str, client: Optional[Any] = None) -> "LLMMetric":
        """Loads a metric configuration from a YAML or JSON file.

        This method allows for the creation of an LLMMetric instance from a
        local file path or a Google Cloud Storage (GCS) URI. It will
        automatically
        detect the file type (.yaml, .yml, or .json) and parse it accordingly.

        Args:
            config_path: The local path or GCS URI (e.g.,
              'gs://bucket/metric.yaml') to the metric configuration file.
            client: Optional. The Vertex AI client instance to use for
              authentication. If not provided, Application Default Credentials
              (ADC) will be used.

        Returns:
            An instance of LLMMetric configured with the loaded data.

        Raises:
            ValueError: If the file path is invalid or the file content cannot
            be parsed.
            ImportError: If a required library like 'PyYAML' or
            'google-cloud-storage' is not installed.
            IOError: If the file cannot be read from the specified path.
        """
        file_extension = os.path.splitext(config_path)[1].lower()
        if file_extension not in [".yaml", ".yml", ".json"]:
            raise ValueError(
                "Unsupported file extension for metric config. Must be .yaml,"
                " .yml, or .json"
            )

        content_str: str
        if config_path.startswith("gs://"):
            try:
                from google.cloud import storage

                storage_client = storage.Client(
                    credentials=client._api_client._credentials if client else None
                )
                path_without_prefix = config_path[len("gs://") :]
                bucket_name, blob_path = path_without_prefix.split("/", 1)

                bucket = storage_client.bucket(bucket_name)
                blob = bucket.blob(blob_path)
                content_str = blob.download_as_bytes().decode("utf-8")
            except ImportError as e:
                raise ImportError(
                    "Reading from GCS requires the 'google-cloud-storage'"
                    " library. Please install it with 'pip install"
                    " google-cloud-aiplatform[evaluation]'."
                ) from e
            except Exception as e:
                raise IOError(f"Failed to read from GCS path {config_path}: {e}") from e
        else:
            try:
                with open(config_path, "r", encoding="utf-8") as f:
                    content_str = f.read()
            except FileNotFoundError:
                raise FileNotFoundError(
                    f"Local configuration file not found at: {config_path}"
                )
            except Exception as e:
                raise IOError(f"Failed to read local file {config_path}: {e}") from e

        data: Dict[str, Any]

        if file_extension in [".yaml", ".yml"]:
            if yaml is None:
                raise ImportError(
                    "YAML parsing requires the pyyaml library. Please install"
                    " it with 'pip install"
                    " google-cloud-aiplatform[evaluation]'."
                )
            data = yaml.safe_load(content_str)
        elif file_extension == ".json":
            data = json.loads(content_str)

        if not isinstance(data, dict):
            raise ValueError("Metric config content did not parse into a dictionary.")

        return cls.model_validate(data)


class MetricDict(TypedDict, total=False):
    """The metric used for evaluation."""

    name: Optional[str]
    """The name of the metric."""

    custom_function: Optional[Callable]
    """The custom function that defines the end-to-end logic for metric computation."""

    prompt_template: Optional[str]
    """The prompt template for the metric."""

    judge_model: Optional[str]
    """The judge model for the metric."""

    judge_model_sampling_count: Optional[int]
    """The sampling count for the judge model."""

    judge_model_system_instruction: Optional[str]
    """The system instruction for the judge model."""

    return_raw_output: Optional[bool]
    """Whether to return the raw output from the judge model."""

    parse_and_reduce_fn: Optional[Callable]
    """The parse and reduce function for the judge model."""

    aggregate_summary_fn: Optional[Callable]
    """The aggregate summary function for the judge model."""


MetricOrDict = Union[Metric, MetricDict]


class Message(_common.BaseModel):
    """Represents a single message turn in a conversation."""

    turn_id: Optional[str] = Field(
        default=None, description="""Unique identifier for the message turn."""
    )
    content: Optional[genai_types.Content] = Field(
        default=None,
        description="""Content of the message, including function call.""",
    )
    creation_timestamp: Optional[datetime.datetime] = Field(
        default=None,
        description="""Timestamp indicating when the message was created.""",
    )
    author: Optional[str] = Field(
        default=None,
        description="""Name of the entity that produced the message.""",
    )


class MessageDict(TypedDict, total=False):
    """Represents a single message turn in a conversation."""

    turn_id: Optional[str]
    """Unique identifier for the message turn."""

    content: Optional[genai_types.Content]
    """Content of the message, including function call."""

    creation_timestamp: Optional[datetime.datetime]
    """Timestamp indicating when the message was created."""

    author: Optional[str]
    """Name of the entity that produced the message."""


MessageOrDict = Union[Message, MessageDict]


class AgentData(_common.BaseModel):
    """Container for all agent-specific data."""

    tool_use_trajectory: Optional[list[Message]] = Field(
        default=None,
        description="""Tool use trajectory in chronological order.""",
    )
    intermediate_responses: Optional[list[Message]] = Field(
        default=None,
        description="""Intermediate responses generated by sub-agents to convey progress or status in a multi-agent system, distinct from the final response.""",
    )


class AgentDataDict(TypedDict, total=False):
    """Container for all agent-specific data."""

    tool_use_trajectory: Optional[list[MessageDict]]
    """Tool use trajectory in chronological order."""

    intermediate_responses: Optional[list[MessageDict]]
    """Intermediate responses generated by sub-agents to convey progress or status in a multi-agent system, distinct from the final response."""


AgentDataOrDict = Union[AgentData, AgentDataDict]


class ResponseCandidate(_common.BaseModel):
    """A model-generated content to the prompt."""

    response: Optional[genai_types.Content] = Field(
        default=None,
        description="""The final model-generated response to the `prompt`.""",
    )
    agent_data: Optional[AgentData] = Field(
        default=None,
        description="""Agent-specific data including tool use trajectory and intermediate responses for more complex interactions.""",
    )


class ResponseCandidateDict(TypedDict, total=False):
    """A model-generated content to the prompt."""

    response: Optional[genai_types.Content]
    """The final model-generated response to the `prompt`."""

    agent_data: Optional[AgentDataDict]
    """Agent-specific data including tool use trajectory and intermediate responses for more complex interactions."""


ResponseCandidateOrDict = Union[ResponseCandidate, ResponseCandidateDict]


class EvalCase(_common.BaseModel):
    """A comprehensive representation of a GenAI interaction for evaluation."""

    prompt: Optional[genai_types.Content] = Field(
        default=None,
        description="""The most recent user message (current input).""",
    )
    responses: Optional[list[ResponseCandidate]] = Field(
        default=None,
        description="""Model-generated replies to the last user message. Multiple responses are allowed to support use cases such as comparing different model outputs.""",
    )
    reference: Optional[ResponseCandidate] = Field(
        default=None,
        description="""User-provided, golden reference model reply to prompt in context of chat history.""",
    )
    system_instruction: Optional[genai_types.Content] = Field(
        default=None, description="""System instruction for the model."""
    )
    conversation_history: Optional[list[Message]] = Field(
        default=None,
        description="""List of all prior messages in the conversation (chat history).""",
    )
    eval_case_id: Optional[str] = Field(
        default=None,
        description="""Unique identifier for the evaluation case.""",
    )
    # Allow extra fields to support custom metric prompts and stay backward compatible.
    model_config = ConfigDict(frozen=True, extra="allow")


class EvalCaseDict(TypedDict, total=False):
    """A comprehensive representation of a GenAI interaction for evaluation."""

    prompt: Optional[genai_types.Content]
    """The most recent user message (current input)."""

    responses: Optional[list[ResponseCandidateDict]]
    """Model-generated replies to the last user message. Multiple responses are allowed to support use cases such as comparing different model outputs."""

    reference: Optional[ResponseCandidateDict]
    """User-provided, golden reference model reply to prompt in context of chat history."""

    system_instruction: Optional[genai_types.Content]
    """System instruction for the model."""

    conversation_history: Optional[list[MessageDict]]
    """List of all prior messages in the conversation (chat history)."""

    eval_case_id: Optional[str]
    """Unique identifier for the evaluation case."""


EvalCaseOrDict = Union[EvalCase, EvalCaseDict]


class GcsSource(_common.BaseModel):
    """Cloud storage source holds the dataset.

    Currently only one Cloud Storage file path is supported.
    """

    uris: Optional[list[str]] = Field(
        default=None,
        description="""Required. Google Cloud Storage URI(-s) to the input file(s). May contain wildcards. For more information on wildcards, see https://cloud.google.com/storage/docs/wildcards.""",
    )


class GcsSourceDict(TypedDict, total=False):
    """Cloud storage source holds the dataset.

    Currently only one Cloud Storage file path is supported.
    """

    uris: Optional[list[str]]
    """Required. Google Cloud Storage URI(-s) to the input file(s). May contain wildcards. For more information on wildcards, see https://cloud.google.com/storage/docs/wildcards."""


GcsSourceOrDict = Union[GcsSource, GcsSourceDict]


class BigQuerySource(_common.BaseModel):
    """The BigQuery location for the input content."""

    input_uri: Optional[str] = Field(
        default=None,
        description="""Required. BigQuery URI to a table, up to 2000 characters long. Accepted forms: * BigQuery path. For example: `bq://projectId.bqDatasetId.bqTableId`.""",
    )


class BigQuerySourceDict(TypedDict, total=False):
    """The BigQuery location for the input content."""

    input_uri: Optional[str]
    """Required. BigQuery URI to a table, up to 2000 characters long. Accepted forms: * BigQuery path. For example: `bq://projectId.bqDatasetId.bqTableId`."""


BigQuerySourceOrDict = Union[BigQuerySource, BigQuerySourceDict]


class EvaluationDataset(_common.BaseModel):
    """The dataset used for evaluation."""

    eval_cases: Optional[list[EvalCase]] = Field(
        default=None, description="""The evaluation cases to be evaluated."""
    )
    eval_dataset_df: Optional["pd.DataFrame"] = Field(
        default=None,
        description="""The evaluation dataset in the form of a Pandas DataFrame.""",
    )
    candidate_name: Optional[str] = Field(
        default=None,
        description="""The name of the candidate model or agent for this evaluation dataset.""",
    )
    gcs_source: Optional[GcsSource] = Field(
        default=None,
        description="""The GCS source for the evaluation dataset.""",
    )
    bigquery_source: Optional[BigQuerySource] = Field(
        default=None,
        description="""The BigQuery source for the evaluation dataset.""",
    )

    @model_validator(mode="before")
    @classmethod
    def _check_pandas_installed(cls, data: Any) -> Any:
        if isinstance(data, dict) and data.get("eval_dataset_df") is not None:
            if pd is None:
                logger.warning(
                    "Pandas is not installed, some evals features are not"
                    " available. Please install it with `pip install"
                    " google-cloud-aiplatform[evaluation]`."
                )
        return data

    def show(self) -> None:
        """Shows the evaluation dataset."""
        from . import _evals_visualization

        _evals_visualization.display_evaluation_dataset(self)


class EvaluationDatasetDict(TypedDict, total=False):
    """The dataset used for evaluation."""

    eval_cases: Optional[list[EvalCaseDict]]
    """The evaluation cases to be evaluated."""

    eval_dataset_df: Optional["pd.DataFrame"]
    """The evaluation dataset in the form of a Pandas DataFrame."""

    candidate_name: Optional[str]
    """The name of the candidate model or agent for this evaluation dataset."""

    gcs_source: Optional[GcsSourceDict]
    """The GCS source for the evaluation dataset."""

    bigquery_source: Optional[BigQuerySourceDict]
    """The BigQuery source for the evaluation dataset."""


EvaluationDatasetOrDict = Union[EvaluationDataset, EvaluationDatasetDict]


class WinRateStats(_common.BaseModel):
    """Statistics for win rates for a single metric."""

    win_rates: Optional[list[float]] = Field(
        default=None,
        description="""Win rates for the metric, one for each candidate.""",
    )
    tie_rate: Optional[float] = Field(
        default=None, description="""Tie rate for the metric."""
    )


class WinRateStatsDict(TypedDict, total=False):
    """Statistics for win rates for a single metric."""

    win_rates: Optional[list[float]]
    """Win rates for the metric, one for each candidate."""

    tie_rate: Optional[float]
    """Tie rate for the metric."""


WinRateStatsOrDict = Union[WinRateStats, WinRateStatsDict]


class EvalCaseMetricResult(_common.BaseModel):
    """Evaluation result for a single evaluation case for a single metric."""

    metric_name: Optional[str] = Field(
        default=None, description="""Name of the metric."""
    )
    score: Optional[float] = Field(default=None, description="""Score of the metric.""")
    explanation: Optional[str] = Field(
        default=None, description="""Explanation of the metric."""
    )
    raw_output: Optional[list[str]] = Field(
        default=None, description="""Raw output of the metric."""
    )
    rubrics: Optional[list[str]] = Field(
        default=None,
        description="""A list of rubrics used to evaluate the example for rubric-based metrics.""",
    )
    error_message: Optional[str] = Field(
        default=None, description="""Error message for the metric."""
    )


class EvalCaseMetricResultDict(TypedDict, total=False):
    """Evaluation result for a single evaluation case for a single metric."""

    metric_name: Optional[str]
    """Name of the metric."""

    score: Optional[float]
    """Score of the metric."""

    explanation: Optional[str]
    """Explanation of the metric."""

    raw_output: Optional[list[str]]
    """Raw output of the metric."""

    rubrics: Optional[list[str]]
    """A list of rubrics used to evaluate the example for rubric-based metrics."""

    error_message: Optional[str]
    """Error message for the metric."""


EvalCaseMetricResultOrDict = Union[EvalCaseMetricResult, EvalCaseMetricResultDict]


class ResponseCandidateResult(_common.BaseModel):
    """Aggregated metric results for a single response candidate of an EvalCase."""

    response_index: Optional[int] = Field(
        default=None,
        description="""Index of the response candidate this result pertains to.""",
    )
    metric_results: Optional[dict[str, EvalCaseMetricResult]] = Field(
        default=None,
        description="""A dictionary of metric results for this response candidate, keyed by metric name.""",
    )


class ResponseCandidateResultDict(TypedDict, total=False):
    """Aggregated metric results for a single response candidate of an EvalCase."""

    response_index: Optional[int]
    """Index of the response candidate this result pertains to."""

    metric_results: Optional[dict[str, EvalCaseMetricResultDict]]
    """A dictionary of metric results for this response candidate, keyed by metric name."""


ResponseCandidateResultOrDict = Union[
    ResponseCandidateResult, ResponseCandidateResultDict
]


class EvalCaseResult(_common.BaseModel):
    """Eval result for a single evaluation case."""

    eval_case_index: Optional[int] = Field(
        default=None, description="""Index of the evaluation case."""
    )
    response_candidate_results: Optional[list[ResponseCandidateResult]] = Field(
        default=None,
        description="""A list of results, one for each response candidate of the EvalCase.""",
    )


class EvalCaseResultDict(TypedDict, total=False):
    """Eval result for a single evaluation case."""

    eval_case_index: Optional[int]
    """Index of the evaluation case."""

    response_candidate_results: Optional[list[ResponseCandidateResultDict]]
    """A list of results, one for each response candidate of the EvalCase."""


EvalCaseResultOrDict = Union[EvalCaseResult, EvalCaseResultDict]


class AggregatedMetricResult(_common.BaseModel):
    """Evaluation result for a single metric for an evaluation dataset."""

    metric_name: Optional[str] = Field(
        default=None, description="""Name of the metric."""
    )
    num_cases_total: Optional[int] = Field(
        default=None, description="""Total number of cases in the dataset."""
    )
    num_cases_valid: Optional[int] = Field(
        default=None, description="""Number of valid cases in the dataset."""
    )
    num_cases_error: Optional[int] = Field(
        default=None,
        description="""Number of cases with errors in the dataset.""",
    )
    mean_score: Optional[float] = Field(
        default=None, description="""Mean score of the metric."""
    )
    stdev_score: Optional[float] = Field(
        default=None, description="""Standard deviation of the metric."""
    )

    # Allow extra fields to support custom aggregation stats.
    model_config = ConfigDict(extra="allow")


class AggregatedMetricResultDict(TypedDict, total=False):
    """Evaluation result for a single metric for an evaluation dataset."""

    metric_name: Optional[str]
    """Name of the metric."""

    num_cases_total: Optional[int]
    """Total number of cases in the dataset."""

    num_cases_valid: Optional[int]
    """Number of valid cases in the dataset."""

    num_cases_error: Optional[int]
    """Number of cases with errors in the dataset."""

    mean_score: Optional[float]
    """Mean score of the metric."""

    stdev_score: Optional[float]
    """Standard deviation of the metric."""


AggregatedMetricResultOrDict = Union[AggregatedMetricResult, AggregatedMetricResultDict]


class EvaluationRunMetadata(_common.BaseModel):
    """Metadata for an evaluation run."""

    candidate_names: Optional[list[str]] = Field(
        default=None,
        description="""Name of the candidate(s) being evaluated in the evaluation run.""",
    )
    dataset_name: Optional[str] = Field(
        default=None,
        description="""Name of the evaluation dataset used for the evaluation run.""",
    )
    dataset_id: Optional[str] = Field(
        default=None,
        description="""Unique identifier for the evaluation dataset used for the evaluation run.""",
    )
    creation_timestamp: Optional[datetime.datetime] = Field(
        default=None,
        description="""Creation timestamp of the evaluation run.""",
    )


class EvaluationRunMetadataDict(TypedDict, total=False):
    """Metadata for an evaluation run."""

    candidate_names: Optional[list[str]]
    """Name of the candidate(s) being evaluated in the evaluation run."""

    dataset_name: Optional[str]
    """Name of the evaluation dataset used for the evaluation run."""

    dataset_id: Optional[str]
    """Unique identifier for the evaluation dataset used for the evaluation run."""

    creation_timestamp: Optional[datetime.datetime]
    """Creation timestamp of the evaluation run."""


EvaluationRunMetadataOrDict = Union[EvaluationRunMetadata, EvaluationRunMetadataDict]


class EvaluationResult(_common.BaseModel):
    """Result of an evaluation run for an evaluation dataset."""

    eval_case_results: Optional[list[EvalCaseResult]] = Field(
        default=None,
        description="""A list of evaluation results for each evaluation case.""",
    )
    summary_metrics: Optional[list[AggregatedMetricResult]] = Field(
        default=None,
        description="""A list of summary-level evaluation results for each metric.""",
    )
    win_rates: Optional[dict[str, WinRateStats]] = Field(
        default=None,
        description="""A dictionary of win rates for each metric, only populated for multi-response evaluation runs.""",
    )
    evaluation_dataset: Optional[list[EvaluationDataset]] = Field(
        default=None,
        description="""The input evaluation dataset(s) for the evaluation run.""",
    )
    metadata: Optional[EvaluationRunMetadata] = Field(
        default=None, description="""Metadata for the evaluation run."""
    )

    def show(self, candidate_names: Optional[List[str]] = None) -> None:
        """Shows the evaluation result.

        Args:
            candidate_names: list of names for the evaluated candidates, used in
              comparison reports.
        """
        from . import _evals_visualization

        _evals_visualization.display_evaluation_result(self, candidate_names)


class EvaluationResultDict(TypedDict, total=False):
    """Result of an evaluation run for an evaluation dataset."""

    eval_case_results: Optional[list[EvalCaseResultDict]]
    """A list of evaluation results for each evaluation case."""

    summary_metrics: Optional[list[AggregatedMetricResultDict]]
    """A list of summary-level evaluation results for each metric."""

    win_rates: Optional[dict[str, WinRateStatsDict]]
    """A dictionary of win rates for each metric, only populated for multi-response evaluation runs."""

    evaluation_dataset: Optional[list[EvaluationDatasetDict]]
    """The input evaluation dataset(s) for the evaluation run."""

    metadata: Optional[EvaluationRunMetadataDict]
    """Metadata for the evaluation run."""


EvaluationResultOrDict = Union[EvaluationResult, EvaluationResultDict]


class EvaluateMethodConfig(_common.BaseModel):
    """Optional parameters for the evaluate method."""

    http_options: Optional[HttpOptions] = Field(
        default=None, description="""Used to override HTTP request options."""
    )
    dataset_schema: Optional[Literal["GEMINI", "FLATTEN", "OPENAI"]] = Field(
        default=None,
        description="""The schema to use for the dataset.
      If not specified, the dataset schema will be inferred from the first
      example in the dataset.""",
    )
    dest: Optional[str] = Field(
        default=None,
        description="""The destination path for the evaluation results.""",
    )


class EvaluateMethodConfigDict(TypedDict, total=False):
    """Optional parameters for the evaluate method."""

    http_options: Optional[HttpOptionsDict]
    """Used to override HTTP request options."""

    dataset_schema: Optional[Literal["GEMINI", "FLATTEN", "OPENAI"]]
    """The schema to use for the dataset.
      If not specified, the dataset schema will be inferred from the first
      example in the dataset."""

    dest: Optional[str]
    """The destination path for the evaluation results."""


EvaluateMethodConfigOrDict = Union[EvaluateMethodConfig, EvaluateMethodConfigDict]


class OutputConfig(_common.BaseModel):
    """Config for evaluation output."""

    gcs_destination: Optional[GcsDestination] = Field(
        default=None,
        description="""Cloud storage destination for evaluation output.""",
    )


class OutputConfigDict(TypedDict, total=False):
    """Config for evaluation output."""

    gcs_destination: Optional[GcsDestinationDict]
    """Cloud storage destination for evaluation output."""


OutputConfigOrDict = Union[OutputConfig, OutputConfigDict]


class EvaluateDatasetConfig(_common.BaseModel):
    """Config for evaluate instances."""

    http_options: Optional[HttpOptions] = Field(
        default=None, description="""Used to override HTTP request options."""
    )


class EvaluateDatasetConfigDict(TypedDict, total=False):
    """Config for evaluate instances."""

    http_options: Optional[HttpOptionsDict]
    """Used to override HTTP request options."""


EvaluateDatasetConfigOrDict = Union[EvaluateDatasetConfig, EvaluateDatasetConfigDict]


class EvaluateDatasetRequestParameters(_common.BaseModel):
    """Parameters for batch dataset evaluation."""

    dataset: Optional[EvaluationDataset] = Field(default=None, description="""""")
    metrics: Optional[list[Metric]] = Field(default=None, description="""""")
    output_config: Optional[OutputConfig] = Field(default=None, description="""""")
    autorater_config: Optional[AutoraterConfig] = Field(
        default=None, description=""""""
    )
    config: Optional[EvaluateDatasetConfig] = Field(default=None, description="""""")


class EvaluateDatasetRequestParametersDict(TypedDict, total=False):
    """Parameters for batch dataset evaluation."""

    dataset: Optional[EvaluationDatasetDict]
    """"""

    metrics: Optional[list[MetricDict]]
    """"""

    output_config: Optional[OutputConfigDict]
    """"""

    autorater_config: Optional[AutoraterConfigDict]
    """"""

    config: Optional[EvaluateDatasetConfigDict]
    """"""


EvaluateDatasetRequestParametersOrDict = Union[
    EvaluateDatasetRequestParameters, EvaluateDatasetRequestParametersDict
]


class EvaluateDatasetOperation(_common.BaseModel):

    name: Optional[str] = Field(
        default=None,
        description="""The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.""",
    )
    metadata: Optional[dict[str, Any]] = Field(
        default=None,
        description="""Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata.  Any method that returns a long-running operation should document the metadata type, if any.""",
    )
    done: Optional[bool] = Field(
        default=None,
        description="""If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.""",
    )
    error: Optional[dict[str, Any]] = Field(
        default=None,
        description="""The error result of the operation in case of failure or cancellation.""",
    )
    response: Optional[EvaluationDataset] = Field(default=None, description="""""")


class EvaluateDatasetOperationDict(TypedDict, total=False):

    name: Optional[str]
    """The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`."""

    metadata: Optional[dict[str, Any]]
    """Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata.  Any method that returns a long-running operation should document the metadata type, if any."""

    done: Optional[bool]
    """If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available."""

    error: Optional[dict[str, Any]]
    """The error result of the operation in case of failure or cancellation."""

    response: Optional[EvaluationDatasetDict]
    """"""


EvaluateDatasetOperationOrDict = Union[
    EvaluateDatasetOperation, EvaluateDatasetOperationDict
]


class AgentEngine(_common.BaseModel):
    """An agent engine instance."""

    api_client: Optional[Any] = Field(
        default=None, description="""The underlying API client."""
    )
    api_async_client: Optional[Any] = Field(
        default=None,
        description="""The underlying API client for asynchronous operations.""",
    )
    api_resource: Optional[ReasoningEngine] = Field(
        default=None,
        description="""The underlying API resource (i.e. ReasoningEngine).""",
    )

    # Allows dynamic binding of methods based on the registered operations.
    model_config = ConfigDict(extra="allow")

    def __repr__(self) -> str:
        return (
            f"AgentEngine(api_resource.name='{self.api_resource.name}')"
            if self.api_resource is not None
            else "AgentEngine(api_resource.name=None)"
        )

    def operation_schemas(self) -> Optional[list[Dict[str, Any]]]:
        """Returns the schemas of all registered operations for the agent."""
        if not isinstance(self.api_resource, ReasoningEngine):
            raise ValueError("api_resource is not initialized.")
        if not self.api_resource.spec:
            raise ValueError("api_resource.spec is not initialized.")
        return self.api_resource.spec.class_methods

    def delete(
        self,
        force: bool = False,
        config: Optional[DeleteAgentEngineConfigOrDict] = None,
    ) -> None:
        """Deletes the agent engine.

        Args:
          force (bool): Optional. If set to True, child resources will also be
            deleted. Otherwise, the request will fail with FAILED_PRECONDITION
            error when the Agent Engine has undeleted child resources. Defaults
            to False.
          config (DeleteAgentEngineConfig): Optional. Additional configurations
            for deleting the Agent Engine.
        """
        if not isinstance(self.api_resource, ReasoningEngine):
            raise ValueError("api_resource is not initialized.")
        self.api_client.delete(name=self.api_resource.name, force=force, config=config)  # type: ignore[union-attr]


class AgentEngineDict(TypedDict, total=False):
    """An agent engine instance."""

    api_client: Optional[Any]
    """The underlying API client."""

    api_async_client: Optional[Any]
    """The underlying API client for asynchronous operations."""

    api_resource: Optional[ReasoningEngineDict]
    """The underlying API resource (i.e. ReasoningEngine)."""


AgentEngineOrDict = Union[AgentEngine, AgentEngineDict]


class AgentEngineConfig(_common.BaseModel):
    """Config for agent engine methods."""

    http_options: Optional[HttpOptions] = Field(
        default=None, description="""Used to override HTTP request options."""
    )
    staging_bucket: Optional[str] = Field(
        default=None,
        description="""The GCS bucket to use for staging the artifacts needed.

      It must be a valid GCS bucket name, e.g. "gs://bucket-name". It is
      required if `agent_engine` is specified.""",
    )
    requirements: Optional[Any] = Field(
        default=None,
        description="""The set of PyPI dependencies needed.

      It can either be the path to a single file (requirements.txt), or an
      ordered list of strings corresponding to each line of the requirements
      file.""",
    )
    display_name: Optional[str] = Field(
        default=None,
        description="""The user-defined name of the Agent Engine.

      The name can be up to 128 characters long and can comprise any UTF-8
      character.""",
    )
    description: Optional[str] = Field(
        default=None, description="""The description of the Agent Engine."""
    )
    gcs_dir_name: Optional[str] = Field(
        default=None,
        description="""The GCS bucket directory under `staging_bucket` to use for staging
      the artifacts needed.""",
    )
    extra_packages: Optional[list[str]] = Field(
        default=None,
        description="""The set of extra user-provided packages (if any).""",
    )
    env_vars: Optional[Any] = Field(
        default=None,
        description="""The environment variables to be set when running the Agent Engine.

      If it is a dictionary, the keys are the environment variable names, and
      the values are the corresponding values.""",
    )


class AgentEngineConfigDict(TypedDict, total=False):
    """Config for agent engine methods."""

    http_options: Optional[HttpOptionsDict]
    """Used to override HTTP request options."""

    staging_bucket: Optional[str]
    """The GCS bucket to use for staging the artifacts needed.

      It must be a valid GCS bucket name, e.g. "gs://bucket-name". It is
      required if `agent_engine` is specified."""

    requirements: Optional[Any]
    """The set of PyPI dependencies needed.

      It can either be the path to a single file (requirements.txt), or an
      ordered list of strings corresponding to each line of the requirements
      file."""

    display_name: Optional[str]
    """The user-defined name of the Agent Engine.

      The name can be up to 128 characters long and can comprise any UTF-8
      character."""

    description: Optional[str]
    """The description of the Agent Engine."""

    gcs_dir_name: Optional[str]
    """The GCS bucket directory under `staging_bucket` to use for staging
      the artifacts needed."""

    extra_packages: Optional[list[str]]
    """The set of extra user-provided packages (if any)."""

    env_vars: Optional[Any]
    """The environment variables to be set when running the Agent Engine.

      If it is a dictionary, the keys are the environment variable names, and
      the values are the corresponding values."""


AgentEngineConfigOrDict = Union[AgentEngineConfig, AgentEngineConfigDict]
