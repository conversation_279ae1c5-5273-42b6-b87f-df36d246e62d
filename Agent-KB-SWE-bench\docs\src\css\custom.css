/**
 * Any CSS included here will be global. The classic template
 * bundles Infima by default. Infima is a CSS framework designed to
 * work well for content-centric websites.
 */

/* You can override the default Infima variables here. */

:root {
  --ifm-code-font-size: 95%;
  --ifm-color-primary: #000;
  --ifm-background-color: #F1EAE0;
  --ifm-navbar-background-color: #F1EAE0;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.1);
  --secondary: #171717;
  --secondary-dark: #0a0a0a;
  --secondary-light: #737373;
}

/* For readability concerns, you should choose a lighter palette in dark mode. */
[data-theme="dark"] {
  --ifm-color-primary: #FFF;
  --ifm-background-color: #000;
  --ifm-navbar-background-color: #000;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.3);
  --secondary: #737373;
  --secondary-dark: #171717;
  --secondary-light: #ccc;
}

article a, .a {
  text-decoration: underline;
}
