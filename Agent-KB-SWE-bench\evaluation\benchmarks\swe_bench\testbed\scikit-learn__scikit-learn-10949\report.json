{"all": ["sklearn/utils/tests/test_validation.py::test_check_dataframe_warns_on_dtype", "sklearn/utils/tests/test_validation.py::test_as_float_array", "sklearn/utils/tests/test_validation.py::test_as_float_array_nan[X0]", "sklearn/utils/tests/test_validation.py::test_as_float_array_nan[X1]", "sklearn/utils/tests/test_validation.py::test_np_matrix", "sklearn/utils/tests/test_validation.py::test_memmap", "sklearn/utils/tests/test_validation.py::test_ordering", "sklearn/utils/tests/test_validation.py::test_check_array_force_all_finite_valid[asarray-inf-False]", "sklearn/utils/tests/test_validation.py::test_check_array_force_all_finite_valid[asarray-nan-allow-nan]", "sklearn/utils/tests/test_validation.py::test_check_array_force_all_finite_valid[asarray-nan-False]", "sklearn/utils/tests/test_validation.py::test_check_array_force_all_finite_valid[csr_matrix-inf-False]", "sklearn/utils/tests/test_validation.py::test_check_array_force_all_finite_valid[csr_matrix-nan-allow-nan]", "sklearn/utils/tests/test_validation.py::test_check_array_force_all_finite_valid[csr_matrix-nan-False]", "sklearn/utils/tests/test_validation.py::test_check_array", "sklearn/utils/tests/test_validation.py::test_check_array_pandas_dtype_object_conversion", "sklearn/utils/tests/test_validation.py::test_check_array_on_mock_dataframe", "sklearn/utils/tests/test_validation.py::test_check_array_dtype_stability", "sklearn/utils/tests/test_validation.py::test_check_array_dtype_warning", "sklearn/utils/tests/test_validation.py::test_check_array_accept_sparse_type_exception", "sklearn/utils/tests/test_validation.py::test_check_array_accept_sparse_no_exception", "sklearn/utils/tests/test_validation.py::test_check_array_accept_large_sparse_no_exception[csr]", "sklearn/utils/tests/test_validation.py::test_check_array_accept_large_sparse_no_exception[csc]", "sklearn/utils/tests/test_validation.py::test_check_array_accept_large_sparse_no_exception[coo]", "sklearn/utils/tests/test_validation.py::test_check_array_accept_large_sparse_no_exception[bsr]", "sklearn/utils/tests/test_validation.py::test_check_array_accept_large_sparse_raise_exception[csr]", "sklearn/utils/tests/test_validation.py::test_check_array_accept_large_sparse_raise_exception[csc]", "sklearn/utils/tests/test_validation.py::test_check_array_accept_large_sparse_raise_exception[coo]", "sklearn/utils/tests/test_validation.py::test_check_array_accept_large_sparse_raise_exception[bsr]", "sklearn/utils/tests/test_validation.py::test_check_array_large_indices_non_supported_scipy_version[csr]", "sklearn/utils/tests/test_validation.py::test_check_array_large_indices_non_supported_scipy_version[csc]", "sklearn/utils/tests/test_validation.py::test_check_array_large_indices_non_supported_scipy_version[coo]", "sklearn/utils/tests/test_validation.py::test_check_array_large_indices_non_supported_scipy_version[bsr]", "sklearn/utils/tests/test_validation.py::test_check_array_min_samples_and_features_messages", "sklearn/utils/tests/test_validation.py::test_check_array_complex_data_error", "sklearn/utils/tests/test_validation.py::test_has_fit_parameter", "sklearn/utils/tests/test_validation.py::test_check_symmetric", "sklearn/utils/tests/test_validation.py::test_check_is_fitted", "sklearn/utils/tests/test_validation.py::test_check_consistent_length", "sklearn/utils/tests/test_validation.py::test_check_dataframe_fit_attribute", "sklearn/utils/tests/test_validation.py::test_suppress_validation", "sklearn/utils/tests/test_validation.py::test_check_memory", "sklearn/utils/tests/test_validation.py::test_check_array_memmap[True]", "sklearn/utils/tests/test_validation.py::test_check_array_memmap[False]"], "FAIL_TO_PASS": {"success": [], "failure": ["sklearn/utils/tests/test_validation.py::test_check_dataframe_warns_on_dtype"]}, "PASS_TO_PASS": {"success": [], "failure": ["sklearn/utils/tests/test_validation.py::test_as_float_array", "sklearn/utils/tests/test_validation.py::test_as_float_array_nan[X0]", "sklearn/utils/tests/test_validation.py::test_as_float_array_nan[X1]", "sklearn/utils/tests/test_validation.py::test_np_matrix", "sklearn/utils/tests/test_validation.py::test_memmap", "sklearn/utils/tests/test_validation.py::test_ordering", "sklearn/utils/tests/test_validation.py::test_check_array_force_all_finite_valid[asarray-inf-False]", "sklearn/utils/tests/test_validation.py::test_check_array_force_all_finite_valid[asarray-nan-allow-nan]", "sklearn/utils/tests/test_validation.py::test_check_array_force_all_finite_valid[asarray-nan-False]", "sklearn/utils/tests/test_validation.py::test_check_array_force_all_finite_valid[csr_matrix-inf-False]", "sklearn/utils/tests/test_validation.py::test_check_array_force_all_finite_valid[csr_matrix-nan-allow-nan]", "sklearn/utils/tests/test_validation.py::test_check_array_force_all_finite_valid[csr_matrix-nan-False]", "sklearn/utils/tests/test_validation.py::test_check_array", "sklearn/utils/tests/test_validation.py::test_check_array_pandas_dtype_object_conversion", "sklearn/utils/tests/test_validation.py::test_check_array_on_mock_dataframe", "sklearn/utils/tests/test_validation.py::test_check_array_dtype_stability", "sklearn/utils/tests/test_validation.py::test_check_array_dtype_warning", "sklearn/utils/tests/test_validation.py::test_check_array_accept_sparse_type_exception", "sklearn/utils/tests/test_validation.py::test_check_array_accept_sparse_no_exception", "sklearn/utils/tests/test_validation.py::test_check_array_accept_large_sparse_no_exception[csr]", "sklearn/utils/tests/test_validation.py::test_check_array_accept_large_sparse_no_exception[csc]", "sklearn/utils/tests/test_validation.py::test_check_array_accept_large_sparse_no_exception[coo]", "sklearn/utils/tests/test_validation.py::test_check_array_accept_large_sparse_no_exception[bsr]", "sklearn/utils/tests/test_validation.py::test_check_array_accept_large_sparse_raise_exception[csr]", "sklearn/utils/tests/test_validation.py::test_check_array_accept_large_sparse_raise_exception[csc]", "sklearn/utils/tests/test_validation.py::test_check_array_accept_large_sparse_raise_exception[coo]", "sklearn/utils/tests/test_validation.py::test_check_array_accept_large_sparse_raise_exception[bsr]", "sklearn/utils/tests/test_validation.py::test_check_array_large_indices_non_supported_scipy_version[csr]", "sklearn/utils/tests/test_validation.py::test_check_array_large_indices_non_supported_scipy_version[csc]", "sklearn/utils/tests/test_validation.py::test_check_array_large_indices_non_supported_scipy_version[coo]", "sklearn/utils/tests/test_validation.py::test_check_array_large_indices_non_supported_scipy_version[bsr]", "sklearn/utils/tests/test_validation.py::test_check_array_min_samples_and_features_messages", "sklearn/utils/tests/test_validation.py::test_check_array_complex_data_error", "sklearn/utils/tests/test_validation.py::test_has_fit_parameter", "sklearn/utils/tests/test_validation.py::test_check_symmetric", "sklearn/utils/tests/test_validation.py::test_check_is_fitted", "sklearn/utils/tests/test_validation.py::test_check_consistent_length", "sklearn/utils/tests/test_validation.py::test_check_dataframe_fit_attribute", "sklearn/utils/tests/test_validation.py::test_suppress_validation", "sklearn/utils/tests/test_validation.py::test_check_memory", "sklearn/utils/tests/test_validation.py::test_check_array_memmap[True]", "sklearn/utils/tests/test_validation.py::test_check_array_memmap[False]"]}, "FAIL_TO_FAIL": {"success": [], "failure": []}, "PASS_TO_FAIL": {"success": [], "failure": []}}