# SWE-bench Report
This folder contains the evaluation results of the SWE-bench using the [official evaluation docker containerization](https://github.com/princeton-nlp/SWE-bench/blob/main/docs/20240627_docker/README.md#choosing-the-right-cache_level).

## Summary
- total instances: 300
- submitted instances: 30
- completed instances: 22
- empty patch instances: 4
- resolved instances: 7
- unresolved instances: 15
- error instances: 4

## Resolved Instances
- [django__django-11422](./eval_outputs/django__django-11422/run_instance.log)
- [django__django-11583](./eval_outputs/django__django-11583/run_instance.log)
- [django__django-17087](./eval_outputs/django__django-17087/run_instance.log)
- [sphinx-doc__sphinx-8721](./eval_outputs/sphinx-doc__sphinx-8721/run_instance.log)
- [sympy__sympy-14774](./eval_outputs/sympy__sympy-14774/run_instance.log)
- [sympy__sympy-20590](./eval_outputs/sympy__sympy-20590/run_instance.log)
- [sympy__sympy-21612](./eval_outputs/sympy__sympy-21612/run_instance.log)

## Unresolved Instances
- [astropy__astropy-12907](./eval_outputs/astropy__astropy-12907/run_instance.log)
- [django__django-11797](./eval_outputs/django__django-11797/run_instance.log)
- [django__django-12113](./eval_outputs/django__django-12113/run_instance.log)
- [django__django-14017](./eval_outputs/django__django-14017/run_instance.log)
- [django__django-14915](./eval_outputs/django__django-14915/run_instance.log)
- [django__django-15202](./eval_outputs/django__django-15202/run_instance.log)
- [django__django-15252](./eval_outputs/django__django-15252/run_instance.log)
- [django__django-15738](./eval_outputs/django__django-15738/run_instance.log)
- [django__django-15996](./eval_outputs/django__django-15996/run_instance.log)
- [matplotlib__matplotlib-23476](./eval_outputs/matplotlib__matplotlib-23476/run_instance.log)
- [pytest-dev__pytest-5221](./eval_outputs/pytest-dev__pytest-5221/run_instance.log)
- [scikit-learn__scikit-learn-10508](./eval_outputs/scikit-learn__scikit-learn-10508/run_instance.log)
- [scikit-learn__scikit-learn-13142](./eval_outputs/scikit-learn__scikit-learn-13142/run_instance.log)
- [sphinx-doc__sphinx-8282](./eval_outputs/sphinx-doc__sphinx-8282/run_instance.log)
- [sympy__sympy-17022](./eval_outputs/sympy__sympy-17022/run_instance.log)

## Error Instances
- [matplotlib__matplotlib-23987](./eval_outputs/matplotlib__matplotlib-23987/run_instance.log)
- [matplotlib__matplotlib-25311](./eval_outputs/matplotlib__matplotlib-25311/run_instance.log)
- [pytest-dev__pytest-11148](./eval_outputs/pytest-dev__pytest-11148/run_instance.log)
- [sympy__sympy-22005](./eval_outputs/sympy__sympy-22005/run_instance.log)

## Empty Patch Instances
- [matplotlib__matplotlib-18869](./eval_outputs/matplotlib__matplotlib-18869/run_instance.log)
- [matplotlib__matplotlib-24334](./eval_outputs/matplotlib__matplotlib-24334/run_instance.log)
- [sympy__sympy-11897](./eval_outputs/sympy__sympy-11897/run_instance.log)
- [sympy__sympy-21171](./eval_outputs/sympy__sympy-21171/run_instance.log)

## Incomplete Instances
- [astropy__astropy-14182](./eval_outputs/astropy__astropy-14182/run_instance.log)
- [astropy__astropy-14365](./eval_outputs/astropy__astropy-14365/run_instance.log)
- [astropy__astropy-14995](./eval_outputs/astropy__astropy-14995/run_instance.log)
- [astropy__astropy-6938](./eval_outputs/astropy__astropy-6938/run_instance.log)
- [astropy__astropy-7746](./eval_outputs/astropy__astropy-7746/run_instance.log)
- [django__django-10914](./eval_outputs/django__django-10914/run_instance.log)
- [django__django-10924](./eval_outputs/django__django-10924/run_instance.log)
- [django__django-11001](./eval_outputs/django__django-11001/run_instance.log)
- [django__django-11019](./eval_outputs/django__django-11019/run_instance.log)
- [django__django-11039](./eval_outputs/django__django-11039/run_instance.log)
- [django__django-11049](./eval_outputs/django__django-11049/run_instance.log)
- [django__django-11099](./eval_outputs/django__django-11099/run_instance.log)
- [django__django-11133](./eval_outputs/django__django-11133/run_instance.log)
- [django__django-11179](./eval_outputs/django__django-11179/run_instance.log)
- [django__django-11283](./eval_outputs/django__django-11283/run_instance.log)
- [django__django-11564](./eval_outputs/django__django-11564/run_instance.log)
- [django__django-11620](./eval_outputs/django__django-11620/run_instance.log)
- [django__django-11630](./eval_outputs/django__django-11630/run_instance.log)
- [django__django-11742](./eval_outputs/django__django-11742/run_instance.log)
- [django__django-11815](./eval_outputs/django__django-11815/run_instance.log)
- [django__django-11848](./eval_outputs/django__django-11848/run_instance.log)
- [django__django-11905](./eval_outputs/django__django-11905/run_instance.log)
- [django__django-11910](./eval_outputs/django__django-11910/run_instance.log)
- [django__django-11964](./eval_outputs/django__django-11964/run_instance.log)
- [django__django-11999](./eval_outputs/django__django-11999/run_instance.log)
- [django__django-12125](./eval_outputs/django__django-12125/run_instance.log)
- [django__django-12184](./eval_outputs/django__django-12184/run_instance.log)
- [django__django-12284](./eval_outputs/django__django-12284/run_instance.log)
- [django__django-12286](./eval_outputs/django__django-12286/run_instance.log)
- [django__django-12308](./eval_outputs/django__django-12308/run_instance.log)
- [django__django-12453](./eval_outputs/django__django-12453/run_instance.log)
- [django__django-12470](./eval_outputs/django__django-12470/run_instance.log)
- [django__django-12497](./eval_outputs/django__django-12497/run_instance.log)
- [django__django-12589](./eval_outputs/django__django-12589/run_instance.log)
- [django__django-12700](./eval_outputs/django__django-12700/run_instance.log)
- [django__django-12708](./eval_outputs/django__django-12708/run_instance.log)
- [django__django-12747](./eval_outputs/django__django-12747/run_instance.log)
- [django__django-12856](./eval_outputs/django__django-12856/run_instance.log)
- [django__django-12908](./eval_outputs/django__django-12908/run_instance.log)
- [django__django-12915](./eval_outputs/django__django-12915/run_instance.log)
- [django__django-12983](./eval_outputs/django__django-12983/run_instance.log)
- [django__django-13028](./eval_outputs/django__django-13028/run_instance.log)
- [django__django-13033](./eval_outputs/django__django-13033/run_instance.log)
- [django__django-13158](./eval_outputs/django__django-13158/run_instance.log)
- [django__django-13220](./eval_outputs/django__django-13220/run_instance.log)
- [django__django-13230](./eval_outputs/django__django-13230/run_instance.log)
- [django__django-13265](./eval_outputs/django__django-13265/run_instance.log)
- [django__django-13315](./eval_outputs/django__django-13315/run_instance.log)
- [django__django-13321](./eval_outputs/django__django-13321/run_instance.log)
- [django__django-13401](./eval_outputs/django__django-13401/run_instance.log)
- [django__django-13447](./eval_outputs/django__django-13447/run_instance.log)
- [django__django-13448](./eval_outputs/django__django-13448/run_instance.log)
- [django__django-13551](./eval_outputs/django__django-13551/run_instance.log)
- [django__django-13590](./eval_outputs/django__django-13590/run_instance.log)
- [django__django-13658](./eval_outputs/django__django-13658/run_instance.log)
- [django__django-13660](./eval_outputs/django__django-13660/run_instance.log)
- [django__django-13710](./eval_outputs/django__django-13710/run_instance.log)
- [django__django-13757](./eval_outputs/django__django-13757/run_instance.log)
- [django__django-13768](./eval_outputs/django__django-13768/run_instance.log)
- [django__django-13925](./eval_outputs/django__django-13925/run_instance.log)
- [django__django-13933](./eval_outputs/django__django-13933/run_instance.log)
- [django__django-13964](./eval_outputs/django__django-13964/run_instance.log)
- [django__django-14016](./eval_outputs/django__django-14016/run_instance.log)
- [django__django-14155](./eval_outputs/django__django-14155/run_instance.log)
- [django__django-14238](./eval_outputs/django__django-14238/run_instance.log)
- [django__django-14382](./eval_outputs/django__django-14382/run_instance.log)
- [django__django-14411](./eval_outputs/django__django-14411/run_instance.log)
- [django__django-14534](./eval_outputs/django__django-14534/run_instance.log)
- [django__django-14580](./eval_outputs/django__django-14580/run_instance.log)
- [django__django-14608](./eval_outputs/django__django-14608/run_instance.log)
- [django__django-14667](./eval_outputs/django__django-14667/run_instance.log)
- [django__django-14672](./eval_outputs/django__django-14672/run_instance.log)
- [django__django-14730](./eval_outputs/django__django-14730/run_instance.log)
- [django__django-14752](./eval_outputs/django__django-14752/run_instance.log)
- [django__django-14787](./eval_outputs/django__django-14787/run_instance.log)
- [django__django-14855](./eval_outputs/django__django-14855/run_instance.log)
- [django__django-14997](./eval_outputs/django__django-14997/run_instance.log)
- [django__django-14999](./eval_outputs/django__django-14999/run_instance.log)
- [django__django-15061](./eval_outputs/django__django-15061/run_instance.log)
- [django__django-15213](./eval_outputs/django__django-15213/run_instance.log)
- [django__django-15320](./eval_outputs/django__django-15320/run_instance.log)
- [django__django-15347](./eval_outputs/django__django-15347/run_instance.log)
- [django__django-15388](./eval_outputs/django__django-15388/run_instance.log)
- [django__django-15400](./eval_outputs/django__django-15400/run_instance.log)
- [django__django-15498](./eval_outputs/django__django-15498/run_instance.log)
- [django__django-15695](./eval_outputs/django__django-15695/run_instance.log)
- [django__django-15781](./eval_outputs/django__django-15781/run_instance.log)
- [django__django-15789](./eval_outputs/django__django-15789/run_instance.log)
- [django__django-15790](./eval_outputs/django__django-15790/run_instance.log)
- [django__django-15814](./eval_outputs/django__django-15814/run_instance.log)
- [django__django-15819](./eval_outputs/django__django-15819/run_instance.log)
- [django__django-15851](./eval_outputs/django__django-15851/run_instance.log)
- [django__django-15902](./eval_outputs/django__django-15902/run_instance.log)
- [django__django-16041](./eval_outputs/django__django-16041/run_instance.log)
- [django__django-16046](./eval_outputs/django__django-16046/run_instance.log)
- [django__django-16139](./eval_outputs/django__django-16139/run_instance.log)
- [django__django-16229](./eval_outputs/django__django-16229/run_instance.log)
- [django__django-16255](./eval_outputs/django__django-16255/run_instance.log)
- [django__django-16379](./eval_outputs/django__django-16379/run_instance.log)
- [django__django-16400](./eval_outputs/django__django-16400/run_instance.log)
- [django__django-16408](./eval_outputs/django__django-16408/run_instance.log)
- [django__django-16527](./eval_outputs/django__django-16527/run_instance.log)
- [django__django-16595](./eval_outputs/django__django-16595/run_instance.log)
- [django__django-16816](./eval_outputs/django__django-16816/run_instance.log)
- [django__django-16820](./eval_outputs/django__django-16820/run_instance.log)
- [django__django-16873](./eval_outputs/django__django-16873/run_instance.log)
- [django__django-16910](./eval_outputs/django__django-16910/run_instance.log)
- [django__django-17051](./eval_outputs/django__django-17051/run_instance.log)
- [matplotlib__matplotlib-22711](./eval_outputs/matplotlib__matplotlib-22711/run_instance.log)
- [matplotlib__matplotlib-22835](./eval_outputs/matplotlib__matplotlib-22835/run_instance.log)
- [matplotlib__matplotlib-23299](./eval_outputs/matplotlib__matplotlib-23299/run_instance.log)
- [matplotlib__matplotlib-23314](./eval_outputs/matplotlib__matplotlib-23314/run_instance.log)
- [matplotlib__matplotlib-23562](./eval_outputs/matplotlib__matplotlib-23562/run_instance.log)
- [matplotlib__matplotlib-23563](./eval_outputs/matplotlib__matplotlib-23563/run_instance.log)
- [matplotlib__matplotlib-23913](./eval_outputs/matplotlib__matplotlib-23913/run_instance.log)
- [matplotlib__matplotlib-23964](./eval_outputs/matplotlib__matplotlib-23964/run_instance.log)
- [matplotlib__matplotlib-24149](./eval_outputs/matplotlib__matplotlib-24149/run_instance.log)
- [matplotlib__matplotlib-24265](./eval_outputs/matplotlib__matplotlib-24265/run_instance.log)
- [matplotlib__matplotlib-24970](./eval_outputs/matplotlib__matplotlib-24970/run_instance.log)
- [matplotlib__matplotlib-25079](./eval_outputs/matplotlib__matplotlib-25079/run_instance.log)
- [matplotlib__matplotlib-25332](./eval_outputs/matplotlib__matplotlib-25332/run_instance.log)
- [matplotlib__matplotlib-25433](./eval_outputs/matplotlib__matplotlib-25433/run_instance.log)
- [matplotlib__matplotlib-25442](./eval_outputs/matplotlib__matplotlib-25442/run_instance.log)
- [matplotlib__matplotlib-25498](./eval_outputs/matplotlib__matplotlib-25498/run_instance.log)
- [matplotlib__matplotlib-26011](./eval_outputs/matplotlib__matplotlib-26011/run_instance.log)
- [matplotlib__matplotlib-26020](./eval_outputs/matplotlib__matplotlib-26020/run_instance.log)
- [mwaskom__seaborn-2848](./eval_outputs/mwaskom__seaborn-2848/run_instance.log)
- [mwaskom__seaborn-3010](./eval_outputs/mwaskom__seaborn-3010/run_instance.log)
- [mwaskom__seaborn-3190](./eval_outputs/mwaskom__seaborn-3190/run_instance.log)
- [mwaskom__seaborn-3407](./eval_outputs/mwaskom__seaborn-3407/run_instance.log)
- [pallets__flask-4045](./eval_outputs/pallets__flask-4045/run_instance.log)
- [pallets__flask-4992](./eval_outputs/pallets__flask-4992/run_instance.log)
- [pallets__flask-5063](./eval_outputs/pallets__flask-5063/run_instance.log)
- [psf__requests-1963](./eval_outputs/psf__requests-1963/run_instance.log)
- [psf__requests-2148](./eval_outputs/psf__requests-2148/run_instance.log)
- [psf__requests-2317](./eval_outputs/psf__requests-2317/run_instance.log)
- [psf__requests-2674](./eval_outputs/psf__requests-2674/run_instance.log)
- [psf__requests-3362](./eval_outputs/psf__requests-3362/run_instance.log)
- [psf__requests-863](./eval_outputs/psf__requests-863/run_instance.log)
- [pydata__xarray-3364](./eval_outputs/pydata__xarray-3364/run_instance.log)
- [pydata__xarray-4094](./eval_outputs/pydata__xarray-4094/run_instance.log)
- [pydata__xarray-4248](./eval_outputs/pydata__xarray-4248/run_instance.log)
- [pydata__xarray-4493](./eval_outputs/pydata__xarray-4493/run_instance.log)
- [pydata__xarray-5131](./eval_outputs/pydata__xarray-5131/run_instance.log)
- [pylint-dev__pylint-5859](./eval_outputs/pylint-dev__pylint-5859/run_instance.log)
- [pylint-dev__pylint-6506](./eval_outputs/pylint-dev__pylint-6506/run_instance.log)
- [pylint-dev__pylint-7080](./eval_outputs/pylint-dev__pylint-7080/run_instance.log)
- [pylint-dev__pylint-7114](./eval_outputs/pylint-dev__pylint-7114/run_instance.log)
- [pylint-dev__pylint-7228](./eval_outputs/pylint-dev__pylint-7228/run_instance.log)
- [pylint-dev__pylint-7993](./eval_outputs/pylint-dev__pylint-7993/run_instance.log)
- [pytest-dev__pytest-11143](./eval_outputs/pytest-dev__pytest-11143/run_instance.log)
- [pytest-dev__pytest-5103](./eval_outputs/pytest-dev__pytest-5103/run_instance.log)
- [pytest-dev__pytest-5227](./eval_outputs/pytest-dev__pytest-5227/run_instance.log)
- [pytest-dev__pytest-5413](./eval_outputs/pytest-dev__pytest-5413/run_instance.log)
- [pytest-dev__pytest-5495](./eval_outputs/pytest-dev__pytest-5495/run_instance.log)
- [pytest-dev__pytest-5692](./eval_outputs/pytest-dev__pytest-5692/run_instance.log)
- [pytest-dev__pytest-6116](./eval_outputs/pytest-dev__pytest-6116/run_instance.log)
- [pytest-dev__pytest-7168](./eval_outputs/pytest-dev__pytest-7168/run_instance.log)
- [pytest-dev__pytest-7220](./eval_outputs/pytest-dev__pytest-7220/run_instance.log)
- [pytest-dev__pytest-7373](./eval_outputs/pytest-dev__pytest-7373/run_instance.log)
- [pytest-dev__pytest-7432](./eval_outputs/pytest-dev__pytest-7432/run_instance.log)
- [pytest-dev__pytest-7490](./eval_outputs/pytest-dev__pytest-7490/run_instance.log)
- [pytest-dev__pytest-8365](./eval_outputs/pytest-dev__pytest-8365/run_instance.log)
- [pytest-dev__pytest-8906](./eval_outputs/pytest-dev__pytest-8906/run_instance.log)
- [pytest-dev__pytest-9359](./eval_outputs/pytest-dev__pytest-9359/run_instance.log)
- [scikit-learn__scikit-learn-10297](./eval_outputs/scikit-learn__scikit-learn-10297/run_instance.log)
- [scikit-learn__scikit-learn-10949](./eval_outputs/scikit-learn__scikit-learn-10949/run_instance.log)
- [scikit-learn__scikit-learn-11040](./eval_outputs/scikit-learn__scikit-learn-11040/run_instance.log)
- [scikit-learn__scikit-learn-11281](./eval_outputs/scikit-learn__scikit-learn-11281/run_instance.log)
- [scikit-learn__scikit-learn-12471](./eval_outputs/scikit-learn__scikit-learn-12471/run_instance.log)
- [scikit-learn__scikit-learn-13241](./eval_outputs/scikit-learn__scikit-learn-13241/run_instance.log)
- [scikit-learn__scikit-learn-13439](./eval_outputs/scikit-learn__scikit-learn-13439/run_instance.log)
- [scikit-learn__scikit-learn-13496](./eval_outputs/scikit-learn__scikit-learn-13496/run_instance.log)
- [scikit-learn__scikit-learn-13497](./eval_outputs/scikit-learn__scikit-learn-13497/run_instance.log)
- [scikit-learn__scikit-learn-13584](./eval_outputs/scikit-learn__scikit-learn-13584/run_instance.log)
- [scikit-learn__scikit-learn-13779](./eval_outputs/scikit-learn__scikit-learn-13779/run_instance.log)
- [scikit-learn__scikit-learn-14087](./eval_outputs/scikit-learn__scikit-learn-14087/run_instance.log)
- [scikit-learn__scikit-learn-14092](./eval_outputs/scikit-learn__scikit-learn-14092/run_instance.log)
- [scikit-learn__scikit-learn-14894](./eval_outputs/scikit-learn__scikit-learn-14894/run_instance.log)
- [scikit-learn__scikit-learn-14983](./eval_outputs/scikit-learn__scikit-learn-14983/run_instance.log)
- [scikit-learn__scikit-learn-15512](./eval_outputs/scikit-learn__scikit-learn-15512/run_instance.log)
- [scikit-learn__scikit-learn-15535](./eval_outputs/scikit-learn__scikit-learn-15535/run_instance.log)
- [scikit-learn__scikit-learn-25500](./eval_outputs/scikit-learn__scikit-learn-25500/run_instance.log)
- [scikit-learn__scikit-learn-25570](./eval_outputs/scikit-learn__scikit-learn-25570/run_instance.log)
- [scikit-learn__scikit-learn-25638](./eval_outputs/scikit-learn__scikit-learn-25638/run_instance.log)
- [scikit-learn__scikit-learn-25747](./eval_outputs/scikit-learn__scikit-learn-25747/run_instance.log)
- [sphinx-doc__sphinx-10325](./eval_outputs/sphinx-doc__sphinx-10325/run_instance.log)
- [sphinx-doc__sphinx-10451](./eval_outputs/sphinx-doc__sphinx-10451/run_instance.log)
- [sphinx-doc__sphinx-11445](./eval_outputs/sphinx-doc__sphinx-11445/run_instance.log)
- [sphinx-doc__sphinx-7686](./eval_outputs/sphinx-doc__sphinx-7686/run_instance.log)
- [sphinx-doc__sphinx-7738](./eval_outputs/sphinx-doc__sphinx-7738/run_instance.log)
- [sphinx-doc__sphinx-7975](./eval_outputs/sphinx-doc__sphinx-7975/run_instance.log)
- [sphinx-doc__sphinx-8273](./eval_outputs/sphinx-doc__sphinx-8273/run_instance.log)
- [sphinx-doc__sphinx-8435](./eval_outputs/sphinx-doc__sphinx-8435/run_instance.log)
- [sphinx-doc__sphinx-8474](./eval_outputs/sphinx-doc__sphinx-8474/run_instance.log)
- [sphinx-doc__sphinx-8506](./eval_outputs/sphinx-doc__sphinx-8506/run_instance.log)
- [sphinx-doc__sphinx-8595](./eval_outputs/sphinx-doc__sphinx-8595/run_instance.log)
- [sphinx-doc__sphinx-8627](./eval_outputs/sphinx-doc__sphinx-8627/run_instance.log)
- [sphinx-doc__sphinx-8713](./eval_outputs/sphinx-doc__sphinx-8713/run_instance.log)
- [sphinx-doc__sphinx-8801](./eval_outputs/sphinx-doc__sphinx-8801/run_instance.log)
- [sympy__sympy-11400](./eval_outputs/sympy__sympy-11400/run_instance.log)
- [sympy__sympy-11870](./eval_outputs/sympy__sympy-11870/run_instance.log)
- [sympy__sympy-12171](./eval_outputs/sympy__sympy-12171/run_instance.log)
- [sympy__sympy-12236](./eval_outputs/sympy__sympy-12236/run_instance.log)
- [sympy__sympy-12419](./eval_outputs/sympy__sympy-12419/run_instance.log)
- [sympy__sympy-12454](./eval_outputs/sympy__sympy-12454/run_instance.log)
- [sympy__sympy-12481](./eval_outputs/sympy__sympy-12481/run_instance.log)
- [sympy__sympy-13031](./eval_outputs/sympy__sympy-13031/run_instance.log)
- [sympy__sympy-13043](./eval_outputs/sympy__sympy-13043/run_instance.log)
- [sympy__sympy-13146](./eval_outputs/sympy__sympy-13146/run_instance.log)
- [sympy__sympy-13177](./eval_outputs/sympy__sympy-13177/run_instance.log)
- [sympy__sympy-13437](./eval_outputs/sympy__sympy-13437/run_instance.log)
- [sympy__sympy-13471](./eval_outputs/sympy__sympy-13471/run_instance.log)
- [sympy__sympy-13480](./eval_outputs/sympy__sympy-13480/run_instance.log)
- [sympy__sympy-13647](./eval_outputs/sympy__sympy-13647/run_instance.log)
- [sympy__sympy-13773](./eval_outputs/sympy__sympy-13773/run_instance.log)
- [sympy__sympy-13895](./eval_outputs/sympy__sympy-13895/run_instance.log)
- [sympy__sympy-13915](./eval_outputs/sympy__sympy-13915/run_instance.log)
- [sympy__sympy-13971](./eval_outputs/sympy__sympy-13971/run_instance.log)
- [sympy__sympy-14024](./eval_outputs/sympy__sympy-14024/run_instance.log)
- [sympy__sympy-14308](./eval_outputs/sympy__sympy-14308/run_instance.log)
- [sympy__sympy-14317](./eval_outputs/sympy__sympy-14317/run_instance.log)
- [sympy__sympy-14396](./eval_outputs/sympy__sympy-14396/run_instance.log)
- [sympy__sympy-14817](./eval_outputs/sympy__sympy-14817/run_instance.log)
- [sympy__sympy-15011](./eval_outputs/sympy__sympy-15011/run_instance.log)
- [sympy__sympy-15308](./eval_outputs/sympy__sympy-15308/run_instance.log)
- [sympy__sympy-15345](./eval_outputs/sympy__sympy-15345/run_instance.log)
- [sympy__sympy-15346](./eval_outputs/sympy__sympy-15346/run_instance.log)
- [sympy__sympy-15609](./eval_outputs/sympy__sympy-15609/run_instance.log)
- [sympy__sympy-15678](./eval_outputs/sympy__sympy-15678/run_instance.log)
- [sympy__sympy-16106](./eval_outputs/sympy__sympy-16106/run_instance.log)
- [sympy__sympy-16281](./eval_outputs/sympy__sympy-16281/run_instance.log)
- [sympy__sympy-16503](./eval_outputs/sympy__sympy-16503/run_instance.log)
- [sympy__sympy-16792](./eval_outputs/sympy__sympy-16792/run_instance.log)
- [sympy__sympy-16988](./eval_outputs/sympy__sympy-16988/run_instance.log)
- [sympy__sympy-17139](./eval_outputs/sympy__sympy-17139/run_instance.log)
- [sympy__sympy-17630](./eval_outputs/sympy__sympy-17630/run_instance.log)
- [sympy__sympy-17655](./eval_outputs/sympy__sympy-17655/run_instance.log)
- [sympy__sympy-18057](./eval_outputs/sympy__sympy-18057/run_instance.log)
- [sympy__sympy-18087](./eval_outputs/sympy__sympy-18087/run_instance.log)
- [sympy__sympy-18189](./eval_outputs/sympy__sympy-18189/run_instance.log)
- [sympy__sympy-18199](./eval_outputs/sympy__sympy-18199/run_instance.log)
- [sympy__sympy-18532](./eval_outputs/sympy__sympy-18532/run_instance.log)
- [sympy__sympy-18621](./eval_outputs/sympy__sympy-18621/run_instance.log)
- [sympy__sympy-18698](./eval_outputs/sympy__sympy-18698/run_instance.log)
- [sympy__sympy-18835](./eval_outputs/sympy__sympy-18835/run_instance.log)
- [sympy__sympy-19007](./eval_outputs/sympy__sympy-19007/run_instance.log)
- [sympy__sympy-19254](./eval_outputs/sympy__sympy-19254/run_instance.log)
- [sympy__sympy-19487](./eval_outputs/sympy__sympy-19487/run_instance.log)
- [sympy__sympy-20049](./eval_outputs/sympy__sympy-20049/run_instance.log)
- [sympy__sympy-20154](./eval_outputs/sympy__sympy-20154/run_instance.log)
- [sympy__sympy-20212](./eval_outputs/sympy__sympy-20212/run_instance.log)
- [sympy__sympy-20322](./eval_outputs/sympy__sympy-20322/run_instance.log)
- [sympy__sympy-20442](./eval_outputs/sympy__sympy-20442/run_instance.log)
- [sympy__sympy-20639](./eval_outputs/sympy__sympy-20639/run_instance.log)
- [sympy__sympy-21055](./eval_outputs/sympy__sympy-21055/run_instance.log)
- [sympy__sympy-21379](./eval_outputs/sympy__sympy-21379/run_instance.log)
- [sympy__sympy-21614](./eval_outputs/sympy__sympy-21614/run_instance.log)
- [sympy__sympy-21627](./eval_outputs/sympy__sympy-21627/run_instance.log)
- [sympy__sympy-21847](./eval_outputs/sympy__sympy-21847/run_instance.log)
- [sympy__sympy-22714](./eval_outputs/sympy__sympy-22714/run_instance.log)
- [sympy__sympy-22840](./eval_outputs/sympy__sympy-22840/run_instance.log)
- [sympy__sympy-23117](./eval_outputs/sympy__sympy-23117/run_instance.log)
- [sympy__sympy-23191](./eval_outputs/sympy__sympy-23191/run_instance.log)
- [sympy__sympy-23262](./eval_outputs/sympy__sympy-23262/run_instance.log)
- [sympy__sympy-24066](./eval_outputs/sympy__sympy-24066/run_instance.log)
- [sympy__sympy-24102](./eval_outputs/sympy__sympy-24102/run_instance.log)
- [sympy__sympy-24152](./eval_outputs/sympy__sympy-24152/run_instance.log)
- [sympy__sympy-24213](./eval_outputs/sympy__sympy-24213/run_instance.log)
- [sympy__sympy-24909](./eval_outputs/sympy__sympy-24909/run_instance.log)
