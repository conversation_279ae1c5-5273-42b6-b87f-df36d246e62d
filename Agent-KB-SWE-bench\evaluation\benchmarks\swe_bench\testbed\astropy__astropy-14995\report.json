{"all": ["astropy/nddata/mixins/tests/test_ndarithmetic.py::test_nddata_bitmask_arithmetic", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data[data10-data20]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data[data11-data21]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data[data12-data22]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data[data13-data23]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data[data14-data24]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data[data15-data25]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data[data16-data26]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_invalid", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_unit_identical[data10-data20]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_unit_identical[data11-data21]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_unit_identical[data12-data22]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_unit_identical[data13-data23]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_unit_identical[data14-data24]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_unit_identical[data15-data25]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_unit_identical[data16-data26]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_unit_identical[data17-data27]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_unit_not_identical[data10-data20]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_unit_not_identical[data11-data21]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_unit_not_identical[data12-data22]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_unit_not_identical[data13-data23]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_wcs[None-None]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_wcs[None-wcs21]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_wcs[wcs12-None]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_wcs[wcs13-wcs23]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_wcs[wcs14-wcs24]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_masks[None-None]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_masks[None-False]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_masks[True-None]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_masks[False-False]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_masks[True-False]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_masks[False-True]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_masks[True-True]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_masks[mask17-mask27]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_masks[mask18-mask28]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_masks[mask19-mask29]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_masks[mask110-mask210]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_masks[mask111-mask211]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_masks[mask112-mask212]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_masks_invalid", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[-1-uncert10-data20]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[-0.5-uncert11-data21]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[-0.25-uncert12-data22]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[0-uncert13-data23]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[0.25-uncert14-data24]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[0.5-uncert15-data25]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[1-uncert16-data26]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[-1-uncert17-data27]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[-0.5-uncert18-data28]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[-0.25-uncert19-data29]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[0-uncert110-data210]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[0.25-uncert111-data211]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[0.5-uncert112-data212]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[1-uncert113-data213]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[-1-uncert114-data214]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[-0.5-uncert115-data215]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[-0.25-uncert116-data216]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[0-uncert117-data217]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[0.25-uncert118-data218]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[0.5-uncert119-data219]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[1-uncert120-data220]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[-1-uncert121-data221]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[-0.5-uncert122-data222]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[-0.25-uncert123-data223]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[0-uncert124-data224]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[0.25-uncert125-data225]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[0.5-uncert126-data226]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[1-uncert127-data227]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[-1-uncert10-data20]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[-0.5-uncert11-data21]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[-0.25-uncert12-data22]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[0-uncert13-data23]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[0.25-uncert14-data24]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[0.5-uncert15-data25]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[1-uncert16-data26]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[-1-uncert17-data27]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[-0.5-uncert18-data28]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[-0.25-uncert19-data29]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[0-uncert110-data210]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[0.25-uncert111-data211]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[0.5-uncert112-data212]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[1-uncert113-data213]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[-1-uncert114-data214]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[-0.5-uncert115-data215]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[-0.25-uncert116-data216]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[0-uncert117-data217]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[0.25-uncert118-data218]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[0.5-uncert119-data219]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[1-uncert120-data220]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[-1-uncert121-data221]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[-0.5-uncert122-data222]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[-0.25-uncert123-data223]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[0-uncert124-data224]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[0.25-uncert125-data225]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[0.5-uncert126-data226]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[1-uncert127-data227]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[-1-uncert10-data20]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[-0.5-uncert11-data21]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[-0.25-uncert12-data22]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[0-uncert13-data23]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[0.25-uncert14-data24]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[0.5-uncert15-data25]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[1-uncert16-data26]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[-1-uncert17-data27]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[-0.5-uncert18-data28]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[-0.25-uncert19-data29]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[0-uncert110-data210]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[0.25-uncert111-data211]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[0.5-uncert112-data212]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[1-uncert113-data213]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[-1-uncert114-data214]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[-0.5-uncert115-data215]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[-0.25-uncert116-data216]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[0-uncert117-data217]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[0.25-uncert118-data218]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[0.5-uncert119-data219]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[1-uncert120-data220]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[-1-uncert121-data221]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[-0.5-uncert122-data222]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[-0.25-uncert123-data223]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[0-uncert124-data224]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[0.25-uncert125-data225]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[0.5-uncert126-data226]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[1-uncert127-data227]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation_array", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_with_correlation_unsupported", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_one_missing", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_with_units[uncert10-None]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_with_units[uncert11-None]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_with_units[None-uncert22]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_with_units[None-uncert23]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_with_units[uncert14-uncert24]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_with_units[uncert15-uncert25]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_with_units[uncert16-uncert26]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_with_units[uncert17-uncert27]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_with_units[uncert18-uncert28]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_with_units[uncert19-uncert29]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_with_units[uncert110-uncert210]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_with_units[uncert111-uncert211]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_with_units[uncert10-None]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_with_units[uncert11-None]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_with_units[None-uncert22]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_with_units[None-uncert23]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_with_units[uncert14-uncert24]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_with_units[uncert15-uncert25]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_with_units[uncert16-uncert26]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_with_units[uncert17-uncert27]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_with_units[uncert18-uncert28]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_with_units[uncert19-uncert29]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_with_units[uncert110-uncert210]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_with_units[uncert111-uncert211]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_with_units[uncert10-None]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_with_units[uncert11-None]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_with_units[None-uncert22]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_with_units[None-uncert23]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_with_units[uncert14-uncert24]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_with_units[uncert15-uncert25]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_with_units[uncert16-uncert26]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_with_units[uncert17-uncert27]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_with_units[uncert18-uncert28]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_with_units[uncert19-uncert29]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_with_units[uncert110-uncert210]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_with_units[uncert111-uncert211]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_handle_switches[ff]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_handle_switches[first_found]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_meta_func", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_wcs_func", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_mask_func", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_two_argument_useage[add]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_two_argument_useage[subtract]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_two_argument_useage[divide]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_two_argument_useage[multiply]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_two_argument_useage_non_nddata_first_arg[add]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_two_argument_useage_non_nddata_first_arg[subtract]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_two_argument_useage_non_nddata_first_arg[divide]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_two_argument_useage_non_nddata_first_arg[multiply]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_unknown_uncertainties", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_psf_warning", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_raise_method_not_supported"], "FAIL_TO_PASS": {"success": ["astropy/nddata/mixins/tests/test_ndarithmetic.py::test_nddata_bitmask_arithmetic"], "failure": []}, "PASS_TO_PASS": {"success": ["astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data[data10-data20]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data[data11-data21]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data[data12-data22]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data[data13-data23]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data[data14-data24]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data[data15-data25]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data[data16-data26]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_invalid", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_unit_identical[data10-data20]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_unit_identical[data11-data21]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_unit_identical[data12-data22]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_unit_identical[data13-data23]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_unit_identical[data14-data24]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_unit_identical[data15-data25]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_unit_identical[data16-data26]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_unit_identical[data17-data27]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_unit_not_identical[data10-data20]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_unit_not_identical[data11-data21]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_unit_not_identical[data12-data22]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_unit_not_identical[data13-data23]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_wcs[None-None]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_wcs[None-wcs21]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_wcs[wcs12-None]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_wcs[wcs13-wcs23]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_wcs[wcs14-wcs24]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_masks[None-None]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_masks[None-False]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_masks[True-None]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_masks[False-False]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_masks[True-False]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_masks[False-True]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_masks[True-True]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_masks[mask17-mask27]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_masks[mask18-mask28]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_masks[mask19-mask29]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_masks[mask110-mask210]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_masks[mask111-mask211]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_masks[mask112-mask212]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_data_masks_invalid", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[-1-uncert10-data20]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[-0.5-uncert11-data21]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[-0.25-uncert12-data22]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[0-uncert13-data23]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[0.25-uncert14-data24]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[0.5-uncert15-data25]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[1-uncert16-data26]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[-1-uncert17-data27]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[-0.5-uncert18-data28]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[-0.25-uncert19-data29]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[0-uncert110-data210]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[0.25-uncert111-data211]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[0.5-uncert112-data212]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[1-uncert113-data213]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[-1-uncert114-data214]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[-0.5-uncert115-data215]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[-0.25-uncert116-data216]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[0-uncert117-data217]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[0.25-uncert118-data218]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[0.5-uncert119-data219]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[1-uncert120-data220]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[-1-uncert121-data221]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[-0.5-uncert122-data222]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[-0.25-uncert123-data223]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[0-uncert124-data224]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[0.25-uncert125-data225]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[0.5-uncert126-data226]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation[1-uncert127-data227]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[-1-uncert10-data20]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[-0.5-uncert11-data21]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[-0.25-uncert12-data22]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[0-uncert13-data23]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[0.25-uncert14-data24]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[0.5-uncert15-data25]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[1-uncert16-data26]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[-1-uncert17-data27]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[-0.5-uncert18-data28]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[-0.25-uncert19-data29]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[0-uncert110-data210]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[0.25-uncert111-data211]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[0.5-uncert112-data212]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[1-uncert113-data213]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[-1-uncert114-data214]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[-0.5-uncert115-data215]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[-0.25-uncert116-data216]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[0-uncert117-data217]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[0.25-uncert118-data218]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[0.5-uncert119-data219]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[1-uncert120-data220]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[-1-uncert121-data221]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[-0.5-uncert122-data222]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[-0.25-uncert123-data223]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[0-uncert124-data224]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[0.25-uncert125-data225]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[0.5-uncert126-data226]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_basic_with_correlation[1-uncert127-data227]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[-1-uncert10-data20]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[-0.5-uncert11-data21]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[-0.25-uncert12-data22]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[0-uncert13-data23]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[0.25-uncert14-data24]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[0.5-uncert15-data25]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[1-uncert16-data26]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[-1-uncert17-data27]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[-0.5-uncert18-data28]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[-0.25-uncert19-data29]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[0-uncert110-data210]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[0.25-uncert111-data211]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[0.5-uncert112-data212]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[1-uncert113-data213]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[-1-uncert114-data214]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[-0.5-uncert115-data215]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[-0.25-uncert116-data216]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[0-uncert117-data217]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[0.25-uncert118-data218]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[0.5-uncert119-data219]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[1-uncert120-data220]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[-1-uncert121-data221]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[-0.5-uncert122-data222]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[-0.25-uncert123-data223]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[0-uncert124-data224]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[0.25-uncert125-data225]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[0.5-uncert126-data226]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_basic_with_correlation[1-uncert127-data227]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_basic_with_correlation_array", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_with_correlation_unsupported", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_one_missing", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_with_units[uncert10-None]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_with_units[uncert11-None]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_with_units[None-uncert22]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_with_units[None-uncert23]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_with_units[uncert14-uncert24]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_with_units[uncert15-uncert25]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_with_units[uncert16-uncert26]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_with_units[uncert17-uncert27]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_with_units[uncert18-uncert28]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_with_units[uncert19-uncert29]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_with_units[uncert110-uncert210]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_stddevuncertainty_with_units[uncert111-uncert211]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_with_units[uncert10-None]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_with_units[uncert11-None]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_with_units[None-uncert22]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_with_units[None-uncert23]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_with_units[uncert14-uncert24]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_with_units[uncert15-uncert25]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_with_units[uncert16-uncert26]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_with_units[uncert17-uncert27]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_with_units[uncert18-uncert28]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_with_units[uncert19-uncert29]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_with_units[uncert110-uncert210]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_varianceuncertainty_with_units[uncert111-uncert211]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_with_units[uncert10-None]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_with_units[uncert11-None]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_with_units[None-uncert22]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_with_units[None-uncert23]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_with_units[uncert14-uncert24]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_with_units[uncert15-uncert25]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_with_units[uncert16-uncert26]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_with_units[uncert17-uncert27]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_with_units[uncert18-uncert28]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_with_units[uncert19-uncert29]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_with_units[uncert110-uncert210]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_inversevarianceuncertainty_with_units[uncert111-uncert211]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_handle_switches[ff]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_handle_switches[first_found]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_meta_func", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_wcs_func", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_mask_func", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_two_argument_useage[add]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_two_argument_useage[subtract]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_two_argument_useage[divide]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_two_argument_useage[multiply]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_two_argument_useage_non_nddata_first_arg[add]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_two_argument_useage_non_nddata_first_arg[subtract]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_two_argument_useage_non_nddata_first_arg[divide]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_two_argument_useage_non_nddata_first_arg[multiply]", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_arithmetics_unknown_uncertainties", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_psf_warning", "astropy/nddata/mixins/tests/test_ndarithmetic.py::test_raise_method_not_supported"], "failure": []}, "FAIL_TO_FAIL": {"success": [], "failure": []}, "PASS_TO_FAIL": {"success": [], "failure": []}}