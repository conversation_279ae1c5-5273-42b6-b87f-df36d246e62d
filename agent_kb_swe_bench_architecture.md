````mermaid
graph TB
    subgraph "Frontend Layer"
        UI[Web UI :3001]
        Terminal[Terminal Component]
        Chat[Chat Interface]
        Settings[Banner Settings]
    end

    subgraph "Communication Layer"
        WS[WebSocket :3000/ws]
        EventStream[Event Stream]
        Services[Frontend Services]
    end

    subgraph "Backend Core"
        Server[Server :3000]
        AgentController[Agent Controller]
        State[State Management]
        Plan[Plan Management]
    end

    subgraph "Agent Layer"
        CodeActAgent[CodeAct Agent]
        VisualBrowsingAgent[Visual Browsing Agent]
        MicroAgents[Micro Agents]
    end

    subgraph "Knowledge System"
        WorkingMemory[Working Memory]
        EpisodicMemory[Episodic Memory]
        SemanticKB[Semantic Knowledge Base]
        
        subgraph "Micro Agents"
            KnowledgeAgents[Knowledge-based Agents]
            RepoAgents[Repository Agents]
            TriggerSystem[Trigger System]
        end
    end

    subgraph "Runtime Environment"
        DockerRuntime[Docker Runtime]
        ActionExecutor[Action Executor]
        Browser[Browser]
        BashShell[Bash Shell]
        Jupyter[Jupyter Server]
        Plugins[Plugins]
    end

    subgraph "External Systems"
        LLM[LLM Models]
        SWEBench[SWE-Bench Dataset]
        SWEGym[SWE-Gym Environment]
    end

    %% Frontend connections
    UI --> Terminal
    UI --> Chat
    UI --> Settings
    Terminal --> WS
    Chat --> Services
    Settings --> Services
    Services --> WS

    %% Backend connections
    WS --> Server
    Server --> AgentController
    AgentController --> State
    AgentController --> Plan
    AgentController --> EventStream

    %% Agent connections
    AgentController --> CodeActAgent
    AgentController --> VisualBrowsingAgent
    CodeActAgent --> MicroAgents
    MicroAgents --> KnowledgeAgents
    MicroAgents --> RepoAgents
    TriggerSystem --> KnowledgeAgents

    %% Knowledge system
    CodeActAgent --> WorkingMemory
    CodeActAgent --> EpisodicMemory
    CodeActAgent --> SemanticKB

    %% Runtime connections
    AgentController --> DockerRuntime
    DockerRuntime --> ActionExecutor
    ActionExecutor --> Browser
    ActionExecutor --> BashShell
    ActionExecutor --> Jupyter
    ActionExecutor --> Plugins

    %% External connections
    CodeActAgent --> LLM
    AgentController --> SWEBench
    AgentController --> SWEGym

    %% Event flow
    EventStream -.-> AgentController
    EventStream -.-> DockerRuntime

    classDef frontend fill:#e1f5fe
    classDef backend fill:#f3e5f5
    classDef agent fill:#e8f5e8
    classDef knowledge fill:#fff3e0
    classDef runtime fill:#fce4ec
    classDef external fill:#f1f8e9

    class UI,Terminal,Chat,Settings frontend
    class Server,AgentController,State,Plan backend
    class CodeActAgent,VisualBrowsingAgent,MicroAgents agent
    class WorkingMemory,EpisodicMemory,SemanticKB,KnowledgeAgents,RepoAgents,TriggerSystem knowledge
    class DockerRuntime,ActionExecutor,Browser,BashShell,Jupyter,Plugins runtime
    class LLM,SWEBench,SWEGym external
```