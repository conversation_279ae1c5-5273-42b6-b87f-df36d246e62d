# リポジトリマイクロエージェント

## 概要

OpenHandsは、リポジトリ固有のコンテキストとガイドラインを提供することで、特定のリポジトリでより効果的に動作するようにカスタマイズできます。このセクションでは、OpenHandsをプロジェクトに最適化する方法を説明します。

## リポジトリマイクロエージェントの作成

リポジトリのルートに`.openhands/microagents/`ディレクトリを作成することで、リポジトリに対するOpenHandsの動作をカスタマイズできます。
最低限、このリポジトリで作業する際にエージェントに与えられる指示を含む
`.openhands/microagents/repo.md`ファイルが必要です。

### リポジトリマイクロエージェントのベストプラクティス

* **指示を最新に保つ**：プロジェクトの進化に合わせて`.openhands/microagents/`ディレクトリを定期的に更新します。
* **具体的に**：プロジェクト固有のパス、パターン、要件を含めます。
* **依存関係を文書化**：開発に必要なすべてのツールと依存関係をリストアップします。
* **例を含める**：プロジェクトの良いコードパターンの例を提供します。
* **規約を指定**：命名規則、ファイル構成、コードスタイルの設定を文書化します。

### リポジトリマイクロエージェントを作成するステップ

#### 1. リポジトリマイクロエージェントの計画

リポジトリ固有のマイクロエージェントを作成する際は、以下の情報を含めることを推奨します：

* **リポジトリの概要**：プロジェクトの目的とアーキテクチャの簡単な説明。
* **ディレクトリ構造**：主要なディレクトリとその目的。
* **開発ガイドライン**：プロジェクト固有のコーディング標準とプラクティス。
* **テスト要件**：テストの実行方法と必要なテストの種類。
* **セットアップ手順**：プロジェクトのビルドと実行に必要な手順。

#### 2. ファイルの作成

リポジトリの`.openhands/microagents/`ディレクトリにファイルを作成します（例：`.openhands/microagents/repo.md`）

[必要なフォーマット](./microagents-overview#microagent-format)に従って必要なフロントマターと、リポジトリに必要な特殊なガイドラインでファイルを更新します。

### リポジトリマイクロエージェントの例

```
---
name: repo
type: repo
agent: CodeActAgent
---

リポジトリ：MyProject
説明：タスク管理のためのWebアプリケーション

ディレクトリ構造：
- src/：メインアプリケーションコード
- tests/：テストファイル
- docs/：ドキュメント

セットアップ：
- `npm install`で依存関係をインストール
- 開発には`npm run dev`を使用
- テストには`npm test`を実行

ガイドライン：
- ESLint設定に従う
- すべての新機能にテストを書く
- 新しいコードにはTypeScriptを使用

src/componentsに新しいコンポーネントを追加する場合は、必ずtests/components/に適切なユニットテストを追加してください。
```
