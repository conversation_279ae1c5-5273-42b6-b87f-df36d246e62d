repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
        exclude: docs/modules/python
      - id: end-of-file-fixer
        exclude: docs/modules/python
      - id: check-yaml
      - id: debug-statements

  - repo: https://github.com/tox-dev/pyproject-fmt
    rev: 1.7.0
    hooks:
      - id: pyproject-fmt
  - repo: https://github.com/abravalheri/validate-pyproject
    rev: v0.16
    hooks:
      - id: validate-pyproject

  - repo: https://github.com/astral-sh/ruff-pre-commit
    # Ruff version.
    rev: v0.4.1
    hooks:
      # Run the linter.
      - id: ruff
        entry: ruff check --config dev_config/python/ruff.toml
        types_or: [python, pyi, jupyter]
        args: [--fix]
      # Run the formatter.
      - id: ruff-format
        entry: ruff format --config dev_config/python/ruff.toml
        types_or: [python, pyi, jupyter]

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.9.0
    hooks:
      - id: mypy
        additional_dependencies:
          [types-requests, types-setuptools, types-pyyaml, types-toml]
        entry: mypy --config-file dev_config/python/mypy.ini openhands/
        always_run: true
        pass_filenames: false
