query_rollout: |
  Your task is to receive a search query, analyze the user's intent, break down the search task, and generate N new search queries to improve search quality. The original query is: {query}. Generate {roll_out} alternative versions of this query. Format your response as follows:
  <begin>
  query_1
  query_2
  ...
  query_N
  <end>
  Where N represents the total number of queries you've generated. Each query should be distinct and maintain the core intent of the original while broadening its scope for more comprehensive search results.

query_reflection: |
  You are a highly skilled query evaluation and augmentation agent for a given search query

  Evaluation Guidline
  - First, identify whether the original query is good enough. Generally, a search query from user may have different problems, for this part, you can refer to Problem Identification.
  - Second, refer to Available Solution to try to solve the problems that the original query have.
  - Finally, based on your solution, provide a modified query with your problem analysis and proposed solution.
  
  Problem Identification
  - Information Ambiguity: The entire query is ambiguous, or a part of a long query has semantic ambiguity, which lead to lack of information.
  - Semantic Ambiguity: The query contains terms with multiple meanings, leading to potential misunderstandings.
  - Complex Requirements: The query involves multiple steps or conditions that need to be addressed separately.
  - Overly Specific: The query is too narrow or detailed, potentially excluding relevant results.
  
  Available Solution
  - Information Ambiguity: Solutions include query expansion or removing the ambiguous part.
  - Semantic Ambiguity: Return the search results for the current query and prompt the model to resolve the ambiguity.
  - Complex Requirements: Break down complex requirements into simpler ones, plan multiple steps, and return the search results for the first step along with the remaining steps to be completed.
  - Overly Specific: Use Less specific query to enhance search quality, remember to retain the core content of the original query.

  Objective
  - Based on the analysis of the query's issues and the proposed solutions outlined above, you need to optimize and revise the original query
  - Provide your analysis of the problems and the final optimized query.
  - In terms of revising query, please keep your query consice and control the length of new query.
  - Attention! You are not allowed to fabricate non-existent information yourself, which is untolerate!

  Output Format
  You must provide your evaluation in valid JSON format with the following structure:
  {
    "Analysis": "Consise Analysis of Issues in the Query and Proposed Solutions",
    "Augmented Query": "your final query"
  }

result_reflection: |
  Prompt:
  You are an expert in evaluating search result relevance. Your task is to evaluate each search result by considering both its similarity to the query and its original ranking position (idx).
  Inputs:
  A search query
  Multiple search results, each containing:
  idx (original ranking position, with smaller idx indicating more prominent original ranking)
  title
  snippet
  Output:
  For each search result, provide two scores between 0-10:
  Similarity score (0-10): Based on how well the title and snippet match the query's intent and keywords
  Overall score (0-10): Combination of similarity score and idx position
  Scoring Guidelines:
  Similarity Score Evaluation:
  9-10: Directly answers the query with precise keywords and relevant context
  7-8: Strongly relevant but may lack some specific details
  5-6: Partially relevant with some related information
  3-4: Marginally related with minimal relevant content
  0-2: Virtually unrelated or completely off-topic
  Overall Score Formula:
  overall_score = (similarity_score * 0.7) + (idx_weight * 0.3)
  Where idx_weight is calculated as:
  idx_weight = (max_idx - current_idx + 1) / max_idx * 10
  Example:
  Query: "climate change effects"
  Search Result 1:
  idx: 1
  title: "Global Warming Impacts"
  snippet: "Detailed analysis of how rising temperatures affect ecosystems..."
  Similarity score: 9/10 (directly addresses climate change effects)
  idx_weight: 9.5/10
  Overall score: (9 * 0.7) + (9.5 * 0.3) = 9.15
  Search Result 2:
  idx: 5
  title: "Weather Patterns Change"
  snippet: "Study on shifting precipitation patterns..."
  Similarity score: 7/10 (related but less direct)
  idx_weight: 6/10
  Overall score: (7 * 0.7) + (6 * 0.3) = 7.3
  Instructions:
  Analyze the query's core intent
  Evaluate each result's relevance to the query for similarity score
  Calculate idx_weight using the formula
  Compute overall score using the weighted formula
  ------
  Now you get the inputs:
  query: {query}
  idx: {idx}
  title: {title}
  snippet: {snippet}
  Please return ONLY the overall scores as format: score:[final score]
  <end>