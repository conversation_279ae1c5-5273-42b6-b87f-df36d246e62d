{"all": ["astropy/io/ascii/tests/test_qdp.py::test_roundtrip[True]", "astropy/io/ascii/tests/test_qdp.py::test_get_tables_from_qdp_file", "astropy/io/ascii/tests/test_qdp.py::test_roundtrip[False]", "astropy/io/ascii/tests/test_qdp.py::test_read_example", "astropy/io/ascii/tests/test_qdp.py::test_roundtrip_example", "astropy/io/ascii/tests/test_qdp.py::test_roundtrip_example_comma", "astropy/io/ascii/tests/test_qdp.py::test_read_write_simple", "astropy/io/ascii/tests/test_qdp.py::test_read_write_simple_specify_name", "astropy/io/ascii/tests/test_qdp.py::test_get_lines_from_qdp"], "FAIL_TO_PASS": {"success": [], "failure": ["astropy/io/ascii/tests/test_qdp.py::test_roundtrip[True]"]}, "PASS_TO_PASS": {"success": ["astropy/io/ascii/tests/test_qdp.py::test_get_tables_from_qdp_file", "astropy/io/ascii/tests/test_qdp.py::test_roundtrip[False]", "astropy/io/ascii/tests/test_qdp.py::test_read_example", "astropy/io/ascii/tests/test_qdp.py::test_roundtrip_example", "astropy/io/ascii/tests/test_qdp.py::test_roundtrip_example_comma", "astropy/io/ascii/tests/test_qdp.py::test_read_write_simple", "astropy/io/ascii/tests/test_qdp.py::test_read_write_simple_specify_name", "astropy/io/ascii/tests/test_qdp.py::test_get_lines_from_qdp"], "failure": []}, "FAIL_TO_FAIL": {"success": [], "failure": []}, "PASS_TO_FAIL": {"success": [], "failure": []}}