---
sidebar_position: 1
---

# 💻 OpenHands

OpenHands 是一个**自主 AI 软件工程师**，能够执行复杂的工程任务，并在软件开发项目中积极与用户合作。
这个项目是完全开源的，所以你可以随意使用和修改它。

:::tip
在 [GitHub](https://github.com/All-Hands-AI/OpenHands) 上探索 OpenHands 的代码库或加入我们的社区！

<a href="https://github.com/All-Hands-AI/OpenHands/graphs/contributors">
  <img
    src="https://img.shields.io/github/contributors/All-Hands-AI/OpenHands?style=for-the-badge"
    alt="Contributors"
  />
</a>
<a href="https://github.com/All-Hands-AI/OpenHands/network/members">
  <img
    src="https://img.shields.io/github/forks/All-Hands-AI/OpenHands?style=for-the-badge"
    alt="Forks"
  />
</a>
<a href="https://github.com/All-Hands-AI/OpenHands/stargazers">
  <img
    src="https://img.shields.io/github/stars/All-Hands-AI/OpenHands?style=for-the-badge"
    alt="Stargazers"
  />
</a>
<a href="https://github.com/All-Hands-AI/OpenHands/issues">
  <img
    src="https://img.shields.io/github/issues/All-Hands-AI/OpenHands?style=for-the-badge"
    alt="Issues"
  />
</a>
<br></br>
<a href="https://github.com/All-Hands-AI/OpenHands/blob/main/LICENSE">
  <img
    src="https://img.shields.io/github/license/All-Hands-AI/OpenHands?style=for-the-badge"
    alt="MIT License"
  />
</a>
<br></br>
<a href="https://join.slack.com/t/openhands-ai/shared_invite/zt-2ngejmfw6-9gW4APWOC9XUp1n~SiQ6iw">
  <img
    src="https://img.shields.io/badge/Slack-Join%20Us-red?logo=slack&logoColor=white&style=for-the-badge"
    alt="Join our Slack community"
  />
</a>
<a href="https://discord.gg/ESHStjSjD4">
  <img
    src="https://img.shields.io/badge/Discord-Join%20Us-purple?logo=discord&logoColor=white&style=for-the-badge"
    alt="Join our Discord community"
  />
</a>
:::

## 🛠️ 入门指南

运行 OpenHands 最简单的方法是在 Docker 容器中。它在 Docker 的最新版本 `26.0.0` 上运行效果最佳。
你必须使用 Linux、Mac OS 或 Windows 上的 WSL。

要在 Docker 容器中启动 OpenHands，请在终端中运行以下命令：

:::warning
运行以下命令时，`./workspace` 中的文件可能会被修改或删除。
:::

```bash
WORKSPACE_BASE=$(pwd)/workspace
docker run -it \
    --pull=always \
    -e SANDBOX_USER_ID=$(id -u) \
    -e WORKSPACE_MOUNT_PATH=$WORKSPACE_BASE \
    -v $WORKSPACE_BASE:/opt/workspace_base \
    -v /var/run/docker.sock:/var/run/docker.sock \
    -p 3000:3000 \
    --add-host host.docker.internal:host-gateway \
    --name openhands-app-$(date +%Y%m%d%H%M%S) \
    ghcr.io/all-hands-ai/openhands:main
```

你会发现 OpenHands 在 [http://localhost:3000](http://localhost:3000) 运行，并可以访问 `./workspace`。要让 OpenHands 操作你的代码，请将代码放在 `./workspace` 中。

OpenHands 只会访问这个工作区文件夹。它在一个安全的 docker 沙盒中运行，不会影响你系统的其他部分。

:::tip
如果你想使用**（不稳定！）**最新版本，可以使用 `ghcr.io/all-hands-ai/openhands:main` 作为镜像（最后一行）。
:::

有关开发工作流程，请参阅 [Development.md](https://github.com/All-Hands-AI/OpenHands/blob/main/Development.md)。

遇到问题了吗？查看我们的 [故障排除指南](https://docs.all-hands.dev/modules/usage/troubleshooting)。

:::warning
OpenHands 目前正在开发中，但你已经可以运行 alpha 版本来查看端到端系统的运作情况。
:::

[contributors-shield]: https://img.shields.io/github/contributors/All-Hands-AI/OpenHands?style=for-the-badge
[contributors-url]: https://github.com/All-Hands-AI/OpenHands/graphs/contributors
[forks-shield]: https://img.shields.io/github/forks/All-Hands-AI/OpenHands?style=for-the-badge
[forks-url]: https://github.com/All-Hands-AI/OpenHands/network/members
[stars-shield]: https://img.shields.io/github/stars/All-Hands-AI/OpenHands?style=for-the-badge
[stars-url]: https://github.com/All-Hands-AI/OpenHands/stargazers
[issues-shield]: https://img.shields.io/github/issues/All-Hands-AI/OpenHands?style=for-the-badge
[issues-url]: https://github.com/All-Hands-AI/OpenHands/issues
[license-shield]: https://img.shields.io/github/license/All-Hands-AI/OpenHands?style=for-the-badge
[license-url]: https://github.com/All-Hands-AI/OpenHands/blob/main/LICENSE
