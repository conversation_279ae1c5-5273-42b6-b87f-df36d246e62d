#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识生成和复用系统
实现Gemini先训练生成知识，然后Qwen3利用这些知识的完整流程
"""

import json
import asyncio
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from datetime import datetime
import logging

from gemini_model_integration import get_gemini_client, generate_knowledge_with_gemini
from qwen3_model_integration import get_qwen3_client, process_with_qwen3_pattern

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class KnowledgePattern:
    """知识模式数据类"""
    pattern_id: str
    name: str
    description: str
    intent_type: str
    keywords: List[str]
    parameters: Dict[str, Any]
    examples: List[str]
    api_mapping: Dict[str, Any]
    confidence: float
    source_model: str  # "gemini" or "qwen3"
    created_at: datetime
    last_used: datetime
    usage_count: int
    success_count: int

@dataclass
class KnowledgeGenerationResult:
    """知识生成结果"""
    success: bool
    patterns: List[KnowledgePattern]
    generation_time: float
    source_query: str
    model_used: str
    error: Optional[str] = None

class KnowledgeGenerationSystem:
    """知识生成和复用系统"""
    
    def __init__(self, storage_dir: str = "knowledge_storage"):
        """
        初始化知识生成系统
        
        Args:
            storage_dir: 知识存储目录
        """
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        
        # 知识存储
        self.generated_patterns: Dict[str, KnowledgePattern] = {}
        self.pattern_index: Dict[str, List[str]] = {}  # 关键词到模式ID的索引
        
        # 统计信息
        self.total_generations = 0
        self.successful_generations = 0
        self.total_reuses = 0
        self.successful_reuses = 0
        
        # 加载现有知识
        self._load_existing_knowledge()
    
    async def generate_knowledge_with_gemini(self, query: str, context: Dict[str, Any] = None) -> KnowledgeGenerationResult:
        """
        使用Gemini生成知识模式
        
        Args:
            query: 触发生成的查询
            context: 上下文信息
            
        Returns:
            KnowledgeGenerationResult: 生成结果
        """
        start_time = asyncio.get_event_loop().time()
        self.total_generations += 1
        
        try:
            logger.info(f"使用Gemini生成知识模式: {query}")
            
            # 调用Gemini生成知识
            gemini_result = await generate_knowledge_with_gemini(query, context)
            
            if not gemini_result["success"]:
                return KnowledgeGenerationResult(
                    success=False,
                    patterns=[],
                    generation_time=asyncio.get_event_loop().time() - start_time,
                    source_query=query,
                    model_used="gemini",
                    error=gemini_result.get("error", "Gemini生成失败")
                )
            
            # 解析生成的知识模式
            patterns = self._parse_gemini_knowledge(
                gemini_result["knowledge_pattern"], 
                query
            )
            
            # 存储生成的模式
            for pattern in patterns:
                self._store_pattern(pattern)
            
            # 更新索引
            self._update_pattern_index(patterns)
            
            # 保存到文件
            await self._save_patterns_to_file()
            
            self.successful_generations += 1
            generation_time = asyncio.get_event_loop().time() - start_time
            
            logger.info(f"成功生成 {len(patterns)} 个知识模式，耗时 {generation_time:.2f}s")
            
            return KnowledgeGenerationResult(
                success=True,
                patterns=patterns,
                generation_time=generation_time,
                source_query=query,
                model_used="gemini"
            )
            
        except Exception as e:
            logger.error(f"Gemini知识生成失败: {e}")
            return KnowledgeGenerationResult(
                success=False,
                patterns=[],
                generation_time=asyncio.get_event_loop().time() - start_time,
                source_query=query,
                model_used="gemini",
                error=str(e)
            )
    
    async def reuse_knowledge_with_qwen3(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        使用Qwen3复用已生成的知识
        
        Args:
            query: 用户查询
            context: 上下文信息
            
        Returns:
            处理结果
        """
        start_time = asyncio.get_event_loop().time()
        self.total_reuses += 1
        
        try:
            # 查找匹配的知识模式
            matching_patterns = self._find_matching_patterns(query)
            
            if not matching_patterns:
                return {
                    "success": False,
                    "message": "未找到匹配的知识模式",
                    "suggestion": "建议使用Gemini生成新的知识模式"
                }
            
            # 选择最佳模式
            best_pattern = self._select_best_pattern(matching_patterns, query)
            
            logger.info(f"使用知识模式 {best_pattern.name} 处理查询: {query}")
            
            # 使用Qwen3处理
            # 转换pattern为可序列化的格式
            pattern_dict = asdict(best_pattern)
            pattern_dict["created_at"] = best_pattern.created_at.isoformat()
            pattern_dict["last_used"] = best_pattern.last_used.isoformat()

            qwen3_result = await process_with_qwen3_pattern(
                query,
                pattern_dict,
                context
            )
            
            if qwen3_result["success"]:
                # 更新模式使用统计
                best_pattern.usage_count += 1
                best_pattern.last_used = datetime.now()
                best_pattern.success_count += 1
                
                self.successful_reuses += 1
                
                # 保存更新的统计
                await self._save_patterns_to_file()
                
                processing_time = asyncio.get_event_loop().time() - start_time
                
                return {
                    "success": True,
                    "result": qwen3_result["result"],
                    "pattern_used": {
                        "id": best_pattern.pattern_id,
                        "name": best_pattern.name,
                        "confidence": best_pattern.confidence
                    },
                    "processing_time": processing_time,
                    "model_used": "qwen3",
                    "usage": qwen3_result["usage"]
                }
            else:
                # 处理失败，但不更新成功计数
                best_pattern.usage_count += 1
                best_pattern.last_used = datetime.now()
                
                return {
                    "success": False,
                    "error": qwen3_result.get("error", "Qwen3处理失败"),
                    "pattern_attempted": best_pattern.name,
                    "suggestion": "可能需要使用Gemini重新生成知识模式"
                }
                
        except Exception as e:
            logger.error(f"知识复用失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "知识复用过程中出现异常"
            }
    
    async def intelligent_query_processing(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        智能查询处理：先尝试复用，失败则生成新知识
        
        Args:
            query: 用户查询
            context: 上下文信息
            
        Returns:
            处理结果
        """
        logger.info(f"开始智能处理查询: {query}")
        
        # 1. 首先尝试使用Qwen3复用现有知识
        reuse_result = await self.reuse_knowledge_with_qwen3(query, context)
        
        if reuse_result["success"]:
            logger.info("成功使用现有知识模式处理查询")
            reuse_result["strategy"] = "knowledge_reuse"
            return reuse_result
        
        # 2. 如果复用失败，使用Gemini生成新知识
        logger.info("现有知识不足，使用Gemini生成新知识")
        
        generation_result = await self.generate_knowledge_with_gemini(query, context)
        
        if not generation_result.success:
            return {
                "success": False,
                "error": generation_result.error,
                "message": "知识生成失败",
                "strategy": "knowledge_generation_failed"
            }
        
        # 3. 使用新生成的知识处理查询
        if generation_result.patterns:
            new_pattern = generation_result.patterns[0]  # 使用第一个生成的模式
            
            # 转换新模式为可序列化的格式
            new_pattern_dict = asdict(new_pattern)
            new_pattern_dict["created_at"] = new_pattern.created_at.isoformat()
            new_pattern_dict["last_used"] = new_pattern.last_used.isoformat()

            qwen3_result = await process_with_qwen3_pattern(
                query,
                new_pattern_dict,
                context
            )
            
            if qwen3_result["success"]:
                # 更新新模式的使用统计
                new_pattern.usage_count += 1
                new_pattern.success_count += 1
                new_pattern.last_used = datetime.now()
                
                await self._save_patterns_to_file()
                
                return {
                    "success": True,
                    "result": qwen3_result["result"],
                    "strategy": "knowledge_generation_and_use",
                    "new_pattern": {
                        "id": new_pattern.pattern_id,
                        "name": new_pattern.name,
                        "confidence": new_pattern.confidence
                    },
                    "generation_time": generation_result.generation_time,
                    "processing_time": qwen3_result["response_time"],
                    "model_used": "gemini+qwen3"
                }
        
        return {
            "success": False,
            "message": "知识生成成功但应用失败",
            "strategy": "knowledge_generation_success_but_application_failed"
        }
    
    def _parse_gemini_knowledge(self, knowledge_data: Any, source_query: str) -> List[KnowledgePattern]:
        """解析Gemini生成的知识"""
        patterns = []
        
        try:
            if isinstance(knowledge_data, str):
                # 尝试解析JSON字符串
                if knowledge_data.strip().startswith('{'):
                    knowledge_data = json.loads(knowledge_data)
                else:
                    # 如果不是JSON，创建一个基本模式
                    knowledge_data = {
                        "intent_type": "general_query",
                        "description": "基于Gemini分析的通用模式",
                        "keywords": self._extract_keywords(source_query),
                        "parameters": {},
                        "examples": [source_query]
                    }
            
            if isinstance(knowledge_data, dict):
                pattern_id = self._generate_pattern_id(source_query)
                
                pattern = KnowledgePattern(
                    pattern_id=pattern_id,
                    name=knowledge_data.get("intent_type", "unknown_pattern"),
                    description=knowledge_data.get("description", "Gemini生成的知识模式"),
                    intent_type=knowledge_data.get("intent_type", "general_query"),
                    keywords=knowledge_data.get("keywords", []),
                    parameters=knowledge_data.get("parameters", {}),
                    examples=knowledge_data.get("examples", [source_query]),
                    api_mapping=knowledge_data.get("api_mapping", {}),
                    confidence=0.8,  # Gemini生成的模式默认置信度
                    source_model="gemini",
                    created_at=datetime.now(),
                    last_used=datetime.now(),
                    usage_count=0,
                    success_count=0
                )
                
                patterns.append(pattern)
            
        except Exception as e:
            logger.error(f"解析Gemini知识失败: {e}")
            # 创建一个基本的fallback模式
            pattern_id = self._generate_pattern_id(source_query)
            fallback_pattern = KnowledgePattern(
                pattern_id=pattern_id,
                name="fallback_pattern",
                description="解析失败时的备用模式",
                intent_type="general_query",
                keywords=self._extract_keywords(source_query),
                parameters={},
                examples=[source_query],
                api_mapping={},
                confidence=0.5,
                source_model="gemini",
                created_at=datetime.now(),
                last_used=datetime.now(),
                usage_count=0,
                success_count=0
            )
            patterns.append(fallback_pattern)
        
        return patterns
    
    def _find_matching_patterns(self, query: str) -> List[KnowledgePattern]:
        """查找匹配的知识模式"""
        matching_patterns = []
        query_keywords = self._extract_keywords(query)
        
        for pattern in self.generated_patterns.values():
            # 计算关键词匹配度
            pattern_keywords = set(pattern.keywords)
            query_keywords_set = set(query_keywords)
            
            if pattern_keywords and query_keywords_set:
                intersection = pattern_keywords.intersection(query_keywords_set)
                union = pattern_keywords.union(query_keywords_set)
                similarity = len(intersection) / len(union) if union else 0
                
                if similarity > 0.3:  # 相似度阈值
                    matching_patterns.append(pattern)
            
            # 检查示例匹配
            for example in pattern.examples:
                if self._calculate_text_similarity(query, example) > 0.7:
                    if pattern not in matching_patterns:
                        matching_patterns.append(pattern)
        
        return matching_patterns
    
    def _select_best_pattern(self, patterns: List[KnowledgePattern], query: str) -> KnowledgePattern:
        """选择最佳模式"""
        if not patterns:
            return None
        
        # 综合考虑置信度、使用次数、成功率等因素
        def score_pattern(pattern: KnowledgePattern) -> float:
            success_rate = pattern.success_count / pattern.usage_count if pattern.usage_count > 0 else 0.5
            usage_factor = min(pattern.usage_count / 10, 1.0)  # 使用次数因子，最大为1
            
            # 计算查询相似度
            similarity_scores = []
            for example in pattern.examples:
                similarity_scores.append(self._calculate_text_similarity(query, example))
            
            max_similarity = max(similarity_scores) if similarity_scores else 0.5
            
            # 综合评分
            score = (
                pattern.confidence * 0.3 +
                success_rate * 0.3 +
                usage_factor * 0.2 +
                max_similarity * 0.2
            )
            
            return score
        
        # 按评分排序，返回最高分的模式
        patterns.sort(key=score_pattern, reverse=True)
        return patterns[0]
    
    def _extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        import re
        
        # 简单的关键词提取
        keywords = []
        
        # 中文关键词
        chinese_words = re.findall(r'[\u4e00-\u9fff]+', text)
        keywords.extend(chinese_words)
        
        # 英文关键词
        english_words = re.findall(r'[a-zA-Z]+', text.lower())
        keywords.extend(english_words)
        
        # 数字
        numbers = re.findall(r'\d+', text)
        keywords.extend(numbers)
        
        return list(set(keywords))
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度"""
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union)
    
    def _generate_pattern_id(self, source_query: str) -> str:
        """生成模式ID"""
        import hashlib
        timestamp = datetime.now().isoformat()
        return hashlib.md5(f"{source_query}_{timestamp}".encode()).hexdigest()[:16]
    
    def _store_pattern(self, pattern: KnowledgePattern):
        """存储模式"""
        self.generated_patterns[pattern.pattern_id] = pattern
    
    def _update_pattern_index(self, patterns: List[KnowledgePattern]):
        """更新模式索引"""
        for pattern in patterns:
            for keyword in pattern.keywords:
                if keyword not in self.pattern_index:
                    self.pattern_index[keyword] = []
                if pattern.pattern_id not in self.pattern_index[keyword]:
                    self.pattern_index[keyword].append(pattern.pattern_id)
    
    async def _save_patterns_to_file(self):
        """保存模式到文件"""
        try:
            patterns_file = self.storage_dir / "generated_patterns.json"
            
            # 转换为可序列化的格式
            serializable_patterns = {}
            for pattern_id, pattern in self.generated_patterns.items():
                pattern_dict = asdict(pattern)
                pattern_dict["created_at"] = pattern.created_at.isoformat()
                pattern_dict["last_used"] = pattern.last_used.isoformat()
                serializable_patterns[pattern_id] = pattern_dict
            
            with open(patterns_file, 'w', encoding='utf-8') as f:
                json.dump(serializable_patterns, f, ensure_ascii=False, indent=2)
            
            # 保存索引
            index_file = self.storage_dir / "pattern_index.json"
            with open(index_file, 'w', encoding='utf-8') as f:
                json.dump(self.pattern_index, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"保存模式到文件失败: {e}")
    
    def _load_existing_knowledge(self):
        """加载现有知识"""
        try:
            patterns_file = self.storage_dir / "generated_patterns.json"
            if patterns_file.exists():
                with open(patterns_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for pattern_id, pattern_data in data.items():
                    # 转换日期字符串
                    pattern_data["created_at"] = datetime.fromisoformat(pattern_data["created_at"])
                    pattern_data["last_used"] = datetime.fromisoformat(pattern_data["last_used"])
                    
                    pattern = KnowledgePattern(**pattern_data)
                    self.generated_patterns[pattern_id] = pattern
            
            # 加载索引
            index_file = self.storage_dir / "pattern_index.json"
            if index_file.exists():
                with open(index_file, 'r', encoding='utf-8') as f:
                    self.pattern_index = json.load(f)
            
            logger.info(f"加载了 {len(self.generated_patterns)} 个现有知识模式")
            
        except Exception as e:
            logger.error(f"加载现有知识失败: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        generation_success_rate = self.successful_generations / self.total_generations if self.total_generations > 0 else 0
        reuse_success_rate = self.successful_reuses / self.total_reuses if self.total_reuses > 0 else 0
        
        return {
            "total_patterns": len(self.generated_patterns),
            "total_generations": self.total_generations,
            "successful_generations": self.successful_generations,
            "generation_success_rate": generation_success_rate,
            "total_reuses": self.total_reuses,
            "successful_reuses": self.successful_reuses,
            "reuse_success_rate": reuse_success_rate,
            "index_size": len(self.pattern_index)
        }
