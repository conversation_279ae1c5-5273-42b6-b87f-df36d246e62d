# 运行 OpenHands

## 系统要求

* Docker 版本 26.0.0+ 或 Docker Desktop 4.31.0+。
* 你必须使用 Linux 或 Mac OS。
  * 如果你使用的是 Windows，你必须使用 [WSL](https://learn.microsoft.com/en-us/windows/wsl/install)。

## 启动应用

在 Docker 中运行 OpenHands 是最简单的方式。

```bash
docker pull docker.all-hands.dev/all-hands-ai/runtime:0.31-nikolaik

docker run -it --rm --pull=always \
    -e SANDBOX_RUNTIME_CONTAINER_IMAGE=docker.all-hands.dev/all-hands-ai/runtime:0.31-nikolaik \
    -e LOG_ALL_EVENTS=true \
    -v /var/run/docker.sock:/var/run/docker.sock \
    -p 3000:3000 \
    --add-host host.docker.internal:host-gateway \
    --name openhands-app \
    docker.all-hands.dev/all-hands-ai/openhands:0.31
```

你也可以在可脚本化的[无头模式](https://docs.all-hands.dev/modules/usage/how-to/headless-mode)下运行 OpenHands，作为[交互式 CLI](https://docs.all-hands.dev/modules/usage/how-to/cli-mode)，或使用 [OpenHands GitHub Action](https://docs.all-hands.dev/modules/usage/how-to/github-action)。

## 设置

运行上述命令后，你可以在 [http://localhost:3000](http://localhost:3000) 找到正在运行的 OpenHands。

启动 OpenHands 后，你会看到一个设置模态框。你**必须**选择一个 `LLM Provider` 和 `LLM Model`，并输入相应的 `API Key`。
这些设置可以随时通过选择 UI 中的 `Settings` 按钮（齿轮图标）进行更改。

如果所需的 `LLM Model` 不在列表中，你可以切换 `Advanced Options`，并在 `Custom Model` 文本框中使用正确的前缀手动输入。
`Advanced Options` 还允许你在需要时指定 `Base URL`。

<div style={{ display: 'flex', justifyContent: 'center', gap: '20px' }}>
  <img src="/img/settings-screenshot.png" alt="settings-modal" width="340" />
  <img src="/img/settings-advanced.png" alt="settings-modal" width="335" />
</div>

## 版本

上述命令拉取最新的 OpenHands 稳定版本。你还有其他选择：
- 对于特定版本，使用 `docker.all-hands.dev/all-hands-ai/openhands:$VERSION`，将 $VERSION 替换为版本号。
- 我们使用语义化版本，并发布主要版本、次要版本和补丁标签。因此，`0.9` 将自动指向最新的 `0.9.x` 版本，而 `0` 将指向最新的 `0.x.x` 版本。
- 对于最新的开发版本，你可以使用 `docker.all-hands.dev/all-hands-ai/openhands:main`。此版本不稳定，仅建议用于测试或开发目的。

你可以根据稳定性要求和所需功能选择最适合你需求的标签。

有关开发工作流程，请参阅 [Development.md](https://github.com/All-Hands-AI/OpenHands/blob/main/Development.md)。

遇到问题了吗？查看我们的[故障排除指南](https://docs.all-hands.dev/modules/usage/troubleshooting)。
