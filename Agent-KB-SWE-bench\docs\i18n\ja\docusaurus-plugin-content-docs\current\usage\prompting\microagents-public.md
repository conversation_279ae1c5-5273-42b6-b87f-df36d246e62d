# パブリックマイクロエージェント

## 概要

パブリックマイクロエージェントは、OpenHandsのすべてのユーザーが利用できる一般的なガイドラインを提供します。これらは、特定のキーワードによってトリガーされ、特定のドメインやタスクに関する専門知識を提供します。

## 現在のパブリックマイクロエージェント

### GitHubエージェント

GitHubエージェントは、GitHubリポジトリとの対話を支援します。主な機能：

* リポジトリのクローン
* ブランチの作成と切り替え
* コミットとプッシュ
* プルリクエストの作成

### NPMエージェント

NPMエージェントは、Node.jsプロジェクトの管理を支援します：

* パッケージのインストールと更新
* 依存関係の管理
* スクリプトの実行
* バージョン管理

## パブリックマイクロエージェントの貢献

### パブリックマイクロエージェントのベストプラクティス

1. **明確なトリガー**：エージェントをトリガーするキーワードを明確に定義します
2. **集中的な範囲**：1つのドメインまたはタスクに焦点を当てます
3. **明確なガイドライン**：エージェントの責任と制限を明確に説明します
4. **実用的な例**：一般的なユースケースの例を含めます

### パブリックマイクロエージェントを貢献するステップ

1. [マイクロエージェントのフォーマット](./microagents-overview#microagent-format)を確認します
2. 新しいマイクロエージェントファイルを作成します
3. ガイドラインとベストプラクティスに従っていることを確認します
4. プルリクエストを作成します

### パブリックマイクロエージェントの実装例

```
---
name: Dockerエージェント
type: public
version: 1.0
agent: CodeActAgent
triggers:
- docker
- container
---

あなたはDockerコンテナの管理とDockerfileの作成を担当します。

主な責任：
1. Dockerfileの作成と修正
2. コンテナのライフサイクル管理
3. Docker Compose設定の処理

ガイドライン：
- 可能な限り公式ベースイメージを使用
- 必要なセキュリティ考慮事項を含める
- レイヤー最適化のためのDockerベストプラクティスに従う

例：
1. Dockerfileの作成：
   FROM node:18-alpine
   WORKDIR /app
   COPY package*.json ./
   RUN npm install
   COPY . .
   CMD ["npm", "start"]

2. Docker Composeの使用：
   version: '3'
   services:
     web:
       build: .
       ports:
         - "3000:3000"

忘れないこと：
- Dockerfileの構文を検証
- セキュリティ脆弱性をチェック
- ビルド時間とイメージサイズを最適化
```

より多くの例については、[現在のパブリックマイクロエージェント](https://github.com/All-Hands-AI/OpenHands/tree/main/microagents/knowledge)をご覧ください。
