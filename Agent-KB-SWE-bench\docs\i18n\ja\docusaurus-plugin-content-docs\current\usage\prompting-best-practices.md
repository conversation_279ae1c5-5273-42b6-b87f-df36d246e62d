# プロンプトのベストプラクティス

OpenHands AIソフトウェア開発者と連携する際、明確で効果的なプロンプトを提供することが非常に重要です。このガイドでは、最も正確で有用な応答を生み出すプロンプトを作成するためのベストプラクティスについて説明します。

## 良いプロンプトの特徴

良いプロンプトは以下のような特徴があります。

1. **具体的** : どの機能を追加するか、どのバグを修正する必要があるかを正確に説明します。
2. **場所を特定** : 可能な場合は、コードベース内のどの場所を変更する必要があるかを説明します。
3. **適切なサイズ** : 1つの機能に対応したサイズで、通常は100行以内のコードに収まります。

## 例

### 良いプロンプトの例

1. "`utils/math_operations.py` に `calculate_average` 関数を追加してください。この関数は数値のリストを入力として受け取り、その平均値を返します。"

2. "`frontend/src/components/UserProfile.tsx` の42行目で発生している TypeError を修正してください。このエラーは、undefined のプロパティにアクセスしようとしていることを示唆しています。"

3. "サインアップフォームの email フィールドに入力検証を実装してください。`frontend/src/components/RegistrationForm.tsx` を更新し、送信前にメールアドレスが有効な形式であるかどうかを確認します。"

### 悪いプロンプトの例

1. "コードを改善してください。" (あまりにも曖昧で具体性に欠ける)

2. "バックエンド全体を別のフレームワークを使用するように書き換えてください。" (適切なサイズではない)

3. "ユーザー認証のどこかにバグがあります。それを見つけて修正できますか?" (具体性と場所情報が不足している)

## 効果的なプロンプトのヒント

1. 望ましい結果や解決すべき問題について、できるだけ具体的に説明してください。
2. 関連するファイルパスや行番号など、コンテキストを提供してください。
3. 大きなタスクは、より小さく管理しやすいプロンプトに分割してください。
4. 関連するエラーメッセージやログをすべて含めてください。
5. コンテキストから明らかでない場合は、プログラミング言語やフレームワークを指定してください。

プロンプトが具体的で情報量が多いほど、AIはOpenHandsソフトウェアの開発や修正をより効果的に支援できることを忘れないでください。

役立つプロンプトの他の例については、[OpenHands入門](./getting-started)を参照してください。
