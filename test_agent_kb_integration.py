#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Agent-KB统计服务器集成测试
验证配置管理、微代理、知识库、内存管理和事件驱动架构的功能
"""

import json
import os
import sys
import time
import unittest
from unittest.mock import Mock, patch
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入被测试的模块
from stats_mcp_server_official import (
    ConfigManager, StatsServerConfig,
    MicroAgentManager, KnowledgeBase, MemoryManager,
    EventStream, EventType, Event, EventHandler
)

class TestConfigManager(unittest.TestCase):
    """测试配置管理器"""
    
    def setUp(self):
        """测试前准备"""
        self.config_manager = ConfigManager()
    
    def test_default_config(self):
        """测试默认配置"""
        config = self.config_manager.get_config()
        self.assertIsInstance(config, StatsServerConfig)
        self.assertEqual(config.api_version, "6.1")
        self.assertEqual(config.default_topcount, 1000)
        self.assertFalse(config.ssl_verify)
    
    def test_env_config_loading(self):
        """测试环境变量配置加载"""
        with patch.dict(os.environ, {
            'STATS_API_URL': 'https://test.example.com/',
            'STATS_TIMEOUT': '60',
            'STATS_SSL_VERIFY': 'true'
        }):
            config_manager = ConfigManager()
            config = config_manager.get_config()
            
            self.assertEqual(config.api_url, 'https://test.example.com/')
            self.assertEqual(config.timeout, 60)
            self.assertTrue(config.ssl_verify)
    
    def test_config_validation(self):
        """测试配置验证"""
        # 测试无效配置
        self.config_manager.config.api_url = ""
        self.config_manager.config.api_username = ""
        
        errors = self.config_manager.validate_config()
        self.assertIn("API URL is required", errors)
        self.assertIn("API username is required", errors)

class TestMicroAgentManager(unittest.TestCase):
    """测试微代理管理器"""
    
    def setUp(self):
        """测试前准备"""
        self.manager = MicroAgentManager()
    
    def test_agent_initialization(self):
        """测试代理初始化"""
        self.assertIn("stats_knowledge", self.manager.agents)
        self.assertIn("api_documentation", self.manager.agents)
        self.assertIn("error_handling", self.manager.agents)
        self.assertIn("query_optimization", self.manager.agents)
    
    def test_trigger_matching(self):
        """测试触发器匹配"""
        # 测试统计知识代理
        agents = self.manager.find_relevant_agents("我想查询统计表")
        self.assertTrue(len(agents) > 0)
        self.assertTrue(any(agent["name"] == "stats_knowledge" for agent in agents))
        
        # 测试API文档代理
        agents = self.manager.find_relevant_agents("API接口怎么使用")
        self.assertTrue(any(agent["name"] == "api_documentation" for agent in agents))
        
        # 测试错误处理代理
        agents = self.manager.find_relevant_agents("出现错误了")
        self.assertTrue(any(agent["name"] == "error_handling" for agent in agents))
    
    def test_knowledge_retrieval(self):
        """测试知识获取"""
        knowledge = self.manager.get_agent_knowledge("stats_knowledge")
        self.assertIsNotNone(knowledge)
        self.assertIn("统计API使用指南", knowledge)

class TestKnowledgeBase(unittest.TestCase):
    """测试知识库"""
    
    def setUp(self):
        """测试前准备"""
        self.kb = KnowledgeBase()
    
    def test_table_knowledge(self):
        """测试表知识"""
        tcp_info = self.kb.get_table_knowledge("tcp_flow")
        self.assertIsNotNone(tcp_info)
        self.assertEqual(tcp_info["description"], "TCP流量统计表")
        self.assertIn("server_ip_addr", tcp_info["common_fields"])
    
    def test_error_solution(self):
        """测试错误解决方案"""
        solution = self.kb.get_error_solution("5")
        self.assertIsNotNone(solution)
        self.assertEqual(solution["message"], "需要登录")
        self.assertIn("setup_api_connection", solution["solution"])
    
    def test_best_practices(self):
        """测试最佳实践"""
        practices = self.kb.get_best_practices("query_optimization")
        self.assertTrue(len(practices) > 0)
        self.assertTrue(any("时间范围" in practice for practice in practices))
    
    def test_query_history(self):
        """测试查询历史"""
        # 添加测试查询
        test_query = {
            "table": "tcp_flow",
            "fields": ["server_ip_addr", "total_byte"],
            "success": True
        }
        self.kb.add_query_history(test_query)
        
        history = self.kb.get_query_history(1)
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0]["table"], "tcp_flow")

class TestMemoryManager(unittest.TestCase):
    """测试内存管理器"""
    
    def setUp(self):
        """测试前准备"""
        self.kb = KnowledgeBase()
        self.memory = MemoryManager(self.kb)
    
    def test_working_memory(self):
        """测试工作内存"""
        self.memory.store_working_memory("test_key", "test_value")
        value = self.memory.get_working_memory("test_key")
        self.assertEqual(value, "test_value")
    
    def test_episodic_memory(self):
        """测试情景记忆"""
        event = {"type": "test_event", "data": "test_data"}
        self.memory.add_episodic_memory(event)
        
        episodes = self.memory.get_recent_episodes(1)
        self.assertEqual(len(episodes), 1)
        self.assertEqual(episodes[0]["type"], "test_event")
    
    def test_similar_queries(self):
        """测试相似查询回忆"""
        # 添加历史查询
        self.kb.add_query_history({
            "table": "tcp_flow",
            "fields": ["server_ip_addr", "total_byte"],
            "success": True
        })
        
        # 查找相似查询
        similar = self.memory.recall_similar_queries("tcp_flow", ["server_ip_addr", "client_ip_addr"])
        self.assertTrue(len(similar) >= 0)  # 可能找到相似查询

class TestEventSystem(unittest.TestCase):
    """测试事件系统"""
    
    def setUp(self):
        """测试前准备"""
        self.event_stream = EventStream()
        self.kb = KnowledgeBase()
        self.memory = MemoryManager(self.kb)
        self.handler = EventHandler(self.kb, self.memory)
        
        # 注册事件处理器
        self.event_stream.subscribe(EventType.API_CONNECTION, self.handler.handle_api_connection)
        self.event_stream.subscribe(EventType.QUERY_START, self.handler.handle_query_start)
        self.event_stream.subscribe(EventType.QUERY_SUCCESS, self.handler.handle_query_success)
        self.event_stream.subscribe(EventType.QUERY_ERROR, self.handler.handle_query_error)
    
    def test_event_emission(self):
        """测试事件发布"""
        event = Event(EventType.API_CONNECTION, {"success": True, "url": "test"})
        self.event_stream.emit(event)
        
        # 检查事件历史
        history = self.event_stream.get_recent_events(1)
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0].event_type, EventType.API_CONNECTION)
    
    def test_api_connection_handling(self):
        """测试API连接事件处理"""
        event = Event(EventType.API_CONNECTION, {
            "success": True,
            "url": "https://test.com",
            "username": "test_user"
        })
        self.event_stream.emit(event)
        
        # 检查工作内存是否更新
        connection_status = self.memory.get_working_memory("api_connection_status")
        self.assertIsNotNone(connection_status)
        self.assertTrue(connection_status["success"])
    
    def test_query_event_handling(self):
        """测试查询事件处理"""
        # 查询开始事件
        start_event = Event(EventType.QUERY_START, {
            "table": "tcp_flow",
            "fields": ["server_ip_addr"],
            "begintime": "2024-01-01 00:00:00",
            "endtime": "2024-01-01 23:59:59"
        })
        self.event_stream.emit(start_event)
        
        # 检查当前查询是否存储
        current_query = self.memory.get_working_memory("current_query")
        self.assertIsNotNone(current_query)
        self.assertEqual(current_query["table"], "tcp_flow")
        
        # 查询成功事件
        success_event = Event(EventType.QUERY_SUCCESS, {
            "table": "tcp_flow",
            "data": [{"server_ip_addr": "***********", "total_byte": 1000}],
            "execution_time": 0.5
        })
        self.event_stream.emit(success_event)
        
        # 检查成功统计
        success_count = self.memory.get_working_memory("success_count")
        self.assertEqual(success_count, 1)

def run_integration_test():
    """运行集成测试"""
    print("=" * 60)
    print("Agent-KB统计服务器集成测试")
    print("=" * 60)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_suite.addTest(unittest.makeSuite(TestConfigManager))
    test_suite.addTest(unittest.makeSuite(TestMicroAgentManager))
    test_suite.addTest(unittest.makeSuite(TestKnowledgeBase))
    test_suite.addTest(unittest.makeSuite(TestMemoryManager))
    test_suite.addTest(unittest.makeSuite(TestEventSystem))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print(f"运行测试: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    print("=" * 60)
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_integration_test()
    sys.exit(0 if success else 1)
