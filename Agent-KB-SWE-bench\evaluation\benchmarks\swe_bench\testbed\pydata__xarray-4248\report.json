{"all": ["xarray/tests/test_formatting.py::test_inline_variable_array_repr_custom_repr", "xarray/tests/test_formatting.py::TestFormatting::test_get_indexer_at_least_n_items", "xarray/tests/test_formatting.py::TestFormatting::test_first_n_items", "xarray/tests/test_formatting.py::TestFormatting::test_last_n_items", "xarray/tests/test_formatting.py::TestFormatting::test_last_item", "xarray/tests/test_formatting.py::TestFormatting::test_format_item", "xarray/tests/test_formatting.py::TestFormatting::test_format_items", "xarray/tests/test_formatting.py::TestFormatting::test_format_array_flat", "xarray/tests/test_formatting.py::TestFormatting::test_pretty_print", "xarray/tests/test_formatting.py::TestFormatting::test_maybe_truncate", "xarray/tests/test_formatting.py::TestFormatting::test_format_timestamp_out_of_bounds", "xarray/tests/test_formatting.py::TestFormatting::test_attribute_repr", "xarray/tests/test_formatting.py::TestFormatting::test_diff_attrs_repr_with_array", "xarray/tests/test_formatting.py::TestFormatting::test_array_repr", "xarray/tests/test_formatting.py::test_set_numpy_options", "xarray/tests/test_formatting.py::test_short_numpy_repr", "xarray/tests/test_formatting.py::test_large_array_repr_length", "xarray/tests/test_formatting.py::TestFormatting::test_diff_array_repr", "xarray/tests/test_formatting.py::TestFormatting::test_diff_dataset_repr"], "FAIL_TO_PASS": {"success": [], "failure": ["xarray/tests/test_formatting.py::test_inline_variable_array_repr_custom_repr"]}, "PASS_TO_PASS": {"success": ["xarray/tests/test_formatting.py::TestFormatting::test_get_indexer_at_least_n_items", "xarray/tests/test_formatting.py::TestFormatting::test_first_n_items", "xarray/tests/test_formatting.py::TestFormatting::test_last_n_items", "xarray/tests/test_formatting.py::TestFormatting::test_last_item", "xarray/tests/test_formatting.py::TestFormatting::test_format_item", "xarray/tests/test_formatting.py::TestFormatting::test_format_items", "xarray/tests/test_formatting.py::TestFormatting::test_format_array_flat", "xarray/tests/test_formatting.py::TestFormatting::test_pretty_print", "xarray/tests/test_formatting.py::TestFormatting::test_maybe_truncate", "xarray/tests/test_formatting.py::TestFormatting::test_format_timestamp_out_of_bounds", "xarray/tests/test_formatting.py::TestFormatting::test_attribute_repr", "xarray/tests/test_formatting.py::TestFormatting::test_diff_attrs_repr_with_array", "xarray/tests/test_formatting.py::TestFormatting::test_array_repr", "xarray/tests/test_formatting.py::test_set_numpy_options", "xarray/tests/test_formatting.py::test_short_numpy_repr", "xarray/tests/test_formatting.py::test_large_array_repr_length"], "failure": ["xarray/tests/test_formatting.py::TestFormatting::test_diff_array_repr", "xarray/tests/test_formatting.py::TestFormatting::test_diff_dataset_repr"]}, "FAIL_TO_FAIL": {"success": [], "failure": []}, "PASS_TO_FAIL": {"success": [], "failure": []}}