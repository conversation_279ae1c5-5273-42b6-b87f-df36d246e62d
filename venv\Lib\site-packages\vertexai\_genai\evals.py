# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# Code generated by the Google Gen AI SDK generator DO NOT EDIT.

import json
import logging
from typing import Any, Callable, Optional, Union
from urllib.parse import urlencode

from google.genai import _api_module
from google.genai import _common
from google.genai import types as genai_types
from google.genai._common import get_value_by_path as getv
from google.genai._common import set_value_by_path as setv
import pandas as pd

from . import _evals_common
from . import _evals_utils
from . import types


logger = logging.getLogger("vertexai_genai.evals")


def _BleuInstance_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["prediction"]) is not None:
        setv(to_object, ["prediction"], getv(from_object, ["prediction"]))

    if getv(from_object, ["reference"]) is not None:
        setv(to_object, ["reference"], getv(from_object, ["reference"]))

    return to_object


def _BleuSpec_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["use_effective_order"]) is not None:
        setv(
            to_object,
            ["useEffectiveOrder"],
            getv(from_object, ["use_effective_order"]),
        )

    return to_object


def _BleuInput_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["instances"]) is not None:
        setv(
            to_object,
            ["instances"],
            [
                _BleuInstance_to_vertex(item, to_object)
                for item in getv(from_object, ["instances"])
            ],
        )

    if getv(from_object, ["metric_spec"]) is not None:
        setv(
            to_object,
            ["metricSpec"],
            _BleuSpec_to_vertex(getv(from_object, ["metric_spec"]), to_object),
        )

    return to_object


def _ExactMatchInstance_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["prediction"]) is not None:
        setv(to_object, ["prediction"], getv(from_object, ["prediction"]))

    if getv(from_object, ["reference"]) is not None:
        setv(to_object, ["reference"], getv(from_object, ["reference"]))

    return to_object


def _ExactMatchSpec_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}

    return to_object


def _ExactMatchInput_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["instances"]) is not None:
        setv(
            to_object,
            ["instances"],
            [
                _ExactMatchInstance_to_vertex(item, to_object)
                for item in getv(from_object, ["instances"])
            ],
        )

    if getv(from_object, ["metric_spec"]) is not None:
        setv(
            to_object,
            ["metricSpec"],
            _ExactMatchSpec_to_vertex(getv(from_object, ["metric_spec"]), to_object),
        )

    return to_object


def _RougeInstance_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["prediction"]) is not None:
        setv(to_object, ["prediction"], getv(from_object, ["prediction"]))

    if getv(from_object, ["reference"]) is not None:
        setv(to_object, ["reference"], getv(from_object, ["reference"]))

    return to_object


def _RougeSpec_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["rouge_type"]) is not None:
        setv(to_object, ["rougeType"], getv(from_object, ["rouge_type"]))

    if getv(from_object, ["split_summaries"]) is not None:
        setv(
            to_object,
            ["splitSummaries"],
            getv(from_object, ["split_summaries"]),
        )

    if getv(from_object, ["use_stemmer"]) is not None:
        setv(to_object, ["useStemmer"], getv(from_object, ["use_stemmer"]))

    return to_object


def _RougeInput_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["instances"]) is not None:
        setv(
            to_object,
            ["instances"],
            [
                _RougeInstance_to_vertex(item, to_object)
                for item in getv(from_object, ["instances"])
            ],
        )

    if getv(from_object, ["metric_spec"]) is not None:
        setv(
            to_object,
            ["metricSpec"],
            _RougeSpec_to_vertex(getv(from_object, ["metric_spec"]), to_object),
        )

    return to_object


def _PointwiseMetricInstance_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["json_instance"]) is not None:
        setv(to_object, ["jsonInstance"], getv(from_object, ["json_instance"]))

    return to_object


def _PointwiseMetricSpec_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["metric_prompt_template"]) is not None:
        setv(
            to_object,
            ["metricPromptTemplate"],
            getv(from_object, ["metric_prompt_template"]),
        )

    if getv(from_object, ["custom_output_format_config"]) is not None:
        setv(
            to_object,
            ["customOutputFormatConfig"],
            getv(from_object, ["custom_output_format_config"]),
        )

    if getv(from_object, ["system_instruction"]) is not None:
        setv(
            to_object,
            ["systemInstruction"],
            getv(from_object, ["system_instruction"]),
        )

    return to_object


def _PointwiseMetricInput_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["instance"]) is not None:
        setv(
            to_object,
            ["instance"],
            _PointwiseMetricInstance_to_vertex(
                getv(from_object, ["instance"]), to_object
            ),
        )

    if getv(from_object, ["metric_spec"]) is not None:
        setv(
            to_object,
            ["metricSpec"],
            _PointwiseMetricSpec_to_vertex(
                getv(from_object, ["metric_spec"]), to_object
            ),
        )

    return to_object


def _PairwiseMetricInstance_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}

    if getv(from_object, ["json_instance"]) is not None:
        setv(to_object, ["jsonInstance"], getv(from_object, ["json_instance"]))

    return to_object


def _PairwiseMetricSpec_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["metric_prompt_template"]) is not None:
        setv(
            to_object,
            ["metricPromptTemplate"],
            getv(from_object, ["metric_prompt_template"]),
        )

    if getv(from_object, ["baseline_response_field_name"]) is not None:
        setv(
            to_object,
            ["baselineResponseFieldName"],
            getv(from_object, ["baseline_response_field_name"]),
        )

    if getv(from_object, ["candidate_response_field_name"]) is not None:
        setv(
            to_object,
            ["candidateResponseFieldName"],
            getv(from_object, ["candidate_response_field_name"]),
        )

    if getv(from_object, ["custom_output_format_config"]) is not None:
        setv(
            to_object,
            ["customOutputFormatConfig"],
            getv(from_object, ["custom_output_format_config"]),
        )

    if getv(from_object, ["system_instruction"]) is not None:
        setv(
            to_object,
            ["systemInstruction"],
            getv(from_object, ["system_instruction"]),
        )

    return to_object


def _PairwiseMetricInput_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["instance"]) is not None:
        setv(
            to_object,
            ["instance"],
            _PairwiseMetricInstance_to_vertex(
                getv(from_object, ["instance"]), to_object
            ),
        )

    if getv(from_object, ["metric_spec"]) is not None:
        setv(
            to_object,
            ["metricSpec"],
            _PairwiseMetricSpec_to_vertex(
                getv(from_object, ["metric_spec"]), to_object
            ),
        )

    return to_object


def _ToolCallValidInstance_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["prediction"]) is not None:
        setv(to_object, ["prediction"], getv(from_object, ["prediction"]))

    if getv(from_object, ["reference"]) is not None:
        setv(to_object, ["reference"], getv(from_object, ["reference"]))

    return to_object


def _ToolCallValidSpec_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}

    return to_object


def _ToolCallValidInput_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["instances"]) is not None:
        setv(
            to_object,
            ["instances"],
            [
                _ToolCallValidInstance_to_vertex(item, to_object)
                for item in getv(from_object, ["instances"])
            ],
        )

    if getv(from_object, ["metric_spec"]) is not None:
        setv(
            to_object,
            ["metricSpec"],
            _ToolCallValidSpec_to_vertex(getv(from_object, ["metric_spec"]), to_object),
        )

    return to_object


def _ToolNameMatchInstance_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["prediction"]) is not None:
        setv(to_object, ["prediction"], getv(from_object, ["prediction"]))

    if getv(from_object, ["reference"]) is not None:
        setv(to_object, ["reference"], getv(from_object, ["reference"]))

    return to_object


def _ToolNameMatchSpec_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}

    return to_object


def _ToolNameMatchInput_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["instances"]) is not None:
        setv(
            to_object,
            ["instances"],
            [
                _ToolNameMatchInstance_to_vertex(item, to_object)
                for item in getv(from_object, ["instances"])
            ],
        )

    if getv(from_object, ["metric_spec"]) is not None:
        setv(
            to_object,
            ["metricSpec"],
            _ToolNameMatchSpec_to_vertex(getv(from_object, ["metric_spec"]), to_object),
        )

    return to_object


def _ToolParameterKeyMatchInstance_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["prediction"]) is not None:
        setv(to_object, ["prediction"], getv(from_object, ["prediction"]))

    if getv(from_object, ["reference"]) is not None:
        setv(to_object, ["reference"], getv(from_object, ["reference"]))

    return to_object


def _ToolParameterKeyMatchSpec_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}

    return to_object


def _ToolParameterKeyMatchInput_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["instances"]) is not None:
        setv(
            to_object,
            ["instances"],
            [
                _ToolParameterKeyMatchInstance_to_vertex(item, to_object)
                for item in getv(from_object, ["instances"])
            ],
        )

    if getv(from_object, ["metric_spec"]) is not None:
        setv(
            to_object,
            ["metricSpec"],
            _ToolParameterKeyMatchSpec_to_vertex(
                getv(from_object, ["metric_spec"]), to_object
            ),
        )

    return to_object


def _ToolParameterKVMatchInstance_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["prediction"]) is not None:
        setv(to_object, ["prediction"], getv(from_object, ["prediction"]))

    if getv(from_object, ["reference"]) is not None:
        setv(to_object, ["reference"], getv(from_object, ["reference"]))

    return to_object


def _ToolParameterKVMatchSpec_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["use_strict_string_match"]) is not None:
        setv(
            to_object,
            ["useStrictStringMatch"],
            getv(from_object, ["use_strict_string_match"]),
        )

    return to_object


def _ToolParameterKVMatchInput_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["instances"]) is not None:
        setv(
            to_object,
            ["instances"],
            [
                _ToolParameterKVMatchInstance_to_vertex(item, to_object)
                for item in getv(from_object, ["instances"])
            ],
        )

    if getv(from_object, ["metric_spec"]) is not None:
        setv(
            to_object,
            ["metricSpec"],
            _ToolParameterKVMatchSpec_to_vertex(
                getv(from_object, ["metric_spec"]), to_object
            ),
        )

    return to_object


def _AutoraterConfig_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["sampling_count"]) is not None:
        setv(to_object, ["samplingCount"], getv(from_object, ["sampling_count"]))

    if getv(from_object, ["flip_enabled"]) is not None:
        setv(to_object, ["flipEnabled"], getv(from_object, ["flip_enabled"]))

    if getv(from_object, ["autorater_model"]) is not None:
        setv(
            to_object,
            ["autoraterModel"],
            getv(from_object, ["autorater_model"]),
        )

    return to_object


def _EvaluateInstancesRequestParameters_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["bleu_input"]) is not None:
        setv(
            to_object,
            ["bleuInput"],
            _BleuInput_to_vertex(getv(from_object, ["bleu_input"]), to_object),
        )

    if getv(from_object, ["exact_match_input"]) is not None:
        setv(
            to_object,
            ["exactMatchInput"],
            _ExactMatchInput_to_vertex(
                getv(from_object, ["exact_match_input"]), to_object
            ),
        )

    if getv(from_object, ["rouge_input"]) is not None:
        setv(
            to_object,
            ["rougeInput"],
            _RougeInput_to_vertex(getv(from_object, ["rouge_input"]), to_object),
        )

    if getv(from_object, ["pointwise_metric_input"]) is not None:
        setv(
            to_object,
            ["pointwiseMetricInput"],
            _PointwiseMetricInput_to_vertex(
                getv(from_object, ["pointwise_metric_input"]), to_object
            ),
        )

    if getv(from_object, ["pairwise_metric_input"]) is not None:
        setv(
            to_object,
            ["pairwiseMetricInput"],
            _PairwiseMetricInput_to_vertex(
                getv(from_object, ["pairwise_metric_input"]), to_object
            ),
        )

    if getv(from_object, ["tool_call_valid_input"]) is not None:
        setv(
            to_object,
            ["toolCallValidInput"],
            _ToolCallValidInput_to_vertex(
                getv(from_object, ["tool_call_valid_input"]), to_object
            ),
        )

    if getv(from_object, ["tool_name_match_input"]) is not None:
        setv(
            to_object,
            ["toolNameMatchInput"],
            _ToolNameMatchInput_to_vertex(
                getv(from_object, ["tool_name_match_input"]), to_object
            ),
        )

    if getv(from_object, ["tool_parameter_key_match_input"]) is not None:
        setv(
            to_object,
            ["toolParameterKeyMatchInput"],
            _ToolParameterKeyMatchInput_to_vertex(
                getv(from_object, ["tool_parameter_key_match_input"]), to_object
            ),
        )

    if getv(from_object, ["tool_parameter_kv_match_input"]) is not None:
        setv(
            to_object,
            ["toolParameterKvMatchInput"],
            _ToolParameterKVMatchInput_to_vertex(
                getv(from_object, ["tool_parameter_kv_match_input"]), to_object
            ),
        )

    if getv(from_object, ["autorater_config"]) is not None:
        setv(
            to_object,
            ["autoraterConfig"],
            _AutoraterConfig_to_vertex(
                getv(from_object, ["autorater_config"]), to_object
            ),
        )

    if getv(from_object, ["config"]) is not None:
        setv(to_object, ["config"], getv(from_object, ["config"]))

    return to_object


def _EvaluateInstancesResponse_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}

    if getv(from_object, ["bleuResults"]) is not None:
        setv(to_object, ["bleu_results"], getv(from_object, ["bleuResults"]))

    if getv(from_object, ["cometResult"]) is not None:
        setv(to_object, ["comet_result"], getv(from_object, ["cometResult"]))

    if getv(from_object, ["exactMatchResults"]) is not None:
        setv(
            to_object,
            ["exact_match_results"],
            getv(from_object, ["exactMatchResults"]),
        )

    if getv(from_object, ["metricxResult"]) is not None:
        setv(to_object, ["metricx_result"], getv(from_object, ["metricxResult"]))

    if getv(from_object, ["pairwiseMetricResult"]) is not None:
        setv(
            to_object,
            ["pairwise_metric_result"],
            getv(from_object, ["pairwiseMetricResult"]),
        )

    if getv(from_object, ["pointwiseMetricResult"]) is not None:
        setv(
            to_object,
            ["pointwise_metric_result"],
            getv(from_object, ["pointwiseMetricResult"]),
        )

    if getv(from_object, ["rougeResults"]) is not None:
        setv(to_object, ["rouge_results"], getv(from_object, ["rougeResults"]))

    if getv(from_object, ["rubricBasedInstructionFollowingResult"]) is not None:
        setv(
            to_object,
            ["rubric_based_instruction_following_result"],
            getv(from_object, ["rubricBasedInstructionFollowingResult"]),
        )

    if getv(from_object, ["summarizationVerbosityResult"]) is not None:
        setv(
            to_object,
            ["summarization_verbosity_result"],
            getv(from_object, ["summarizationVerbosityResult"]),
        )

    if getv(from_object, ["toolCallValidResults"]) is not None:
        setv(
            to_object,
            ["tool_call_valid_results"],
            getv(from_object, ["toolCallValidResults"]),
        )

    if getv(from_object, ["toolNameMatchResults"]) is not None:
        setv(
            to_object,
            ["tool_name_match_results"],
            getv(from_object, ["toolNameMatchResults"]),
        )

    if getv(from_object, ["toolParameterKeyMatchResults"]) is not None:
        setv(
            to_object,
            ["tool_parameter_key_match_results"],
            getv(from_object, ["toolParameterKeyMatchResults"]),
        )

    if getv(from_object, ["toolParameterKvMatchResults"]) is not None:
        setv(
            to_object,
            ["tool_parameter_kv_match_results"],
            getv(from_object, ["toolParameterKvMatchResults"]),
        )

    if getv(from_object, ["trajectoryAnyOrderMatchResults"]) is not None:
        setv(
            to_object,
            ["trajectory_any_order_match_results"],
            getv(from_object, ["trajectoryAnyOrderMatchResults"]),
        )

    if getv(from_object, ["trajectoryExactMatchResults"]) is not None:
        setv(
            to_object,
            ["trajectory_exact_match_results"],
            getv(from_object, ["trajectoryExactMatchResults"]),
        )

    if getv(from_object, ["trajectoryInOrderMatchResults"]) is not None:
        setv(
            to_object,
            ["trajectory_in_order_match_results"],
            getv(from_object, ["trajectoryInOrderMatchResults"]),
        )

    if getv(from_object, ["trajectoryPrecisionResults"]) is not None:
        setv(
            to_object,
            ["trajectory_precision_results"],
            getv(from_object, ["trajectoryPrecisionResults"]),
        )

    if getv(from_object, ["trajectoryRecallResults"]) is not None:
        setv(
            to_object,
            ["trajectory_recall_results"],
            getv(from_object, ["trajectoryRecallResults"]),
        )

    if getv(from_object, ["trajectorySingleToolUseResults"]) is not None:
        setv(
            to_object,
            ["trajectory_single_tool_use_results"],
            getv(from_object, ["trajectorySingleToolUseResults"]),
        )

    return to_object


class Evals(_api_module.BaseModule):
    def _evaluate_instances(
        self,
        *,
        bleu_input: Optional[types.BleuInputOrDict] = None,
        exact_match_input: Optional[types.ExactMatchInputOrDict] = None,
        rouge_input: Optional[types.RougeInputOrDict] = None,
        pointwise_metric_input: Optional[types.PointwiseMetricInputOrDict] = None,
        pairwise_metric_input: Optional[types.PairwiseMetricInputOrDict] = None,
        tool_call_valid_input: Optional[types.ToolCallValidInputOrDict] = None,
        tool_name_match_input: Optional[types.ToolNameMatchInputOrDict] = None,
        tool_parameter_key_match_input: Optional[
            types.ToolParameterKeyMatchInputOrDict
        ] = None,
        tool_parameter_kv_match_input: Optional[
            types.ToolParameterKVMatchInputOrDict
        ] = None,
        autorater_config: Optional[types.AutoraterConfigOrDict] = None,
        config: Optional[types.EvaluateInstancesConfigOrDict] = None,
    ) -> types.EvaluateInstancesResponse:
        """Evaluates instances based on a given metric."""

        parameter_model = types._EvaluateInstancesRequestParameters(
            bleu_input=bleu_input,
            exact_match_input=exact_match_input,
            rouge_input=rouge_input,
            pointwise_metric_input=pointwise_metric_input,
            pairwise_metric_input=pairwise_metric_input,
            tool_call_valid_input=tool_call_valid_input,
            tool_name_match_input=tool_name_match_input,
            tool_parameter_key_match_input=tool_parameter_key_match_input,
            tool_parameter_kv_match_input=tool_parameter_kv_match_input,
            autorater_config=autorater_config,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _EvaluateInstancesRequestParameters_to_vertex(
                parameter_model
            )
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = ":evaluateInstances".format_map(request_url_dict)
            else:
                path = ":evaluateInstances"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = self._api_client.request("post", path, request_dict, http_options)

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _EvaluateInstancesResponse_from_vertex(response_dict)

        return_value = types.EvaluateInstancesResponse._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    def run(self) -> types.EvaluateInstancesResponse:
        """Evaluates an instance of a model.

        This should eventually call _evaluate_instances()
        """
        raise NotImplementedError()

    def evaluate_instances(
        self,
        *,
        metric_config: types._EvaluateInstancesRequestParameters,
    ) -> types.EvaluateInstancesResponse:
        """Evaluates an instance of a model."""

        if isinstance(metric_config, types._EvaluateInstancesRequestParameters):
            metric_config = metric_config.model_dump()
        else:
            metric_config = dict(metric_config)

        return self._evaluate_instances(
            **metric_config,
        )

    def run_inference(
        self,
        *,
        model: Union[str, Callable[[Any], Any]],
        src: Union[str, pd.DataFrame],
        config: Optional[types.EvalRunInferenceConfigOrDict] = None,
    ) -> types.EvaluationDataset:
        """Runs inference on a dataset for evaluation.

        Args:
          model: The model to use for inference. - For Google Gemini models,
            provide the model name string (e.g., "gemini-2.5-flash"). - For
            third-party models via LiteLLM, use the format "provider/model_name"
            (e.g., "openai/gpt-4o"). Ensure the necessary API key (e.g.,
            OPENAI_API_KEY) is set as an environment variable. - For custom
            logic, provide a callable function that accepts a prompt and returns
            a response.
          src: The source of the dataset. Can be a string (path to a local file,
            a GCS path, or a BigQuery table) or a Pandas DataFrame.
          config: The optional configuration for the inference run. Must be a
            dict or `types.EvalRunInferenceConfig` type. - dest: The destination
            path for storage of the inference results. - prompt_template: The
            template string to use for constructing prompts. -
            generate_content_config: The config for the Gemini generate content
              call.

        Returns:
          The evaluation dataset.
        """
        if not config:
            config = types.EvalRunInferenceConfig()
        if isinstance(config, dict):
            config = types.EvalRunInferenceConfig.model_validate(config)
        return _evals_common._execute_inference(  # type: ignore[no-any-return]
            api_client=self._api_client,
            model=model,
            src=src,
            dest=config.dest,
            config=config.generate_content_config,
            prompt_template=config.prompt_template,
        )

    def evaluate(
        self,
        *,
        dataset: Union[
            types.EvaluationDatasetOrDict, list[types.EvaluationDatasetOrDict]
        ],
        metrics: list[types.MetricOrDict],
        config: Optional[types.EvaluateMethodConfigOrDict] = None,
    ) -> types.EvaluationResult:
        """Evaluates candidate responses in the provided dataset(s) using the specified metrics.

        Args:
          dataset: The dataset(s) to evaluate. Can be a single
            `types.EvaluationDataset` or a list of `types.EvaluationDataset`.
          metrics: The list of metrics to use for evaluation.
          config: Optional configuration for the evaluation. Can be a dictionary
            or a `types.EvaluateMethodConfig` object. - dataset_schema: Schema
            to use for the dataset. If not specified, the dataset schema will be
            inferred from the dataset automatically. - dest: Destination path
            for storing evaluation results.

        Returns:
          The evaluation result.
        """
        if not config:
            config = types.EvaluateMethodConfig()
        if isinstance(config, dict):
            config = types.EvaluateMethodConfig.model_validate(config)
        if isinstance(dataset, list):
            dataset = [
                types.EvaluationDataset.model_validate(ds_item)
                if isinstance(ds_item, dict)
                else ds_item
                for ds_item in dataset
            ]
        else:
            if isinstance(dataset, dict):
                dataset = types.EvaluationDataset.model_validate(dataset)

        return _evals_common._execute_evaluation(
            api_client=self._api_client,
            dataset=dataset,
            metrics=metrics,
            dataset_schema=config.dataset_schema,
            dest=config.dest,
        )

    def batch_evaluate(
        self,
        *,
        dataset: types.EvaluationDatasetOrDict,
        metrics: list[types.MetricOrDict],
        dest: str,
        config: Optional[types.EvaluateDatasetConfigOrDict] = None,
    ) -> types.EvaluateDatasetOperation:
        """Evaluates a dataset based on a set of given metrics."""

        resolved_metrics = _evals_common._resolve_metrics(metrics, self._api_client)
        output_config = types.OutputConfig(
            gcs_destination=types.GcsDestination(output_uri_prefix=dest)
        )
        parameter_model = types.EvaluateDatasetRequestParameters(
            dataset=dataset,
            metrics=resolved_metrics,
            output_config=output_config,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _evals_utils.BatchEvaluateRequestPreparer.EvaluateDatasetRequestParameters_to_vertex(
                parameter_model
            )
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = ":evaluateDataset".format_map(request_url_dict)
            else:
                path = ":evaluateDataset"

        request_dict = _evals_utils.BatchEvaluateRequestPreparer.prepare_metric_payload(
            request_dict, resolved_metrics
        )

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[genai_types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = self._api_client.request("post", path, request_dict, http_options)

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _evals_utils.BatchEvaluateRequestPreparer.EvaluateDatasetOperation_from_vertex(
                response_dict
            )

        return_value = types.EvaluateDatasetOperation._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )
        self._api_client._verify_response(return_value)

        return return_value


class AsyncEvals(_api_module.BaseModule):
    async def _evaluate_instances(
        self,
        *,
        bleu_input: Optional[types.BleuInputOrDict] = None,
        exact_match_input: Optional[types.ExactMatchInputOrDict] = None,
        rouge_input: Optional[types.RougeInputOrDict] = None,
        pointwise_metric_input: Optional[types.PointwiseMetricInputOrDict] = None,
        pairwise_metric_input: Optional[types.PairwiseMetricInputOrDict] = None,
        tool_call_valid_input: Optional[types.ToolCallValidInputOrDict] = None,
        tool_name_match_input: Optional[types.ToolNameMatchInputOrDict] = None,
        tool_parameter_key_match_input: Optional[
            types.ToolParameterKeyMatchInputOrDict
        ] = None,
        tool_parameter_kv_match_input: Optional[
            types.ToolParameterKVMatchInputOrDict
        ] = None,
        autorater_config: Optional[types.AutoraterConfigOrDict] = None,
        config: Optional[types.EvaluateInstancesConfigOrDict] = None,
    ) -> types.EvaluateInstancesResponse:
        """Evaluates instances based on a given metric."""

        parameter_model = types._EvaluateInstancesRequestParameters(
            bleu_input=bleu_input,
            exact_match_input=exact_match_input,
            rouge_input=rouge_input,
            pointwise_metric_input=pointwise_metric_input,
            pairwise_metric_input=pairwise_metric_input,
            tool_call_valid_input=tool_call_valid_input,
            tool_name_match_input=tool_name_match_input,
            tool_parameter_key_match_input=tool_parameter_key_match_input,
            tool_parameter_kv_match_input=tool_parameter_kv_match_input,
            autorater_config=autorater_config,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _EvaluateInstancesRequestParameters_to_vertex(
                parameter_model
            )
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = ":evaluateInstances".format_map(request_url_dict)
            else:
                path = ":evaluateInstances"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = await self._api_client.async_request(
            "post", path, request_dict, http_options
        )

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _EvaluateInstancesResponse_from_vertex(response_dict)

        return_value = types.EvaluateInstancesResponse._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    async def batch_evaluate(
        self,
        *,
        dataset: types.EvaluationDatasetOrDict,
        metrics: list[types.MetricOrDict],
        dest: str,
        config: Optional[types.EvaluateDatasetConfigOrDict] = None,
    ) -> types.EvaluateDatasetOperation:
        """Evaluates a dataset based on a set of given metrics."""
        resolved_metrics = _evals_common._resolve_metrics(metrics, self._api_client)
        output_config = types.OutputConfig(
            gcs_destination=types.GcsDestination(output_uri_prefix=dest)
        )
        parameter_model = types.EvaluateDatasetRequestParameters(
            dataset=dataset,
            metrics=resolved_metrics,
            output_config=output_config,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _evals_utils.BatchEvaluateRequestPreparer.EvaluateDatasetRequestParameters_to_vertex(
                parameter_model
            )
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = ":evaluateDataset".format_map(request_url_dict)
            else:
                path = ":evaluateDataset"

        request_dict = _evals_utils.BatchEvaluateRequestPreparer.prepare_metric_payload(
            request_dict, resolved_metrics
        )

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[genai_types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = await self._api_client.async_request(
            "post", path, request_dict, http_options
        )

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _evals_utils.BatchEvaluateRequestPreparer.EvaluateDatasetOperation_from_vertex(
                response_dict
            )

        return_value = types.EvaluateDatasetOperation._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )
        self._api_client._verify_response(return_value)

        return return_value
