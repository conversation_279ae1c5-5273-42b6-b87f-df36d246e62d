{"all": ["test_requests.py::RequestsTestCase::test_BASICAUTH_TUPLE_HTTP_200_OK_GET", "test_requests.py::RequestsTestCase::test_HTTP_200_OK_GET_ALTERNATIVE", "test_requests.py::RequestsTestCase::test_HTTP_200_OK_GET_WITH_PARAMS", "test_requests.py::RequestsTestCase::test_HTTP_200_OK_HEAD", "test_requests.py::RequestsTestCase::test_auth_is_retained_for_redirect_on_host", "test_requests.py::RequestsTestCase::test_different_encodings_dont_break_post", "test_requests.py::RequestsTestCase::test_manual_redirect_with_partial_body_read", "test_requests.py::RequestsTestCase::test_mixed_case_scheme_acceptable", "test_requests.py::RequestsTestCase::test_prepared_from_session", "test_requests.py::RequestsTestCase::test_unicode_multipart_post", "test_requests.py::TestTimeout::test_none_timeout", "test_requests.py::TestTimeout::test_encoded_methods", "test_requests.py::RequestsTestCase::test_DIGESTAUTH_QUOTES_QOP_VALUE", "test_requests.py::RequestsTestCase::test_DIGESTAUTH_WRONG_HTTP_401_GET", "test_requests.py::RequestsTestCase::test_DIGEST_AUTH_RETURNS_COOKIE", "test_requests.py::RequestsTestCase::test_DIGEST_AUTH_SETS_SESSION_COOKIES", "test_requests.py::RequestsTestCase::test_DIGEST_STREAM", "test_requests.py::RequestsTestCase::test_HTTP_200_OK_GET_WITH_MIXED_PARAMS", "test_requests.py::RequestsTestCase::test_HTTP_200_OK_PUT", "test_requests.py::RequestsTestCase::test_LocationParseError", "test_requests.py::RequestsTestCase::test_POSTBIN_GET_POST_FILES", "test_requests.py::RequestsTestCase::test_POSTBIN_GET_POST_FILES_WITH_DATA", "test_requests.py::RequestsTestCase::test_auth_is_stripped_on_redirect_off_host", "test_requests.py::RequestsTestCase::test_autoset_header_values_are_native", "test_requests.py::RequestsTestCase::test_basic_auth_str_is_always_native", "test_requests.py::RequestsTestCase::test_basic_building", "test_requests.py::RequestsTestCase::test_basicauth_with_netrc", "test_requests.py::RequestsTestCase::test_can_send_bytes_bytearray_objects_with_files", "test_requests.py::RequestsTestCase::test_can_send_file_object_with_non_string_filename", "test_requests.py::RequestsTestCase::test_can_send_nonstring_objects_with_files", "test_requests.py::RequestsTestCase::test_cannot_send_unprepared_requests", "test_requests.py::RequestsTestCase::test_connection_error_invalid_domain", "test_requests.py::RequestsTestCase::test_connection_error_invalid_port", "test_requests.py::RequestsTestCase::test_cookie_as_dict_items", "test_requests.py::RequestsTestCase::test_cookie_as_dict_keeps_items", "test_requests.py::RequestsTestCase::test_cookie_as_dict_keeps_len", "test_requests.py::RequestsTestCase::test_cookie_as_dict_keys", "test_requests.py::RequestsTestCase::test_cookie_as_dict_values", "test_requests.py::RequestsTestCase::test_cookie_parameters", "test_requests.py::RequestsTestCase::test_cookie_persists_via_api", "test_requests.py::RequestsTestCase::test_cookie_quote_wrapped", "test_requests.py::RequestsTestCase::test_cookie_removed_on_expire", "test_requests.py::RequestsTestCase::test_custom_content_type", "test_requests.py::RequestsTestCase::test_decompress_gzip", "test_requests.py::RequestsTestCase::test_entry_points", "test_requests.py::RequestsTestCase::test_fixes_1329", "test_requests.py::RequestsTestCase::test_generic_cookiejar_works", "test_requests.py::RequestsTestCase::test_get_auth_from_url", "test_requests.py::RequestsTestCase::test_get_auth_from_url_encoded_hashes", "test_requests.py::RequestsTestCase::test_get_auth_from_url_encoded_spaces", "test_requests.py::RequestsTestCase::test_get_auth_from_url_not_encoded_spaces", "test_requests.py::RequestsTestCase::test_get_auth_from_url_percent_chars", "test_requests.py::RequestsTestCase::test_header_keys_are_native", "test_requests.py::RequestsTestCase::test_header_remove_is_case_insensitive", "test_requests.py::RequestsTestCase::test_headers_on_session_with_None_are_not_sent", "test_requests.py::RequestsTestCase::test_history_is_always_a_list", "test_requests.py::RequestsTestCase::test_hook_receives_request_arguments", "test_requests.py::RequestsTestCase::test_http_error", "test_requests.py::RequestsTestCase::test_invalid_url", "test_requests.py::RequestsTestCase::test_json_param_post_content_type_works", "test_requests.py::RequestsTestCase::test_links", "test_requests.py::RequestsTestCase::test_long_authinfo_in_url", "test_requests.py::RequestsTestCase::test_no_content_length", "test_requests.py::RequestsTestCase::test_nonhttp_schemes_dont_check_URLs", "test_requests.py::RequestsTestCase::test_override_content_length", "test_requests.py::RequestsTestCase::test_params_are_added_before_fragment", "test_requests.py::RequestsTestCase::test_params_are_merged_case_sensitive", "test_requests.py::RequestsTestCase::test_path_is_not_double_encoded", "test_requests.py::RequestsTestCase::test_prepare_request_with_bytestring_url", "test_requests.py::RequestsTestCase::test_prepared_request_hook", "test_requests.py::RequestsTestCase::test_pyopenssl_redirect", "test_requests.py::RequestsTestCase::test_redirect_with_wrong_gzipped_header", "test_requests.py::RequestsTestCase::test_request_and_response_are_pickleable", "test_requests.py::RequestsTestCase::test_request_cookie_overrides_session_cookie", "test_requests.py::RequestsTestCase::test_request_cookies_not_persisted", "test_requests.py::RequestsTestCase::test_request_ok_set", "test_requests.py::RequestsTestCase::test_requests_in_history_are_not_overridden", "test_requests.py::RequestsTestCase::test_response_decode_unicode", "test_requests.py::RequestsTestCase::test_response_is_iterable", "test_requests.py::RequestsTestCase::test_response_iter_lines", "test_requests.py::RequestsTestCase::test_session_hooks_are_overriden_by_request_hooks", "test_requests.py::RequestsTestCase::test_session_hooks_are_used_with_no_request_hooks", "test_requests.py::RequestsTestCase::test_session_pickling", "test_requests.py::RequestsTestCase::test_set_cookie_on_301", "test_requests.py::RequestsTestCase::test_status_raising", "test_requests.py::RequestsTestCase::test_time_elapsed_blank", "test_requests.py::RequestsTestCase::test_transport_adapter_ordering", "test_requests.py::RequestsTestCase::test_unconsumed_session_response_closes_connection", "test_requests.py::RequestsTestCase::test_unicode_get", "test_requests.py::RequestsTestCase::test_unicode_header_name", "test_requests.py::RequestsTestCase::test_unicode_method_name", "test_requests.py::RequestsTestCase::test_unicode_multipart_post_fieldnames", "test_requests.py::RequestsTestCase::test_uppercase_scheme_redirect", "test_requests.py::RequestsTestCase::test_urlencoded_get_query_multivalued_param", "test_requests.py::RequestsTestCase::test_user_agent_transfers", "test_requests.py::TestContentEncodingDetection::test_html4_pragma", "test_requests.py::TestContentEncodingDetection::test_html_charset", "test_requests.py::TestContentEncodingDetection::test_none", "test_requests.py::TestContentEncodingDetection::test_precedence", "test_requests.py::TestContentEncodingDetection::test_xhtml_pragma", "test_requests.py::TestContentEncodingDetection::test_xml", "test_requests.py::TestCaseInsensitiveDict::test_contains", "test_requests.py::TestCaseInsensitiveDict::test_copy", "test_requests.py::TestCaseInsensitiveDict::test_delitem", "test_requests.py::TestCaseInsensitiveDict::test_docstring_example", "test_requests.py::TestCaseInsensitiveDict::test_equality", "test_requests.py::TestCaseInsensitiveDict::test_fixes_649", "test_requests.py::TestCaseInsensitiveDict::test_get", "test_requests.py::TestCaseInsensitiveDict::test_getitem", "test_requests.py::TestCaseInsensitiveDict::test_iter", "test_requests.py::TestCaseInsensitiveDict::test_iterable_init", "test_requests.py::TestCaseInsensitiveDict::test_kwargs_init", "test_requests.py::TestCaseInsensitiveDict::test_len", "test_requests.py::TestCaseInsensitiveDict::test_lower_items", "test_requests.py::TestCaseInsensitiveDict::test_mapping_init", "test_requests.py::TestCaseInsensitiveDict::test_preserve_key_case", "test_requests.py::TestCaseInsensitiveDict::test_preserve_last_key_case", "test_requests.py::TestCaseInsensitiveDict::test_repr", "test_requests.py::TestCaseInsensitiveDict::test_setdefault", "test_requests.py::TestCaseInsensitiveDict::test_update", "test_requests.py::TestCaseInsensitiveDict::test_update_retains_unchanged", "test_requests.py::UtilsTestCase::test_address_in_network", "test_requests.py::UtilsTestCase::test_dotted_netmask", "test_requests.py::UtilsTestCase::test_get_auth_from_url", "test_requests.py::UtilsTestCase::test_get_environ_proxies", "test_requests.py::UtilsTestCase::test_get_environ_proxies_ip_ranges", "test_requests.py::UtilsTestCase::test_guess_filename_when_filename_is_an_int", "test_requests.py::UtilsTestCase::test_guess_filename_when_int", "test_requests.py::UtilsTestCase::test_guess_filename_with_file_like_obj", "test_requests.py::UtilsTestCase::test_guess_filename_with_unicode_name", "test_requests.py::UtilsTestCase::test_is_ipv4_address", "test_requests.py::UtilsTestCase::test_is_valid_cidr", "test_requests.py::UtilsTestCase::test_requote_uri_properly_requotes", "test_requests.py::UtilsTestCase::test_requote_uri_with_unquoted_percents", "test_requests.py::UtilsTestCase::test_super_len_io_streams", "test_requests.py::TestMorselToCookieExpires::test_expires_invalid_int", "test_requests.py::TestMorselToCookieExpires::test_expires_invalid_str", "test_requests.py::TestMorselToCookieExpires::test_expires_none", "test_requests.py::TestMorselToCookieExpires::test_expires_valid_str", "test_requests.py::TestMorselToCookieMaxAge::test_max_age_invalid_str", "test_requests.py::TestMorselToCookieMaxAge::test_max_age_valid_int", "test_requests.py::TestTimeout::test_stream_timeout", "test_requests.py::TestTimeout::test_invalid_timeout", "test_requests.py::TestTimeout::test_read_timeout", "test_requests.py::TestRedirects::test_requests_are_updated_each_time", "test_requests.py::test_data_argument_accepts_tuples", "test_requests.py::test_prepared_request_empty_copy", "test_requests.py::test_prepared_request_no_cookies_copy", "test_requests.py::test_prepared_request_complete_copy", "test_requests.py::test_prepare_unicode_url", "test_requests.py::test_urllib3_retries", "test_requests.py::test_vendor_aliases", "test_requests.py::TestTimeout::test_connect_timeout", "test_requests.py::TestTimeout::test_total_timeout_connect"], "FAIL_TO_PASS": {"success": ["test_requests.py::RequestsTestCase::test_BASICAUTH_TUPLE_HTTP_200_OK_GET", "test_requests.py::RequestsTestCase::test_HTTP_200_OK_GET_ALTERNATIVE", "test_requests.py::RequestsTestCase::test_HTTP_200_OK_GET_WITH_PARAMS", "test_requests.py::RequestsTestCase::test_HTTP_200_OK_HEAD", "test_requests.py::RequestsTestCase::test_auth_is_retained_for_redirect_on_host", "test_requests.py::RequestsTestCase::test_different_encodings_dont_break_post", "test_requests.py::RequestsTestCase::test_manual_redirect_with_partial_body_read", "test_requests.py::RequestsTestCase::test_mixed_case_scheme_acceptable", "test_requests.py::RequestsTestCase::test_prepared_from_session", "test_requests.py::RequestsTestCase::test_unicode_multipart_post", "test_requests.py::TestTimeout::test_none_timeout", "test_requests.py::TestTimeout::test_encoded_methods"], "failure": []}, "PASS_TO_PASS": {"success": ["test_requests.py::RequestsTestCase::test_DIGESTAUTH_QUOTES_QOP_VALUE", "test_requests.py::RequestsTestCase::test_DIGESTAUTH_WRONG_HTTP_401_GET", "test_requests.py::RequestsTestCase::test_DIGEST_AUTH_RETURNS_COOKIE", "test_requests.py::RequestsTestCase::test_DIGEST_AUTH_SETS_SESSION_COOKIES", "test_requests.py::RequestsTestCase::test_DIGEST_STREAM", "test_requests.py::RequestsTestCase::test_HTTP_200_OK_GET_WITH_MIXED_PARAMS", "test_requests.py::RequestsTestCase::test_HTTP_200_OK_PUT", "test_requests.py::RequestsTestCase::test_LocationParseError", "test_requests.py::RequestsTestCase::test_POSTBIN_GET_POST_FILES", "test_requests.py::RequestsTestCase::test_POSTBIN_GET_POST_FILES_WITH_DATA", "test_requests.py::RequestsTestCase::test_auth_is_stripped_on_redirect_off_host", "test_requests.py::RequestsTestCase::test_autoset_header_values_are_native", "test_requests.py::RequestsTestCase::test_basic_auth_str_is_always_native", "test_requests.py::RequestsTestCase::test_basic_building", "test_requests.py::RequestsTestCase::test_basicauth_with_netrc", "test_requests.py::RequestsTestCase::test_can_send_bytes_bytearray_objects_with_files", "test_requests.py::RequestsTestCase::test_can_send_file_object_with_non_string_filename", "test_requests.py::RequestsTestCase::test_can_send_nonstring_objects_with_files", "test_requests.py::RequestsTestCase::test_cannot_send_unprepared_requests", "test_requests.py::RequestsTestCase::test_connection_error_invalid_domain", "test_requests.py::RequestsTestCase::test_connection_error_invalid_port", "test_requests.py::RequestsTestCase::test_cookie_as_dict_items", "test_requests.py::RequestsTestCase::test_cookie_as_dict_keeps_items", "test_requests.py::RequestsTestCase::test_cookie_as_dict_keeps_len", "test_requests.py::RequestsTestCase::test_cookie_as_dict_keys", "test_requests.py::RequestsTestCase::test_cookie_as_dict_values", "test_requests.py::RequestsTestCase::test_cookie_parameters", "test_requests.py::RequestsTestCase::test_cookie_persists_via_api", "test_requests.py::RequestsTestCase::test_cookie_quote_wrapped", "test_requests.py::RequestsTestCase::test_cookie_removed_on_expire", "test_requests.py::RequestsTestCase::test_custom_content_type", "test_requests.py::RequestsTestCase::test_decompress_gzip", "test_requests.py::RequestsTestCase::test_entry_points", "test_requests.py::RequestsTestCase::test_fixes_1329", "test_requests.py::RequestsTestCase::test_generic_cookiejar_works", "test_requests.py::RequestsTestCase::test_get_auth_from_url", "test_requests.py::RequestsTestCase::test_get_auth_from_url_encoded_hashes", "test_requests.py::RequestsTestCase::test_get_auth_from_url_encoded_spaces", "test_requests.py::RequestsTestCase::test_get_auth_from_url_not_encoded_spaces", "test_requests.py::RequestsTestCase::test_get_auth_from_url_percent_chars", "test_requests.py::RequestsTestCase::test_header_keys_are_native", "test_requests.py::RequestsTestCase::test_header_remove_is_case_insensitive", "test_requests.py::RequestsTestCase::test_headers_on_session_with_None_are_not_sent", "test_requests.py::RequestsTestCase::test_history_is_always_a_list", "test_requests.py::RequestsTestCase::test_hook_receives_request_arguments", "test_requests.py::RequestsTestCase::test_http_error", "test_requests.py::RequestsTestCase::test_invalid_url", "test_requests.py::RequestsTestCase::test_json_param_post_content_type_works", "test_requests.py::RequestsTestCase::test_links", "test_requests.py::RequestsTestCase::test_long_authinfo_in_url", "test_requests.py::RequestsTestCase::test_no_content_length", "test_requests.py::RequestsTestCase::test_nonhttp_schemes_dont_check_URLs", "test_requests.py::RequestsTestCase::test_override_content_length", "test_requests.py::RequestsTestCase::test_params_are_added_before_fragment", "test_requests.py::RequestsTestCase::test_params_are_merged_case_sensitive", "test_requests.py::RequestsTestCase::test_path_is_not_double_encoded", "test_requests.py::RequestsTestCase::test_prepare_request_with_bytestring_url", "test_requests.py::RequestsTestCase::test_prepared_request_hook", "test_requests.py::RequestsTestCase::test_pyopenssl_redirect", "test_requests.py::RequestsTestCase::test_redirect_with_wrong_gzipped_header", "test_requests.py::RequestsTestCase::test_request_and_response_are_pickleable", "test_requests.py::RequestsTestCase::test_request_cookie_overrides_session_cookie", "test_requests.py::RequestsTestCase::test_request_cookies_not_persisted", "test_requests.py::RequestsTestCase::test_request_ok_set", "test_requests.py::RequestsTestCase::test_requests_in_history_are_not_overridden", "test_requests.py::RequestsTestCase::test_response_decode_unicode", "test_requests.py::RequestsTestCase::test_response_is_iterable", "test_requests.py::RequestsTestCase::test_response_iter_lines", "test_requests.py::RequestsTestCase::test_session_hooks_are_overriden_by_request_hooks", "test_requests.py::RequestsTestCase::test_session_hooks_are_used_with_no_request_hooks", "test_requests.py::RequestsTestCase::test_session_pickling", "test_requests.py::RequestsTestCase::test_set_cookie_on_301", "test_requests.py::RequestsTestCase::test_status_raising", "test_requests.py::RequestsTestCase::test_time_elapsed_blank", "test_requests.py::RequestsTestCase::test_transport_adapter_ordering", "test_requests.py::RequestsTestCase::test_unconsumed_session_response_closes_connection", "test_requests.py::RequestsTestCase::test_unicode_get", "test_requests.py::RequestsTestCase::test_unicode_header_name", "test_requests.py::RequestsTestCase::test_unicode_method_name", "test_requests.py::RequestsTestCase::test_unicode_multipart_post_fieldnames", "test_requests.py::RequestsTestCase::test_uppercase_scheme_redirect", "test_requests.py::RequestsTestCase::test_urlencoded_get_query_multivalued_param", "test_requests.py::RequestsTestCase::test_user_agent_transfers", "test_requests.py::TestContentEncodingDetection::test_html4_pragma", "test_requests.py::TestContentEncodingDetection::test_html_charset", "test_requests.py::TestContentEncodingDetection::test_none", "test_requests.py::TestContentEncodingDetection::test_precedence", "test_requests.py::TestContentEncodingDetection::test_xhtml_pragma", "test_requests.py::TestContentEncodingDetection::test_xml", "test_requests.py::TestCaseInsensitiveDict::test_contains", "test_requests.py::TestCaseInsensitiveDict::test_copy", "test_requests.py::TestCaseInsensitiveDict::test_delitem", "test_requests.py::TestCaseInsensitiveDict::test_docstring_example", "test_requests.py::TestCaseInsensitiveDict::test_equality", "test_requests.py::TestCaseInsensitiveDict::test_fixes_649", "test_requests.py::TestCaseInsensitiveDict::test_get", "test_requests.py::TestCaseInsensitiveDict::test_getitem", "test_requests.py::TestCaseInsensitiveDict::test_iter", "test_requests.py::TestCaseInsensitiveDict::test_iterable_init", "test_requests.py::TestCaseInsensitiveDict::test_kwargs_init", "test_requests.py::TestCaseInsensitiveDict::test_len", "test_requests.py::TestCaseInsensitiveDict::test_lower_items", "test_requests.py::TestCaseInsensitiveDict::test_mapping_init", "test_requests.py::TestCaseInsensitiveDict::test_preserve_key_case", "test_requests.py::TestCaseInsensitiveDict::test_preserve_last_key_case", "test_requests.py::TestCaseInsensitiveDict::test_repr", "test_requests.py::TestCaseInsensitiveDict::test_setdefault", "test_requests.py::TestCaseInsensitiveDict::test_update", "test_requests.py::TestCaseInsensitiveDict::test_update_retains_unchanged", "test_requests.py::UtilsTestCase::test_address_in_network", "test_requests.py::UtilsTestCase::test_dotted_netmask", "test_requests.py::UtilsTestCase::test_get_auth_from_url", "test_requests.py::UtilsTestCase::test_get_environ_proxies", "test_requests.py::UtilsTestCase::test_get_environ_proxies_ip_ranges", "test_requests.py::UtilsTestCase::test_guess_filename_when_filename_is_an_int", "test_requests.py::UtilsTestCase::test_guess_filename_when_int", "test_requests.py::UtilsTestCase::test_guess_filename_with_file_like_obj", "test_requests.py::UtilsTestCase::test_guess_filename_with_unicode_name", "test_requests.py::UtilsTestCase::test_is_ipv4_address", "test_requests.py::UtilsTestCase::test_is_valid_cidr", "test_requests.py::UtilsTestCase::test_requote_uri_properly_requotes", "test_requests.py::UtilsTestCase::test_requote_uri_with_unquoted_percents", "test_requests.py::UtilsTestCase::test_super_len_io_streams", "test_requests.py::TestMorselToCookieExpires::test_expires_invalid_int", "test_requests.py::TestMorselToCookieExpires::test_expires_invalid_str", "test_requests.py::TestMorselToCookieExpires::test_expires_none", "test_requests.py::TestMorselToCookieExpires::test_expires_valid_str", "test_requests.py::TestMorselToCookieMaxAge::test_max_age_invalid_str", "test_requests.py::TestMorselToCookieMaxAge::test_max_age_valid_int", "test_requests.py::TestTimeout::test_stream_timeout", "test_requests.py::TestTimeout::test_invalid_timeout", "test_requests.py::TestTimeout::test_read_timeout", "test_requests.py::TestRedirects::test_requests_are_updated_each_time", "test_requests.py::test_data_argument_accepts_tuples", "test_requests.py::test_prepared_request_empty_copy", "test_requests.py::test_prepared_request_no_cookies_copy", "test_requests.py::test_prepared_request_complete_copy", "test_requests.py::test_prepare_unicode_url", "test_requests.py::test_urllib3_retries", "test_requests.py::test_vendor_aliases"], "failure": ["test_requests.py::TestTimeout::test_connect_timeout", "test_requests.py::TestTimeout::test_total_timeout_connect"]}, "FAIL_TO_FAIL": {"success": [], "failure": []}, "PASS_TO_FAIL": {"success": [], "failure": []}}