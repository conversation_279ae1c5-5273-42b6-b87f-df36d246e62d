accelerate==1.5.2
aiofiles==23.2.1
aiohappyeyeballs==2.6.1
aiohttp==3.11.14
aioitertools==0.12.0
aiosignal==1.3.2
aiosqlite==0.21.0
alembic==1.15.1
annotated-types==0.7.0
anthropic==0.49.0
anyio==4.9.0
arize-phoenix==8.16.0
arize-phoenix-client==1.1.0
arize-phoenix-evals==0.20.3
arize-phoenix-otel==0.8.0
asttokens==3.0.0
async-timeout==5.0.1
attrs==25.3.0
Authlib==1.5.1
beautifulsoup4==4.13.3
bio==1.7.1
biopython==1.85
biothings_client==0.4.1
blinker==1.9.0
cachetools==5.5.2
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
chess==1.11.2
click==8.1.8
cobble==0.1.4
colorama==0.4.6
cryptography==44.0.2
datasets==3.4.1
decorator==5.2.1
defusedxml==0.7.1
Deprecated==1.2.18
dill==0.3.8
distro==1.9.0
duckduckgo_search==7.5.2
e2b==1.2.0
e2b-code-interpreter==1.1.1
et_xmlfile==2.0.0
exceptiongroup==1.2.2
executing==2.2.0
fastapi==0.115.11
ffmpy==0.5.0
filelock==3.18.0
Flask==3.1.0
flask-cors==5.0.1
frozenlist==1.5.0
fsspec==2024.12.0
google_search_results==2.4.2
googleapis-common-protos==1.69.2
gprofiler-official==1.0.0
gradio==5.22.0
gradio_client==1.8.0
graphql-core==3.2.6
greenlet==3.1.1
groovy==0.1.2
grpc-interceptor==0.15.4
grpcio==1.71.0
h11==0.14.0
httpcore==1.0.7
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.29.3
idna==3.10
importlib_metadata==8.6.1
iniconfig==2.1.0
ipython==8.34.0
itsdangerous==2.2.0
jedi==0.19.2
Jinja2==3.1.6
jiter==0.9.0
joblib==1.4.2
jsonref==1.1.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
litellm==1.63.12
lxml==5.3.1
Mako==1.3.9
mammoth==1.9.0
markdown-it-py==3.0.0
markdownify==1.1.0
MarkupSafe==3.0.2
matplotlib-inline==0.1.7
mcp==1.4.1
mcpadapt==0.0.15
mdurl==0.1.2
mpmath==1.3.0
multidict==6.2.0
multiprocess==0.70.16
mygene==3.2.2
networkx==3.4.2
numexpr==2.10.2
numpy==2.2.4
openai==1.74.0
openinference-instrumentation==0.1.24
openinference-instrumentation-smolagents==0.1.7
openinference-semantic-conventions==0.1.15
openpyxl==3.1.5
opentelemetry-api==1.31.1
opentelemetry-exporter-otlp==1.31.1
opentelemetry-exporter-otlp-proto-common==1.31.1
opentelemetry-exporter-otlp-proto-grpc==1.31.1
opentelemetry-exporter-otlp-proto-http==1.31.1
opentelemetry-instrumentation==0.52b1
opentelemetry-proto==1.31.1
opentelemetry-sdk==1.31.1
opentelemetry-semantic-conventions==0.52b1
orjson==3.10.15
packaging==24.2
pandas==2.2.3
parso==0.8.4
pathvalidate==3.2.3
pdfminer==20191125
pdfminer.six==20240706
pillow==11.1.0
platformdirs==4.3.7
pluggy==1.5.0
pooch==1.8.2
primp==0.14.0
prompt_toolkit==3.0.50
propcache==0.3.0
protobuf==5.29.4
psutil==7.0.0
PubChemPy==1.0.4
pure_eval==0.2.3
puremagic==1.28
pyarrow==19.0.1
pycparser==2.22
pycryptodome==3.22.0
pydantic==2.10.6
pydantic-settings==2.8.1
pydantic_core==2.27.2
pydub==0.25.1
Pygments==2.19.1
PyPDF2==3.1.0
pytest==8.3.5
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-multipart==0.0.20
python-pptx==1.0.2
pytz==2025.1
PyYAML==6.0.2
rank-bm25==0.2.2
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
rich==13.9.4
rpds-py==0.23.1
ruff==0.11.1
safehttpx==0.1.6
safetensors==0.5.3
scikit-learn==1.6.1
scipy==1.15.2
semantic-version==2.10.0
serpapi==0.1.5
shellingham==1.5.4
six==1.17.0
sniffio==1.3.1
sounddevice==0.5.1
soundfile==0.13.1
soupsieve==2.6
SpeechRecognition==3.14.1
SQLAlchemy==2.0.39
sqlean.py==3.47.0
sse-starlette==2.2.1
stack-data==0.6.3
starlette==0.46.1
strawberry-graphql==0.262.5
sympy==1.13.1
threadpoolctl==3.6.0
tiktoken==0.9.0
tokenizers==0.21.1
tomli==2.2.1
tomlkit==0.13.2
torch==2.6.0
torchvision==0.21.0
tqdm==4.67.1
traitlets==5.14.3
transformers==4.49.0
typer==0.15.2
typing_extensions==4.12.2
tzdata==2025.1
urllib3==2.3.0
uvicorn==0.34.0
wcwidth==0.2.13
websockets==15.0.1
Werkzeug==3.1.3
wrapt==1.17.2
xlrd==2.0.1
XlsxWriter==3.2.2
xxhash==3.5.0
yarl==1.18.3
youtube-transcript-api==1.0.2
zipp==3.21.0
yfinance==0.2.55
langchain==0.3.23
mmh3==5.1.0
opencv-python==*********
apify_client==1.10.0
json_repair==0.44.1
sentence_transformers==4.1.0