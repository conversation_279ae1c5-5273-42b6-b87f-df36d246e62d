# プロンプトのベストプラクティス

OpenHands AIソフトウェア開発者と連携する際、明確で効果的なプロンプトを提供することが重要です。このガイドでは、最も正確で有用な応答を引き出すためのプロンプト作成のベストプラクティスについて説明します。

## 良いプロンプトの特徴

良いプロンプトは以下のような特徴があります：

- **具体的**：どのような機能を追加すべきか、またはどのようなエラーを修正する必要があるかを正確に説明します。
- **場所を特定**：可能であれば、コードベース内のどの場所を修正すべきかを説明します。
- **適切な範囲**：1つの機能の大きさであるべきで、通常は100行のコードを超えないようにします。

## 例

### 良いプロンプトの例

- "`utils/math_operations.py`に、数値のリストを入力として受け取り、その平均を返す関数`calculate_average`を追加してください。"
- "`frontend/src/components/UserProfile.tsx`の42行目で発生しているTypeErrorを修正してください。このエラーは、undefinedのプロパティにアクセスしようとしていることを示唆しています。"
- "登録フォームのメールフィールドに入力検証を実装してください。`frontend/src/components/RegistrationForm.tsx`を更新し、送信前にメールが有効な形式であるかどうかを確認するようにしてください。"

### 悪いプロンプトの例

- "コードをもっと良くしてください。"（曖昧すぎる、具体性に欠ける）
- "バックエンド全体を別のフレームワークを使用するように書き換えてください。"（適切な範囲ではない）
- "ユーザー認証のどこかにバグがあります。見つけて修正できますか？"（具体性とロケーション情報に欠ける）

## 効果的なプロンプトのためのヒント

- 望ましい結果や解決すべき問題について、できるだけ具体的に説明してください。
- 関連するファイルパスや行番号など、コンテキストを提供してください。
- 大きなタスクは、より小さく管理しやすいプロンプトに分割してください。
- 関連するエラーメッセージやログを含めてください。
- コンテキストから明らかでない場合は、プログラミング言語やフレームワークを指定してください。

プロンプトが正確で情報量が多いほど、AIはOpenHandsソフトウェアの開発や修正においてより良いサポートができることを覚えておいてください。

役立つプロンプトの他の例については、[OpenHands入門](../getting-started)を参照してください。
