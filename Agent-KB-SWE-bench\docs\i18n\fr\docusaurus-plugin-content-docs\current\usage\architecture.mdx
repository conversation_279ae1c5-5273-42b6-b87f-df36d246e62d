---
sidebar_position: 4
---

# 🏛️ Aperçu de l'Architecture Système

Voici un aperçu de haut niveau de l'architecture du système. Le système est divisé en deux composants principaux : le frontend et le backend. Le frontend est responsable de la gestion des interactions avec l'utilisateur et de l'affichage des résultats. Le backend est responsable de la gestion de la logique métier et de l'exécution des agents.

![system_architecture.svg](/img/system_architecture.svg)

Cet aperçu est simplifié pour montrer les principaux composants et leurs interactions. Pour une vue plus détaillée de l'architecture du backend, consultez la section [Architecture du Backend](#backend-architecture-fr).

# Architecture du Backend {#backend-architecture-fr}

_**Avertissement**: L'architecture du backend est en cours de développement et est sujette à modifications. Le schéma suivant montre l'architecture actuelle du backend basée sur le commit indiqué dans le pied de page du schéma._

![backend_architecture.svg](/img/backend_architecture.svg)

<details>
  <summary>Mise à jour de ce Schéma</summary>
  <div>
    La génération du schéma d'architecture du backend est partiellement automatisée.
    Le schéma est généré à partir des annotations de type dans le code en utilisant l'outil py2puml.
    Le schéma est ensuite revu manuellement, ajusté et exporté en PNG et SVG.

    ## Prérequis

    - Un environnement Python dans lequel openhands est exécutable
    (selon les instructions du fichier README.md à la racine du dépôt)
    - [py2puml](https://github.com/lucsorel/py2puml) installé

## Étapes

1.  Générez automatiquement le schéma en exécutant la commande suivante depuis la racine du dépôt :
    `py2puml openhands openhands > docs/architecture/backend_architecture.puml`

2.  Ouvrez le fichier généré dans un éditeur PlantUML, par exemple Visual Studio Code avec l'extension PlantUML ou [PlantText](https://www.planttext.com/)

3.  Révisez le PUML généré et apportez toutes les modifications nécessaires au schéma (ajoutez les parties manquantes, corrigez les erreurs, améliorez l'agencement).
    _py2puml crée le schéma à partir des annotations de type dans le code, donc les annotations de type manquantes ou incorrectes peuvent entraîner un schéma incomplet ou incorrect._

4.  Examinez la différence entre le nouveau schéma et le précédent et vérifiez manuellement si les modifications sont correctes.
    _Assurez-vous de ne pas supprimer les parties ajoutées manuellement au schéma par le passé et qui sont toujours pertinentes._

5.  Ajoutez le hash du commit qui a été utilisé pour générer le schéma dans le pied de page du schéma.

6.  Exporte le schéma sous forme de fichiers PNG et SVG et remplacez les schémas existants dans le répertoire `docs/architecture`. Cela peut être fait avec (par exemple [PlantText](https://www.planttext.com/))

  </div>
</details>
