# GUI 模式

## 简介

OpenHands 提供了一个用户友好的图形用户界面（GUI）模式，用于与 AI 助手交互。这种模式提供了一种直观的方式来设置环境、管理设置和与 AI 通信。

## 安装和设置

1. 按照[安装](../installation)指南中的说明安装 OpenHands。

2. 运行命令后，通过 [http://localhost:3000](http://localhost:3000) 访问 OpenHands。

## 与 GUI 交互

### 初始设置

1. 首次启动时，您将看到一个设置模态框。
2. 从下拉菜单中选择 `LLM Provider` 和 `LLM Model`。
3. 输入所选提供商对应的 `API Key`。
4. 点击"保存"应用设置。

### GitHub Token 设置

如果可用，OpenHands 会自动将 `GITHUB_TOKEN` 导出到 shell 环境中。这可以通过两种方式实现：

1. **本地（OSS）**：用户直接输入他们的 GitHub token
2. **在线（SaaS）**：通过 GitHub OAuth 身份验证获取 token

#### 设置本地 GitHub Token

1. **生成个人访问令牌（PAT）**：
   - 转到 GitHub 设置 > 开发者设置 > 个人访问令牌 > 令牌（经典）
   - 点击"生成新令牌（经典）"
   - 所需范围：
     - `repo`（完全控制私有仓库）
     - `workflow`（更新 GitHub Action 工作流）
     - `read:org`（读取组织数据）

2. **在 OpenHands 中输入令牌**：
   - 点击右上角的设置按钮（齿轮图标）
   - 导航到"GitHub"部分
   - 将令牌粘贴到"GitHub Token"字段中
   - 点击"保存"应用更改

#### 组织令牌策略

如果您使用组织仓库，可能需要额外的设置：

1. **检查组织要求**：
   - 组织管理员可能会强制执行特定的令牌策略
   - 某些组织要求使用启用 SSO 的令牌
   - 查看您组织的[令牌策略设置](https://docs.github.com/en/organizations/managing-programmatic-access-to-your-organization/setting-a-personal-access-token-policy-for-your-organization)

2. **验证组织访问权限**：
   - 转到 GitHub 上的令牌设置
   - 在"组织访问"下查找组织
   - 如果需要，点击组织旁边的"启用 SSO"
   - 完成 SSO 授权过程

#### OAuth 身份验证（在线模式）

在在线模式下使用 OpenHands 时，GitHub OAuth 流程：

1. 请求以下权限：
   - 仓库访问（读/写）
   - 工作流管理
   - 组织读取访问

2. 身份验证步骤：
   - 出现提示时，点击"使用 GitHub 登录"
   - 查看请求的权限
   - 授权 OpenHands 访问您的 GitHub 帐户
   - 如果使用组织，在出现提示时授权组织访问

#### 故障排除

常见问题和解决方案：

1. **令牌无法识别**：
   - 确保令牌已正确保存在设置中
   - 检查令牌是否已过期
   - 验证令牌是否具有所需的范围
   - 尝试重新生成令牌

2. **组织访问被拒绝**：
   - 检查是否需要但未启用 SSO
   - 验证组织成员资格
   - 如果令牌策略阻止访问，请联系组织管理员

3. **验证令牌是否有效**：
   - 如果令牌有效，应用程序将显示绿色复选标记
   - 尝试访问仓库以确认权限
   - 检查浏览器控制台中是否有任何错误消息
   - 如果可用，使用设置中的"测试连接"按钮

### 高级设置

1. 切换`高级选项`以访问其他设置。
2. 如果列表中没有所需的模型，使用`自定义模型`文本框手动输入模型。
3. 如果您的 LLM 提供商需要，请指定`基本 URL`。

### 主界面

主界面由几个关键组件组成：

1. **聊天窗口**：中央区域，您可以在其中查看与 AI 助手的对话历史记录。
2. **输入框**：位于屏幕底部，用于输入您要发送给 AI 的消息或命令。
3. **发送按钮**：点击此按钮将消息发送给 AI。
4. **设置按钮**：打开设置模态框的齿轮图标，允许您随时调整配置。
5. **工作区面板**：显示工作区中的文件和文件夹，允许您导航和查看文件，或查看代理的过去命令或网页浏览历史记录。

### 与 AI 交互

1. 在输入框中输入您的问题、请求或任务描述。
2. 点击发送按钮或按 Enter 键提交消息。
3. AI 将处理您的输入并在聊天窗口中提供响应。
4. 您可以通过询问后续问题或提供额外信息来继续对话。

## 有效使用的提示

1. 在请求中要具体，以获得最准确和最有帮助的响应，如[提示最佳实践](../prompting/prompting-best-practices)中所述。
2. 使用工作区面板探索项目结构。
3. 使用[LLMs 部分](usage/llms/llms.md)中描述的推荐模型之一。

请记住，OpenHands 的 GUI 模式旨在使您与 AI 助手的交互尽可能流畅和直观。不要犹豫探索其功能以最大限度地提高您的工作效率。
