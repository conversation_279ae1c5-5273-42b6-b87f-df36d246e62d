Here is the translation to Brazilian Portuguese:

# GitHub Cloud Resolver

O GitHub Cloud Resolver automatiza correções de código e fornece assistência inteligente para seus repositórios.

## Configuração

O Resolvedor do GitHub na Nuvem está disponível automaticamente quando você
[concede acesso ao repositório do OpenHands Cloud](./openhands-cloud.md#adding-repository-access).

## Uso

Após conceder acesso ao repositório do OpenHands Cloud, você pode usar o Resolvedor do GitHub na Nuvem nos problemas e pull requests
do repositório.

### Issues

No seu repositório, rotule um issue com `openhands`. O OpenHands irá:

1. Comentar no issue para informar que está trabalhando nele.
   - Você pode clicar no link para acompanhar o progresso no OpenHands Cloud.
2. Abrir um pull request se determinar que o issue foi resolvido com sucesso.
3. Comentar no issue com um resumo das tarefas realizadas e um link para o pull request.

### Pull Requests

Para fazer o OpenHands trabalhar em pull requests, use `@openhands` em comentários de nível superior ou inline para: - Fazer perguntas - Solicitar atualizações - Obter explicações de código

O OpenHands irá:

1. Comentar no PR para informar que está trabalhando nele.
2. Realizar a tarefa.
