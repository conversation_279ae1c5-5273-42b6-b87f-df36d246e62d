# ランタイム設定

ランタイムとは、OpenHands エージェントがファイルを編集したりコマンドを実行したりできる環境のことです。

デフォルトでは、OpenHands はローカルコンピュータ上で動作する Docker ベースのランタイムを使用します。
つまり、使用している LLM の料金のみを支払えばよく、コードが LLM に送信されるだけです。

また、通常はサードパーティが管理する「リモート」ランタイムもサポートしています。
特に多数の OpenHands 会話を並行して実行する場合（例えば評価を行う場合など）、セットアップがより簡単でスケーラブルになります。

さらに、Docker を使用せずに直接マシン上で実行される「ローカル」ランタイムも提供しており、
CI パイプラインのような制御された環境で役立ちます。

## 利用可能なランタイム

OpenHands は以下のようなさまざまなランタイム環境をサポートしています。

- [Docker ランタイム](./runtimes/docker.md) - 分離のために Docker コンテナを使用するデフォルトのランタイム（ほとんどのユーザーにお勧め）
- [OpenHands リモートランタイム](./runtimes/remote.md) - 並列実行用のクラウドベースのランタイム（ベータ版）
- [Modal ランタイム](./runtimes/modal.md) - パートナーである Modal 社が提供するランタイム
- [Daytona ランタイム](./runtimes/daytona.md) - Daytona 社が提供するランタイム
- [ローカルランタイム](./runtimes/local.md) - Docker を使用せずにローカルマシン上で直接実行
