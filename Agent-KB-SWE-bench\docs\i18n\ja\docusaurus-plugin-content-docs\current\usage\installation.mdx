# OpenHandsの実行

## システム要件

* Docker バージョン26.0.0+またはDocker Desktop 4.31.0+
* LinuxまたはMac OSを使用する必要があります
  * Windowsを使用している場合は、[WSL](https://learn.microsoft.com/en-us/windows/wsl/install)を使用する必要があります

## アプリケーションの起動

OpenHandsを実行する最も簡単な方法は、Dockerを使用することです。

```bash
docker pull docker.all-hands.dev/all-hands-ai/runtime:0.29-nikolaik

docker run -it --rm --pull=always \
    -e SANDBOX_RUNTIME_CONTAINER_IMAGE=docker.all-hands.dev/all-hands-ai/runtime:0.29-nikolaik \
    -e LOG_ALL_EVENTS=true \
    -v /var/run/docker.sock:/var/run/docker.sock \
    -p 3000:3000 \
    --add-host host.docker.internal:host-gateway \
    --name openhands-app \
    docker.all-hands.dev/all-hands-ai/openhands:0.29
```

また、[ヘッドレススクリプトモード](https://docs.all-hands.dev/modules/usage/how-to/headless-mode)、[対話型CLI](https://docs.all-hands.dev/modules/usage/how-to/cli-mode)、または[OpenHands GitHub Action](https://docs.all-hands.dev/modules/usage/how-to/github-action)としても実行できます。

## 設定

上記のコマンドを実行すると、OpenHandsは[http://localhost:3000](http://localhost:3000)で動作します。

OpenHandsを起動すると、設定モーダルウィンドウが表示されます。`LLMプロバイダー`と`LLMモデル`を選択し、対応する`APIキー`を入力する**必要があります**。
これらの設定は、UIの`設定`ボタン（歯車アイコン）を選択することでいつでも変更できます。

必要な`LLMモデル`がリストに存在しない場合は、`詳細オプション`を有効にして、
`カスタムモデル`テキストボックスに適切なプレフィックスを付けて手動で入力できます。
`詳細オプション`では、必要に応じて`ベースURL`も指定できます。

<div style={{ display: 'flex', justifyContent: 'center', gap: '20px' }}>
  <img src="/img/settings-screenshot.png" alt="settings-modal" width="340" />
  <img src="/img/settings-advanced.png" alt="settings-modal" width="335" />
</div>

## バージョン

上記のコマンドは、OpenHandsの最新の安定版を取得します。他のオプションもあります：
- 特定のバージョンを使用する場合は、`docker.all-hands.dev/all-hands-ai/openhands:$VERSION`を使用し、$VERSIONをバージョン番号に置き換えてください。
- semverを使用しており、メジャー、マイナー、パッチバージョンのタグを公開しています。したがって、`0.9`は最新の`0.9.x`バージョンを、`0`は最新の`0.x.x`バージョンを指します。
- 最新の開発バージョンを使用する場合は、`docker.all-hands.dev/all-hands-ai/openhands:main`を使用できます。このバージョンは不安定で、テストまたは開発目的でのみ推奨されます。

安定性要件と必要な機能に応じて、最適なタグを選択できます。

開発ワークフローについては、[Development.md](https://github.com/All-Hands-AI/OpenHands/blob/main/Development.md)を参照してください。

問題がありますか？[トラブルシューティングガイド](https://docs.all-hands.dev/modules/usage/troubleshooting)をご確認ください。
